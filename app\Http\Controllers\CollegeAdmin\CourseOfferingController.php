<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\College;
use App\curriculum;
use App\CtrSection;
use App\offerings_infos_table;
use App\Services\ProgramService;

class CourseOfferingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Display the course offering index page
     */
    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        // Get programs directly from the Program model
        $programs = \App\Program::join('colleges', 'programs.college_id', '=', 'colleges.id')
            ->where('colleges.college_code', $collegeCode)
            ->select('programs.*')
            ->get();

        // Log for debugging
        \Log::info('College: ' . $collegeCode);
        \Log::info('College ID: ' . $college->id);
        \Log::info('Programs: ' . json_encode($programs));

        return view('collegeadmin.course_offering.index', compact('college', 'programs'));
    }

    /**
     * Display course offerings for a specific program
     */
    public function viewProgramOfferings($programCode)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        // Verify the program belongs to this college
        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Find the program
        $programService = new ProgramService();
        $program = $programService->getProgramByCode($programCode);

        if (!$program) {
            Session::flash('error', "Program with code {$programCode} not found");
            return redirect()->route('collegeadmin.course_offering.index');
        }

        // Get curriculum years for this program
        $curriculumYears = curriculum::distinct()
            ->where('program_code', $programCode)
            ->where('college_code', $collegeCode)
            ->pluck('curriculum_year')
            ->toArray();

        // Get active sections for this program
        $sections = CtrSection::where('program_code', $programCode)
            ->where('is_active', 1)
            ->get();

        return view('collegeadmin.course_offering.program_offerings', compact('program', 'curriculumYears', 'sections', 'college'));
    }

    /**
     * Display offerings for a specific section
     */
    public function viewSectionOfferings($sectionId)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get section information
        $section = CtrSection::findOrFail($sectionId);

        // Verify the section belongs to a program in this college
        $programCode = $section->program_code;
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Get offered curricula with curriculum and subject data
        $offerings = offerings_infos_table::where('section_name', $section->section_name)
            ->with('curriculum.subject')
            ->get();

        // Get available curricula that can be added
        $availableCurricula = $this->getAvailableCurricula($section, $collegeCode);

        // Get semester options
        $semesterOptions = offerings_infos_table::getSemesterOptions();

        return view('collegeadmin.course_offering.section_offerings', compact('section', 'offerings', 'availableCurricula', 'college', 'semesterOptions'));
    }

    /**
     * Add all available curriculum subjects to a section
     */
    public function addAllCurricula($sectionId)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get section information
        $section = CtrSection::findOrFail($sectionId);

        // Verify the section belongs to a program in this college
        $programCode = $section->program_code;
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        $availableCurricula = $this->getAvailableCurricula($section, $collegeCode);
        $addedCount = 0;

        foreach ($availableCurricula as $curriculum) {
            $offering = new offerings_infos_table();
            $offering->curriculum_id = $curriculum->id;
            $offering->section_name = $section->section_name;
            $offering->level = $curriculum->level;
            $offering->semester = $curriculum->period; // Use the period from curriculum as semester
            $offering->save();

            $addedCount++;
        }

        Session::flash('success', "Added {$addedCount} subjects to {$section->section_name}");
        return redirect()->route('collegeadmin.course_offering.section', ['section_id' => $sectionId]);
    }

    /**
     * Add selected curriculum subjects to a section
     */
    public function addSelectedCurricula(Request $request, $sectionId)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get section information
        $section = CtrSection::findOrFail($sectionId);

        // Verify the section belongs to a program in this college
        $programCode = $section->program_code;
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Validate the request
        $request->validate([
            'curriculum_ids' => 'required|array',
            'curriculum_ids.*' => 'required|integer|exists:curricula,id'
        ]);

        $addedCount = 0;
        foreach ($request->curriculum_ids as $curriculumId) {
            // Check if already offered
            $exists = offerings_infos_table::where('curriculum_id', $curriculumId)
                ->where('section_name', $section->section_name)
                ->exists();

            if (!$exists) {
                $curriculum = curriculum::find($curriculumId);

                $offering = new offerings_infos_table();
                $offering->curriculum_id = $curriculumId;
                $offering->section_name = $section->section_name;
                $offering->level = $curriculum->level;
                $offering->semester = $curriculum->period; // Use the period from curriculum as semester
                $offering->save();

                $addedCount++;
            }
        }

        Session::flash('success', "Added {$addedCount} subjects to {$section->section_name}");
        return redirect()->route('collegeadmin.course_offering.section', ['section_id' => $sectionId]);
    }

    /**
     * Remove a curriculum from a section's offerings
     */
    public function removeCurriculum($sectionId, $offeringId)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get section information
        $section = CtrSection::findOrFail($sectionId);

        // Verify the section belongs to a program in this college
        $programCode = $section->program_code;
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Find and delete the offering
        $offering = offerings_infos_table::findOrFail($offeringId);

        // Verify the offering belongs to this section
        if ($offering->section_name !== $section->section_name) {
            abort(403, 'Unauthorized action.');
        }

        $offering->delete();

        Session::flash('success', "Subject removed from {$section->section_name}");
        return redirect()->route('collegeadmin.course_offering.section', ['section_id' => $sectionId]);
    }

    /**
     * Update the semester for a course offering
     */
    public function updateSemester(Request $request, $sectionId, $offeringId)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get section information
        $section = CtrSection::findOrFail($sectionId);

        // Verify the section belongs to a program in this college
        $programCode = $section->program_code;
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Find the offering
        $offering = offerings_infos_table::findOrFail($offeringId);

        // Verify the offering belongs to this section
        if ($offering->section_name !== $section->section_name) {
            abort(403, 'Unauthorized action.');
        }

        // Validate the request
        $request->validate([
            'semester' => 'required|in:First Semester,Second Semester,Mid-year'
        ]);

        // Get the curriculum associated with this offering
        $curriculum = curriculum::find($offering->curriculum_id);

        if (!$curriculum) {
            Session::flash('error', "Curriculum not found for this offering");
            return redirect()->route('collegeadmin.course_offering.section', ['section_id' => $sectionId]);
        }

        // Update the semester in the curriculum
        $curriculum->period = $request->semester;
        $curriculum->save();

        Session::flash('success', "Semester updated for the subject");
        return redirect()->route('collegeadmin.course_offering.section', ['section_id' => $sectionId]);
    }

    /**
     * Get available curricula that can be added to a section
     */
    private function getAvailableCurricula($section, $collegeCode)
    {
        // Get offered curricula IDs
        $offeredIds = offerings_infos_table::where('section_name', $section->section_name)
            ->pluck('curriculum_id')
            ->toArray();

        // Get available curricula
        $curricula = curriculum::where('program_code', $section->program_code)
            ->where('college_code', $collegeCode)
            ->where('level', $section->level)
            ->where('is_active', 1)
            ->whereNotIn('id', $offeredIds)
            ->get();

        return $curricula;
    }
}
