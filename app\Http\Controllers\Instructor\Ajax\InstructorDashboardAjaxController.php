<?php

namespace App\Http\Controllers\Instructor\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Request;
use App\College;
use App\CtrRoom;
use App\CtrSection;
use App\room_schedules;
use App\User;
use App\curriculum;
use App\offerings_infos_table;
use Carbon\Carbon;

class InstructorDashboardAjaxController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get schedule data for calendar view
     */
    public function getScheduleData()
    {
        if (Request::ajax()) {
            $instructor_id = Auth::user()->id;
            
            $schedules = room_schedules::where('instructor', $instructor_id)
                ->where('is_active', 1)
                ->get();
                
            $events = [];
            
            foreach ($schedules as $schedule) {
                $offering = offerings_infos_table::find($schedule->offering_id);
                
                if ($offering) {
                    $curriculum_details = curriculum::find($offering->curriculum_id);
                    
                    if ($curriculum_details) {
                        // Format for FullCalendar
                        $day_num = $this->getDayNumber($schedule->day);
                        
                        // Create event for each day in the current month
                        $now = Carbon::now();
                        $start_of_month = Carbon::now()->startOfMonth();
                        $end_of_month = Carbon::now()->endOfMonth();
                        
                        for ($date = $start_of_month; $date->lte($end_of_month); $date->addDay()) {
                            // Only add for matching days (e.g., if schedule is for Monday, only add Monday dates)
                            if ($date->dayOfWeek == $day_num) {
                                $start_time = Carbon::parse($date->format('Y-m-d') . ' ' . $schedule->time_starts);
                                $end_time = Carbon::parse($date->format('Y-m-d') . ' ' . $schedule->time_end);
                                
                                $events[] = [
                                    'id' => $schedule->id,
                                    'title' => $curriculum_details->course_code . ' - ' . $offering->section_name,
                                    'start' => $start_time->format('Y-m-d H:i:s'),
                                    'end' => $end_time->format('Y-m-d H:i:s'),
                                    'description' => $curriculum_details->course_name,
                                    'room' => $schedule->room,
                                    'backgroundColor' => $this->getRandomColor($schedule->offering_id),
                                    'borderColor' => $this->getRandomColor($schedule->offering_id)
                                ];
                            }
                        }
                    }
                }
            }
            
            return response()->json($events);
        }
    }

    /**
     * Get attendance data for a specific course
     */
    public function getAttendanceData()
    {
        if (Request::ajax()) {
            $instructor_id = Auth::user()->id;
            $offering_id = Input::get('offering_id');
            
            // In a real implementation, this would fetch actual attendance data
            // For now, we'll generate placeholder data
            
            $offering = offerings_infos_table::find($offering_id);
            $curriculum_details = null;
            
            if ($offering) {
                $curriculum_details = curriculum::find($offering->curriculum_id);
            }
            
            if (!$curriculum_details) {
                return response()->json(['error' => 'Course not found']);
            }
            
            // Generate placeholder attendance data
            $total_students = rand(15, 40);
            $attendance_data = [];
            
            // Generate data for the last 10 sessions
            for ($i = 10; $i >= 1; $i--) {
                $date = Carbon::now()->subDays($i * 7)->format('Y-m-d');
                $present = rand(round($total_students * 0.7), $total_students);
                
                $attendance_data[] = [
                    'date' => $date,
                    'present' => $present,
                    'absent' => $total_students - $present,
                    'percentage' => round(($present / $total_students) * 100, 2)
                ];
            }
            
            $response = [
                'course_code' => $curriculum_details->course_code,
                'course_name' => $curriculum_details->course_name,
                'section_name' => $offering->section_name,
                'total_students' => $total_students,
                'attendance_data' => $attendance_data
            ];
            
            return response()->json($response);
        }
    }

    /**
     * Convert day name to day number (for FullCalendar)
     */
    private function getDayNumber($day)
    {
        $days = [
            'Sunday' => 0,
            'Monday' => 1,
            'Tuesday' => 2,
            'Wednesday' => 3,
            'Thursday' => 4,
            'Friday' => 5,
            'Saturday' => 6
        ];
        
        return $days[$day] ?? 1; // Default to Monday if not found
    }

    /**
     * Generate a random color based on offering ID (for consistent colors)
     */
    private function getRandomColor($id)
    {
        // Use offering ID as seed for consistent colors
        srand($id);
        
        // Generate pastel colors for better readability
        $hue = rand(0, 360);
        $saturation = rand(50, 70);
        $lightness = rand(50, 70);
        
        return "hsl($hue, $saturation%, $lightness%)";
    }
}
