<?php
$layout = '';

if (Auth::user()->is_first_login == 1) {
    $layout = 'layouts.first_login';
} else {
    if (Auth::user()->accesslevel == 100) {
        $layout = 'layouts.superadmin';
    } elseif (Auth::user()->accesslevel == 50) {
        $layout = 'layouts.collegeadmin';
    } elseif (Auth::user()->accesslevel == 1) {
        $layout = 'layouts.instructor';
    } elseif (Auth::user()->accesslevel == 0) {
        $layout = 'layouts.admin';
    }
}

?>
@extends($layout)

@section('main-content')
    <section class="content-header">
        <h1><i class="fa fa-spinner"></i>
            Course Scheduling - {{ $college->college_name }}
            <small></small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="{{ url('/') }}"><i class="fa fa-home"></i> Home</a></li>
            <li class="active">Course Scheduling</li>
        </ol>
    </section>

    <div class="container-fluid" style="margin-top: 15px;">
        <div class="box box-default">
            <div class="box-header">
                <h5 class="box-title">Course Scheduling - {{ $college->college_name }}</h5>
            </div>
            <div class="box-body">
                <div class='row filter-row'>
                    <!-- Left Column - Program Filter -->
                    <div class='col-md-4 col-sm-12'>
                        <!-- Program Filter -->
                        <div class='form-group'>
                            <label><i class="fa fa-book"></i> Program</label>
                            <div class="input-group">
                                <select class='select2 form-control' id='program_code'>
                                    <option value=''>All Programs</option>
                                    @foreach ($programs as $program)
                                        <option value='{{ $program->program_code }}'>{{ $program->program_name }}</option>
                                    @endforeach
                                </select>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default btn-flat" onclick="clearProgramFilter()" title="Clear Program Filter">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Middle Column - Level Filter and Search Button -->
                    <div class='col-md-4 col-sm-6'>
                        <!-- Level Filter -->
                        <div class='form-group'>
                            <label><i class="fa fa-graduation-cap"></i> Level</label>
                            <div class="input-group">
                                <select class='select2 form-control' id='level'>
                                    <option value=''>All Levels</option>
                                    @foreach ($levels as $level)
                                        <option value='{{ $level }}'>{{ $level }}</option>
                                    @endforeach
                                </select>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default btn-flat" onclick="clearLevelFilter()" title="Clear Level Filter">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </span>
                            </div>
                        </div>

                        <!-- Search Button -->
                        <div class='form-group'>
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-primary btn-flat btn-block" onclick="filterSections()">
                                <i class="fa fa-search"></i> Search
                            </button>
                        </div>
                    </div>

                    <!-- Right Column - Section Filter -->
                    <div class='col-md-4 col-sm-6'>
                        <!-- Section Filter -->
                        <div class='form-group'>
                            <label><i class="fa fa-users"></i> Section</label>
                            <select class='select2 form-control' id='section' onchange='getcoursesoffered(this.value)'>
                                <option value=''>Please Select</option>
                                @if($sections->count() > 0)
                                    @foreach ($sections as $section)
                                        <option value='{{ $section->section_name }}'
                                                data-level='{{ $section->level }}'
                                                data-college='{{ $section->college_code }}'
                                                data-program='{{ $section->program_code }}'>
                                            {{ $section->section_name }}
                                        </option>
                                    @endforeach
                                @else
                                    <option value='' disabled>No sections available - Click Search to load sections</option>
                                @endif
                            </select>
                        </div>
                        @if($sections->count() == 0)
                        <div class="alert alert-info" style="margin-top: 10px;">
                            <i class="fa fa-info-circle"></i>
                            <strong>No sections loaded initially.</strong>
                            Please use the search filters above and click the <strong>Search</strong> button to load sections for your college.
                        </div>
                        @endif
                    </div>
                </div>
                <div id="filter-status" class="small text-muted" style="margin-top: -10px; margin-bottom: 10px;"></div>
                <div class="small text-muted" style="margin-top: -5px; margin-bottom: 5px;">
                    <i class="fa fa-keyboard-o"></i> Tip: Press <kbd>Enter</kbd> to search
                </div>
            </div>
        </div>

        <div id='displayoffered'></div>
    </div>
@endsection

@section('header-style')
<style>
    /* Custom styles for Select2 dropdowns */
    .select2-container--default .select2-selection--single {
        height: 34px;
        border-radius: 0;
        border-color: #d2d6de;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 32px;
        padding-right: 20px;
        padding-left: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 32px;
    }

    /* Fix for input group alignment */
    .input-group .select2-container {
        position: relative;
        z-index: 2;
        float: left;
        width: 100%;
        margin-bottom: 0;
        display: table;
        table-layout: fixed;
    }

    /* Ensure the dropdown appears above other elements */
    .select2-container--open {
        z-index: 9999;
    }

    /* Improve dropdown option styling */
    .select2-container--default .select2-results__option {
        padding: 6px 12px;
    }

    /* Style for badges in dropdown */
    .select2-results__option .label {
        display: inline-block;
        margin-left: 5px;
        font-size: 85%;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 767px) {
        .filter-row .form-group {
            margin-bottom: 15px;
        }
    }

    /* Fix for select2 dropdown width */
    .select2-container--default .select2-results > .select2-results__options {
        max-height: 250px;
        overflow-y: auto;
    }

    /* Ensure consistent heights for all form elements */
    .btn-flat, .form-control, .input-group-btn .btn {
        height: 34px;
    }

    /* Styles for the new layout */
    .filter-row .form-group {
        margin-bottom: 15px;
    }

    /* Balanced layout for all columns */
    .filter-row > div {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Ensure all form groups have consistent spacing */
    .filter-row .form-group {
        margin-bottom: 15px;
    }

    /* Limit the width of select2 dropdowns to prevent them from being too wide */
    .select2-container {
        max-width: 100%;
        width: 100% !important;
    }

    /* Fix for Select2 placeholder text alignment and display */
    .select2-container .select2-selection--single .select2-selection__rendered {
        padding-left: 12px;
        color: #555;
    }

    /* Fix for the placeholder text in Select2 */
    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        color: #999;
    }

    /* Ensure the section dropdown has proper height */
    #section {
        width: 100%;
        height: 34px;
    }

    /* Fix for the Select2 dropdown search field */
    .select2-search--dropdown .select2-search__field {
        width: 100%;
        padding: 6px 8px;
    }

    /* Fix for the Select2 dropdown container */
    .select2-container--default .select2-selection--single {
        border: 1px solid #d2d6de;
        border-radius: 0;
        box-shadow: none;
    }

    /* Fix for the Select2 dropdown when opened */
    .select2-dropdown {
        border-color: #d2d6de;
        border-radius: 0;
    }

    /* Fix for the "Select an option" text display */
    .select2-selection__placeholder {
        color: #999 !important;
    }

    /* Fix for the dropdown text boxes */
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #555;
    }

    /* Fix for the dropdown text boxes when empty */
    .select2-container--default .select2-selection--single {
        background-color: #fff;
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        /* On tablets, ensure proper spacing between columns */
        .filter-row > div {
            margin-bottom: 5px;
        }
    }

    /* Mobile adjustments */
    @media (max-width: 767px) {
        /* On mobile, stack columns with proper spacing */
        .filter-row > div {
            margin-bottom: 10px;
        }

        /* Ensure the last column doesn't have extra bottom margin */
        .filter-row > div:last-child {
            margin-bottom: 0;
        }
    }
</style>
@endsection

@section('footer-script')
    <script>
        // Global variables to track filter state
        var lastLevelFilter = '';
        var lastProgramFilter = '';
        var isInitialLoad = true;
        var collegeCode = '{{ $college->college_code }}';

        $(document).ready(function() {
            // Fix for Select2 placeholder text display
            $.fn.select2.defaults.set("placeholder", "Select an option");

            // Initialize select2 with enhanced options
            $('.select2').each(function() {
                var selectId = $(this).attr('id');
                var options = {
                    placeholder: 'Select an option',
                    allowClear: true,
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: 0,
                    escapeMarkup: function(markup) {
                        return markup; // Allow HTML in the dropdown
                    },
                    templateResult: function(data) {
                        // For section dropdown, we use a custom formatter
                        if (selectId === 'section') {
                            return data.text; // Let the custom formatter handle it
                        }

                        // For other dropdowns, just return the text
                        return data.text;
                    },
                    templateSelection: function(data) {
                        if (!data.id) return data.text;

                        // Truncate long text in the selection display
                        var $result = $('<span title="' + data.text + '"></span>');
                        var text = data.text;

                        // Different truncation lengths based on the dropdown
                        var maxLength = 30;
                        if (selectId === 'program_code') maxLength = 25;
                        if (selectId === 'level') maxLength = 15;
                        if (selectId === 'section') maxLength = 20;

                        // Truncate if too long
                        if (text.length > maxLength) {
                            text = text.substr(0, maxLength - 3) + '...';
                        }

                        $result.text(text);
                        return $result;
                    }
                };

                // Initialize the select2 dropdown
                $(this).select2(options);

                // Fix for placeholder text display
                if ($(this).val() === '' || $(this).val() === null) {
                    setTimeout(function() {
                        $('.select2-selection__rendered[title="Select an option"]').text('Select an option');
                    }, 100);
                }
            });

            // Add event listeners for filter changes
            $('#program_code, #level').on('change', function() {
                // Update the filter status text
                updateFilterStatus();

                // Don't auto-filter on initial load to avoid unnecessary requests
                if (!isInitialLoad) {
                    // Auto-filter when dropdown values change
                    filterSections();
                }
            });

            // Add Enter key handler for the filter dropdowns
            $('.select2').on('keydown', function(e) {
                if (e.keyCode === 13) { // Enter key
                    e.preventDefault();
                    filterSections();
                    // Close any open dropdown
                    $('.select2-container--open').select2('close');
                }
            });

            // Show success or error messages if they exist in the session
            @if(Session::has('success'))
                toastr.success('{{ Session::get("success") }}');
            @endif

            @if(Session::has('error'))
                toastr.error('{{ Session::get("error") }}');
            @endif

            // Initialize filters if any filter is pre-selected
            var preSelectedProgram = $('#program_code').val();
            var preSelectedLevel = $('#level').val();

            if (preSelectedProgram || preSelectedLevel) {
                updateFilterStatus();
                filterSections();
            }

            // Set initial load flag to false after initialization
            isInitialLoad = false;

            // Auto-load sections if none are initially loaded
            @if($sections->count() == 0)
            console.log('No sections loaded initially, attempting to load sections...');
            setTimeout(function() {
                filterSections();
            }, 1000);
            @endif

            // Add keyboard shortcut for search (Enter key)
            $(document).keydown(function(e) {
                // Check if Enter key is pressed and not inside a text input
                if (e.keyCode === 13 && !$(e.target).is('textarea, input[type="text"]')) {
                    e.preventDefault(); // Prevent default Enter key behavior
                    filterSections();
                }
            });
        });

        /**
         * Update the filter status text to show current filters
         */
        function updateFilterStatus() {
            var program_code = $('#program_code').val();
            var level = $('#level').val();
            var statusText = '';

            if (program_code || level) {
                statusText = 'Filtering by: ';
                var filters = [];

                if (program_code) {
                    var programName = $('#program_code option:selected').text();
                    filters.push('<strong>Program:</strong> ' + programName);
                }

                if (level) {
                    filters.push('<strong>Level:</strong> ' + level);
                }

                statusText += filters.join(' | ');
            }

            $('#filter-status').html(statusText);

            // Store the current filter values for comparison
            lastProgramFilter = program_code;
            lastLevelFilter = level;
        }

        /**
         * Clear the program filter
         */
        function clearProgramFilter() {
            $('#program_code').val('').trigger('change');
        }

        /**
         * Clear the level filter
         */
        function clearLevelFilter() {
            $('#level').val('').trigger('change');
        }

        /**
         * Clear all filters
         */
        function clearAllFilters() {
            $('#program_code, #level').val('').trigger('change');
        }

        /**
         * Filter sections based on selected program and level
         */
        function filterSections() {
            var program_code = $('#program_code').val();
            var level = $('#level').val();

            // Update the filter status
            updateFilterStatus();

            // Show loading indicator in the button
            var $searchBtn = $('button.btn-primary');
            var originalBtnHtml = $searchBtn.html();
            $searchBtn.html('<i class="fa fa-spinner fa-spin"></i> Searching...').prop('disabled', true);

            console.log('Filtering sections for college_code:', collegeCode, 'program_code:', program_code, 'level:', level);

            // Show loading indicator in the section dropdown
            $('#section').prop('disabled', true).empty().append('<option value="">Loading sections...</option>');
            $('#section').trigger('change');

            // Clear any displayed courses
            $('#displayoffered').html('');

            // Make an AJAX request to get filtered sections
            $.ajax({
                type: "GET",
                url: "/ajax/collegeadmin/course_scheduling/get_sections",
                data: {
                    college_code: collegeCode,
                    program_code: program_code,
                    level: level
                },
                success: function(data) {
                    // Parse the HTML response to get section options
                    var $response = $(data);
                    var filteredOptions = [];
                    var alertMessage = $response.find('.alert').text();

                    // Add the default option
                    filteredOptions.push({
                        id: '',
                        text: 'Please Select a Section'
                    });

                    // Extract section options from the response
                    $response.find('option').each(function() {
                        var $option = $(this);
                        var sectionName = $option.val();
                        var sectionLevel = $option.data('level');
                        var sectionCollege = $option.data('college');
                        var sectionProgram = $option.data('program');
                        var sectionText = $option.text().trim();

                        // Skip the default option
                        if (sectionName === '') {
                            return;
                        }

                        // Add to filtered options with all data attributes
                        filteredOptions.push({
                            id: sectionName,
                            text: sectionText,
                            level: sectionLevel,
                            college: sectionCollege,
                            program: sectionProgram
                        });
                    });

                    // Clear and rebuild the select2 dropdown
                    $('#section').prop('disabled', false).empty().select2({
                        data: filteredOptions,
                        templateResult: formatSectionOption,
                        templateSelection: formatSectionSelection
                    });

                    // Show result count and restore button
                    var resultCount = filteredOptions.length - 1;
                    console.log('Loaded', resultCount, 'sections');

                    if (resultCount === 0) {
                        // Show a message if no sections found
                        var message = alertMessage || 'No sections found matching the selected criteria.';
                        toastr.info(message);

                        // Add a suggestion to clear filters
                        $('#filter-status').append(' <button class="btn btn-xs btn-default" onclick="clearAllFilters()">Clear All Filters</button>');
                    } else if (resultCount > 0) {
                        // Show success message with count
                        toastr.success('Found ' + resultCount + ' sections matching your criteria.');
                    }

                    // Restore the search button
                    $searchBtn.html(originalBtnHtml).prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading sections:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);

                    // Enable the dropdown and show an error message
                    $('#section').prop('disabled', false).empty().append('<option value="">Error loading sections</option>');
                    $('#section').trigger('change');

                    // Show detailed error message
                    var errorMessage = 'Failed to load sections. ';
                    if (xhr.status === 401) {
                        errorMessage += 'Please log in again.';
                    } else if (xhr.status === 403) {
                        errorMessage += 'Access denied. Please check your permissions.';
                    } else if (xhr.status === 500) {
                        errorMessage += 'Server error. Please contact administrator.';
                    } else {
                        errorMessage += 'Error: ' + error;
                    }

                    toastr.error(errorMessage);
                    $searchBtn.html(originalBtnHtml).prop('disabled', false);

                    // Add debug information to filter status
                    $('#filter-status').html('<div class="text-danger"><small>Debug: Status=' + xhr.status + ', Error=' + error + '</small></div>');
                }
            });
        }

        /**
         * Format the section option in the dropdown
         */
        function formatSectionOption(option) {
            if (!option.id) {
                return option.text;
            }

            var $option = $('<div class="section-option"></div>');
            $option.append('<strong>' + option.id + '</strong>');

            // Create a container for the badges
            var $badges = $('<div class="section-badges" style="margin-top: 3px;"></div>');

            // Add badges for program and level if available
            if (option.program) {
                $badges.append('<span class="label label-success" style="margin-right: 5px;">' +
                    '<i class="fa fa-book"></i> ' + option.program + '</span>');
            }

            if (option.level) {
                $badges.append('<span class="label label-info">' +
                    '<i class="fa fa-graduation-cap"></i> ' + option.level + '</span>');
            }

            $option.append($badges);
            return $option;
        }

        /**
         * Format the selected section in the dropdown
         */
        function formatSectionSelection(option) {
            if (!option.id) {
                return option.text;
            }

            // Create a container with title attribute for tooltip on hover
            var details = [];
            if (option.program) details.push('Program: ' + option.program);
            if (option.level) details.push('Level: ' + option.level);

            var tooltip = details.join(' | ');
            var $result = $('<span title="' + tooltip + '"></span>');
            $result.text(option.id);

            return $result;
        }

        /**
         * Get courses offered for a selected section
         */
        function getcoursesoffered(section_name) {
            if (!section_name) {
                $('#displayoffered').html('');
                return;
            }

            // Get the selected section's data attributes
            var selectedOption = $('#section option:selected');
            var section_college = selectedOption.data('college');
            var section_level = selectedOption.data('level');
            var section_program = selectedOption.data('program');

            // Get the filter values
            var filter_program = $('#program_code').val();
            var filter_level = $('#level').val();

            // Use section's values if available, otherwise use the filter values
            var program_code = section_program || filter_program;
            var level = section_level || filter_level;

            console.log('Getting courses for section:', section_name);
            console.log('Section college:', section_college, 'Section program:', section_program, 'Section level:', section_level);
            console.log('Using college_code:', collegeCode, 'program_code:', program_code, 'level:', level);

            // Show loading indicator with animation
            $('#displayoffered').html(
                '<div class="box box-default">' +
                '   <div class="box-body">' +
                '       <div class="text-center" style="padding: 30px;">' +
                '           <i class="fa fa-spinner fa-spin fa-3x text-primary"></i>' +
                '           <h4 style="margin-top: 20px;">Loading courses for ' + section_name + '...</h4>' +
                '           <p class="text-muted">Please wait while we retrieve the course data.</p>' +
                '       </div>' +
                '   </div>' +
                '</div>'
            );

            // Prepare request data
            var requestData = {
                college_code: collegeCode,
                program_code: program_code,
                section_name: section_name,
                level: level
            };

            // Make the AJAX request
            $.ajax({
                type: "GET",
                url: "/ajax/collegeadmin/course_scheduling/get_courses_offered",
                data: requestData,
                success: function(data) {
                    console.log('Received courses data');

                    // Fade out the loading indicator and fade in the new content
                    $('#displayoffered').fadeOut(200, function() {
                        $(this).html(data).fadeIn(300);
                    });

                    // Update the filter status to reflect the section's values
                    var filtersUpdated = false;

                    // Update the program dropdown if needed
                    if (section_program && filter_program !== section_program) {
                        // Only update if the option exists in the dropdown
                        if ($('#program_code option[value="' + section_program + '"]').length) {
                            $('#program_code').val(section_program);
                            filtersUpdated = true;
                        } else {
                            // If the program option doesn't exist, we need to add it
                            var programName = section_program; // Default to code if name not available
                            $('#program_code').append('<option value="' + section_program + '">' + programName + '</option>');
                            $('#program_code').val(section_program);
                            filtersUpdated = true;
                        }
                    }

                    // Update the level dropdown if needed
                    if (section_level && filter_level !== section_level) {
                        // Only update if the option exists in the dropdown
                        if ($('#level option[value="' + section_level + '"]').length) {
                            $('#level').val(section_level);
                            filtersUpdated = true;
                        }
                    }

                    // Trigger change event if filters were updated
                    if (filtersUpdated) {
                        // Update the filter status text without triggering another filter operation
                        updateFilterStatus();

                        // Refresh the select2 dropdowns to show the new values
                        $('#program_code, #level').trigger('change.select2');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error getting courses:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);

                    // Show error message with retry button
                    $('#displayoffered').html(
                        '<div class="alert alert-danger">' +
                        '   <h4><i class="icon fa fa-warning"></i> Error Loading Courses</h4>' +
                        '   <p>Failed to load courses for section ' + section_name + '. ' + error + '</p>' +
                        '   <button class="btn btn-danger btn-sm" onclick="getcoursesoffered(\'' + section_name + '\')">' +
                        '       <i class="fa fa-refresh"></i> Retry' +
                        '   </button>' +
                        '</div>'
                    );

                    // Show error toast
                    toastr.error('Failed to load courses. Please try again.');
                }
            });
        }

        function viewTabularSchedule(section_name) {
            window.open('/collegeadmin/course_scheduling/tabular_schedule/' + section_name, '_blank');
        }
    </script>
@endsection
