<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-book"></i>
        {{ $section->section_name }} Offerings
        <small></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/admin/course_offerings/curriculum')}}">Course Offering Curriculum</a></li>
        <li><a href="{{url('/admin/course_offerings/curriculum/program', [$section->program_code])}}">{{ $section->program_code }}</a></li>
        <li class="active">{{ $section->section_name }}</li>
      </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            @if(Session::has('success'))
                <div class="alert alert-success">
                    {{Session::get('success')}}
                </div>
            @endif
            @if(Session::has('error'))
                <div class="alert alert-danger">
                    {{Session::get('error')}}
                </div>
            @endif
            @if(Session::has('warning'))
                <div class="alert alert-warning">
                    {{Session::get('warning')}}
                </div>
            @endif
            @if(Session::has('info'))
                <div class="alert alert-info">
                    {{Session::get('info')}}
                </div>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Section Information</h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th>Section Name</th>
                                    <td>{{ $section->section_name }}</td>
                                </tr>
                                <tr>
                                    <th>Program</th>
                                    <td>{{ $section->program_code }}</td>
                                </tr>
                                <tr>
                                    <th>Level</th>
                                    <td>{{ $section->level }}</td>
                                </tr>
                                <tr>
                                    <th>College</th>
                                    <td>{{ $section->college_code ?? 'Not assigned' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group">
                                <a href="{{ url('/admin/course_offerings/curriculum/section/add_all', [$section->id]) }}"
                                   class="btn btn-success">
                                    <i class="fa fa-plus"></i> Add All Available Subjects
                                </a>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addSelectedModal">
                                    <i class="fa fa-plus-circle"></i> Add Selected Subjects
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Current Offerings</h3>
                </div>
                <div class="box-body">
                    @if($offerings->isEmpty())
                        <div class="alert alert-info">
                            No offerings found for this section.
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($offerings as $offering)
                                        <tr>
                                            <td>{{ $offering->curriculum->course_code }}</td>
                                            <td>{{ $offering->curriculum->course_name }}</td>
                                            <td>{{ $offering->curriculum->lec }}</td>
                                            <td>{{ $offering->curriculum->lab }}</td>
                                            <td>{{ $offering->curriculum->units }}</td>
                                            <td>
                                                <a href="{{ url('/admin/course_offerings/curriculum/section/remove', [$section->id, $offering->id]) }}"
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('Are you sure you want to remove this offering?')">
                                                    <i class="fa fa-trash"></i> Remove
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal for adding selected subjects -->
<div class="modal fade" id="addSelectedModal" tabindex="-1" role="dialog" aria-labelledby="addSelectedModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="addSelectedModalLabel">Add Subjects to {{ $section->section_name }}</h4>
            </div>
            <div class="modal-body">
                <div id="availableCurriculaContainer">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin fa-2x"></i>
                        <p>Loading available subjects...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="submitSelectedBtn" disabled>Add Selected</button>
            </div>
        </div>
    </div>
</div>

<script src="{{asset('plugins/select2/select2.js')}}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();

        // Load available subjects when modal is shown
        $('#addSelectedModal').on('shown.bs.modal', function() {
            loadAvailableSubjects();
        });

        // Handle form submission for selected curricula
        $('#submitSelectedBtn').on('click', function() {
            var selectedIds = [];
            $('input[name="curriculum_ids[]"]:checked').each(function() {
                selectedIds.push($(this).val());
            });

            if (selectedIds.length === 0) {
                alert('Please select at least one subject to add.');
                return;
            }

            $('#addSelectedForm').submit();
        });
    });

    function loadAvailableSubjects(curriculum_year, period) {
        console.log('Loading available subjects for section ID: {{ $section->id }}');

        // Show loading message
        $('#availableCurriculaContainer').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading available subjects...</p></div>');

        var ajaxUrl = '{{ route("ajax.course_offerings.available_subjects") }}';
        console.log('AJAX URL:', ajaxUrl);

        var data = {
            section_id: '{{ $section->id }}'
        };

        // Add filters if provided
        if (curriculum_year) {
            data.curriculum_year = curriculum_year;
        }

        if (period) {
            data.period = period;
        }

        $.ajax({
            url: ajaxUrl,
            type: 'GET',
            data: data,
            success: function(data) {
                console.log('AJAX success. Response length:', data.length);
                $('#availableCurriculaContainer').html(data);

                // Enable submit button if there are available subjects
                if ($('input[name="curriculum_ids[]"]').length > 0) {
                    console.log('Found curriculum checkboxes:', $('input[name="curriculum_ids[]"]').length);
                    $('#submitSelectedBtn').prop('disabled', false);
                } else {
                    console.log('No curriculum checkboxes found');
                    $('#submitSelectedBtn').prop('disabled', true);
                }

                // Initialize select2 for any dropdowns in the loaded content
                $('.select2').select2();
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                console.error('Response:', xhr.responseText);
                $('#availableCurriculaContainer').html('<div class="alert alert-danger">Failed to load available subjects. Error: ' + error + '</div>');
            }
        });
    }
</script>
@endsection
