<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use Excel;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Input;
use App\Http\Controllers\Helper;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UploadCurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    function index()
    {
        return view('superadmin.curriculum_management.integrated_upload_curriculum');
    }

    function upload(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'college_code' => 'required',
            'import_file' => 'required|mimes:xls,xlsx'
        ]);

        $college_code = $request->college_code;
        $college = \App\College::where('college_code', $college_code)->first();

        if (!$college) {
            Session::flash('error', 'College not found!');
            return redirect()->back();
        }

        $row = 2; // Start from row 2 (after header)
        $path = $request->file('import_file')->getRealPath();

        try {
            Excel::load($path, function ($reader) use ($row) {
                $uploaded = [];
                $sheet = $reader->getActiveSheet();

                while ($sheet->cellExists("A{$row}")) {
                    $curriculum_year = $sheet->getCell("A{$row}")->getValue();
                    $period = $sheet->getCell("B{$row}")->getValue();
                    $level = $sheet->getCell("C{$row}")->getValue();
                    $program_code = $sheet->getCell("D{$row}")->getValue();
                    $course_code = $sheet->getCell("E{$row}")->getValue();
                    $course_name = $sheet->getCell("F{$row}")->getValue();
                    $lec = $sheet->getCell("G{$row}")->getValue();
                    $lab = $sheet->getCell("H{$row}")->getValue();
                    $units = $sheet->getCell("I{$row}")->getValue();

                    // Skip empty rows
                    if (empty($curriculum_year) || empty($program_code) || empty($course_code)) {
                        $row++;
                        continue;
                    }

                    $uploaded[] = [
                        'curriculum_year' => $curriculum_year,
                        'period' => $period,
                        'level' => $level,
                        'program_code' => $program_code,
                        'course_code' => $course_code,
                        'course_name' => $course_name,
                        'lec' => $lec,
                        'lab' => $lab,
                        'units' => $units
                    ];
                    $row++;
                }

                session()->flash('upload', $uploaded);
            });

            // Store college code in session
            session()->flash('college_code', $college_code);

            $upload = session('upload');

            if (empty($upload)) {
                Session::flash('error', 'No valid data found in the Excel file!');
                return redirect()->back();
            }

            return view('superadmin.curriculum_management.integrated_upload_curriculum', compact('upload'));

        } catch (\Exception $e) {
            Session::flash('error', 'Error processing Excel file: ' . $e->getMessage());
            return redirect()->back();
        }
    }

    public function save_changes(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'college_code' => 'required',
            'curriculum_year' => 'required|array',
            'program_code' => 'required|array',
            'course_code' => 'required|array',
            'course_name' => 'required|array',
            'lec' => 'required|array',
            'lab' => 'required|array',
            'units' => 'required|array',
            'complab' => 'required|array'
        ]);

        $college_code = $request->college_code;
        $college = \App\College::where('college_code', $college_code)->first();

        if (!$college) {
            Session::flash('error', 'College not found!');
            return redirect()->back();
        }

        // Check if the program codes in the upload are associated with the selected college
        // If not, add them to the college's programs
        $collegePrograms = $college->getProgramCodes();
        $newPrograms = [];

        foreach ($request->program_code as $programCode) {
            if (!in_array($programCode, $collegePrograms)) {
                $newPrograms[] = $programCode;
            }
        }

        // If there are new programs, add them to the college
        if (!empty($newPrograms)) {
            $updatedPrograms = array_merge($collegePrograms, $newPrograms);
            $college->description = implode(', ', $updatedPrograms);
            $college->save();
        }

        // Now save all curriculum entries
        for ($x = 0; $x < count($request->curriculum_year); $x++) {
            $curricula = new \App\curriculum;
            $curricula->curriculum_year = $request->curriculum_year[$x];
            $curricula->program_code = $request->program_code[$x];
            $curricula->college_code = $college_code; // Use the selected college

            // Get program name from program_names table or use program code as fallback
            $programName = DB::table('program_names')
                ->where('program_code', $request->program_code[$x])
                ->first();
            $curricula->program_name = $programName ? $programName->program_name : $request->program_code[$x];

            // Save program name if it doesn't exist in program_names table
            if (!$programName) {
                DB::table('program_names')->insert([
                    'program_code' => $request->program_code[$x],
                    'program_name' => $request->program_code[$x], // Use code as name if not found
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            $curricula->control_code = $request->course_code[$x];
            $curricula->course_code = $request->course_code[$x];
            $curricula->course_name = $request->course_name[$x];
            $curricula->lec = $request->lec[$x];
            $curricula->lab = $request->lab[$x];
            $curricula->units = $request->units[$x];
            $curricula->level = $request->level[$x];
            $curricula->period = $request->period[$x];
            $curricula->percent_tuition = 100;
            $curricula->is_complab = $request->complab[$x];
            $curricula->is_active = 1; // Set as active by default
            $curricula->save();
        }

        Session::flash('success', "Successfully saved curriculum data for {$college->college_name}!");
        return redirect(url('/superadmin/curriculum_management/college', [$college_code]));
    }

    /**
     * Handle manual curriculum entry
     */
    public function manual_upload(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'college_code' => 'required',
            'curriculum_year' => 'required|array',
            'program_code' => 'required|array',
            'course_code' => 'required|array',
            'course_name' => 'required|array',
            'lec' => 'required|array',
            'lab' => 'required|array',
            'units' => 'required|array',
            'complab' => 'required|array',
            'period' => 'required|array',
            'level' => 'required|array'
        ]);
        
        $college_code = $request->college_code;
        $college = \App\College::where('college_code', $college_code)->first();
        
        if (!$college) {
            Session::flash('error', 'College not found!');
            return redirect()->back();
        }
        
        // Check if the program codes in the upload are associated with the selected college
        // If not, add them to the college's programs
        $collegePrograms = $college->getProgramCodes();
        $newPrograms = [];
        
        foreach ($request->program_code as $programCode) {
            if (!in_array($programCode, $collegePrograms)) {
                $newPrograms[] = $programCode;
            }
        }
        
        // If there are new programs, add them to the college
        if (!empty($newPrograms)) {
            $updatedPrograms = array_merge($collegePrograms, $newPrograms);
            $college->description = implode(', ', $updatedPrograms);
            $college->save();
        }
        
        // Now save all curriculum entries
        for ($x = 0; $x < count($request->curriculum_year); $x++) {
            $curricula = new \App\curriculum;
            $curricula->curriculum_year = $request->curriculum_year[$x];
            $curricula->program_code = $request->program_code[$x];
            $curricula->college_code = $college_code; // Use the selected college
            
            // Get program name from program_names table or use program code as fallback
            $programName = DB::table('program_names')
                ->where('program_code', $request->program_code[$x])
                ->first();
            $curricula->program_name = $programName ? $programName->program_name : $request->program_code[$x];
            
            // Save program name if it doesn't exist in program_names table
            if (!$programName) {
                DB::table('program_names')->insert([
                    'program_code' => $request->program_code[$x],
                    'program_name' => $request->program_code[$x], // Use code as name if not found
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            $curricula->control_code = $request->course_code[$x];
            $curricula->course_code = $request->course_code[$x];
            $curricula->course_name = $request->course_name[$x];
            $curricula->lec = $request->lec[$x];
            $curricula->lab = $request->lab[$x];
            $curricula->units = $request->units[$x];
            $curricula->level = $request->level[$x];
            $curricula->period = $request->period[$x];
            $curricula->percent_tuition = 100;
            $curricula->is_complab = $request->complab[$x];
            $curricula->is_active = 1; // Set as active by default
            $curricula->save();
        }
        
        Session::flash('success', "Successfully saved curriculum data for {$college->college_name}!");
        return redirect(url('/superadmin/curriculum_management/college', [$college_code]));
    }
}
