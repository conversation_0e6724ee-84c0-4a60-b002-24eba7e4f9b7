<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOSS - Curriculum Years for ' . $program_code)

@section('content')
<section class="content-header">
    <h1>
        Curriculum Years for {{ $program_code }}
        <small>{{ $program_name }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li class="active">{{ $program_code }}</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Years</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.curriculum.index') }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back to Programs</a>
                        <a href="{{ route('collegeadmin.curriculum.create') }}?program_code={{ $program_code }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> New Curriculum</a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="40%">Curriculum Year</th>
                                    <th width="30%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($curricula as $curriculum)
                                    <tr>
                                        <td>{{ $curriculum->curriculum_year }} - {{ is_numeric($curriculum->curriculum_year) ? (int)$curriculum->curriculum_year + 1 : $curriculum->curriculum_year }}</td>
                                        <td class="text-center">
                                            <a href="{{ route('collegeadmin.curriculum.list_curriculum', [$program_code, $curriculum->curriculum_year]) }}" class="btn btn-flat btn-success" title="View Curriculum"><i class="fa fa-eye"></i></a>
                                            <a onclick="displayedityear('{{ $curriculum->curriculum_year }}','{{ $program_code }}')" class="btn btn-flat btn-primary" title="Edit Curriculum Year"><i class="fa fa-pencil"></i></a>
                                            <a onclick="archiveCurriculum('{{ $curriculum->curriculum_year }}','{{ $program_code }}')" class="btn btn-flat btn-danger" title="Archive Curriculum"><i class="fa fa-archive"></i></a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal for editing curriculum year -->
<div class="modal fade" id="edit-year-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form method="post" action="{{ route('collegeadmin.curriculum.update_year') }}">
                @csrf
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Edit Curriculum Year</h4>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="program_code" id="edit_program_code">
                    <input type="hidden" name="old_curriculum_year" id="old_curriculum_year">
                    <div class="form-group">
                        <label>New Curriculum Year</label>
                        <input type="text" name="new_curriculum_year" id="new_curriculum_year" class="form-control" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function displayedityear(curriculum_year, program_code) {
        $('#old_curriculum_year').val(curriculum_year);
        $('#new_curriculum_year').val(curriculum_year);
        $('#edit_program_code').val(program_code);
        $('#edit-year-modal').modal('show');
    }

    function archiveCurriculum(curriculum_year, program_code) {
        if (confirm('Are you sure you want to archive this curriculum?')) {
            window.location.href = "{{ url('/collegeadmin/curriculum/archive_curriculum') }}/" + program_code + "/" + curriculum_year;
        }
    }
</script>
@endpush
@endsection
