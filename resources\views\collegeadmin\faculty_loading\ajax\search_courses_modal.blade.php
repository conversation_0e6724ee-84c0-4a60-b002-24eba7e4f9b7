<?php 
$collection = collect([]);

if(!$curriculum->isEmpty()){
    foreach($curriculum as $curricula){
        $offering = \App\offerings_infos::where('curriculum_id',$curricula->id)->get();
        if(!$offering->isEmpty()){
            foreach($offering as $offer){
                $schedules = \App\room_schedules::distinct()->where('offering_id',$offer->id)
                        ->where('instructor',NULL)
                        ->where('is_active',1)
                        ->get(['offering_id']);
                if(!$schedules->isEmpty()){
                    $collection->push((object)[
                        'level' => $offer->level,
                        'offering_id' => $offer->id,
                        'section_name' => $offer->section_name,
                        'curriculum_id' => $curricula->id
                    ]);
                }
            }
        }
    }
}

$color_array = ['info','danger','warning','success'];
$ctr = 0;
?>

@if(!$collection->isEmpty())
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Course Code</th>
                    <th>Course Name</th>
                    <th>Section</th>
                    <th>Schedule</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($collection as $data)
                    <?php $curricula = \App\curriculum::find($data->curriculum_id); ?>
                    <tr>
                        <td>{{ $curricula->course_code }}</td>
                        <td>{{ $curricula->course_name }}</td>
                        <td>{{ $data->section_name }}</td>
                        <td>
                            <?php
                            $schedules = \App\room_schedules::where('offering_id', $data->offering_id)->get();
                            ?>
                            @foreach($schedules as $schedule)
                                <div class="schedule-item">
                                    <strong>{{ $schedule->room }}</strong><br>
                                    {{ $schedule->sched_day }} 
                                    {{ date('h:i A', strtotime($schedule->sched_from)) }} - 
                                    {{ date('h:i A', strtotime($schedule->sched_to)) }}
                                </div>
                                @if(!$loop->last)<hr style="margin: 5px 0;">@endif
                            @endforeach
                        </td>
                        <td>
                            <div class="draggable-schedule callout callout-{{ $color_array[$ctr % 4] }}" 
                                 data-event='{"offering": "{{ $data->offering_id }}", "title": "{{ $curricula->course_code }}", "section": "{{ $data->section_name }}"}'>
                                <div class="text-center">
                                    <i class="fa fa-arrows"></i> Drag to Calendar
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary btn-sm add-to-load" data-offering="{{ $data->offering_id }}">
                                <i class="fa fa-plus"></i> Add to Load
                            </button>
                        </td>
                    </tr>
                    <?php $ctr++; ?>
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> No courses found matching your search criteria.
    </div>
@endif

<style>
.draggable-schedule {
    cursor: move;
    margin-bottom: 5px;
    padding: 8px;
    border-radius: 3px;
    text-align: center;
    font-size: 12px;
}

.draggable-schedule:hover {
    opacity: 0.8;
}

.schedule-item {
    font-size: 11px;
    margin-bottom: 3px;
}

.callout {
    border-left: 5px solid #eee;
    border-radius: 3px;
    margin: 5px 0;
    padding: 5px 10px;
}

.callout-info {
    border-left-color: #5bc0de;
    background-color: #f4f8fa;
}

.callout-warning {
    border-left-color: #f0ad4e;
    background-color: #fcf8f2;
}

.callout-success {
    border-left-color: #5cb85c;
    background-color: #f3f8f3;
}

.callout-danger {
    border-left-color: #d9534f;
    background-color: #fdf7f7;
}
</style>
