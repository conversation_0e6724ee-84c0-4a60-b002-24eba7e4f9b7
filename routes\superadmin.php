<?php

// SuperAdmin Dashboard
Route::get('/superadmin/dashboard', '<PERSON>Ad<PERSON>\DashboardController@index');

// Faculty Loading
Route::get('/superadmin/faculty_loading', 'SuperAdmin\FacultyLoadingController@faculty_loading');
Route::get('/superadmin/faculty_loading/generate_schedule/{instructor}', 'SuperAdmin\FacultyLoadingController@generate_schedule');
Route::get('/ajax/superadmin/faculty_loading/courses_to_load', 'SuperAdmin\Ajax\FacultyLoadingAjax@courses_to_load');
Route::get('/ajax/superadmin/faculty_loading/current_load', 'SuperAdmin\Ajax\FacultyLoadingAjax@current_load');
Route::get('/ajax/superadmin/faculty_loading/add_faculty_load', 'SuperAdmin\Ajax\FacultyLoadingAjax@add_faculty_load');
Route::get('/ajax/superadmin/faculty_loading/remove_faculty_load', 'SuperAdmin\Ajax\FacultyLoadingAjax@remove_faculty_load');
Route::get('/ajax/superadmin/faculty_loading/search_courses', 'SuperAdmin\Ajax\FacultyLoadingAjax@search_courses');
Route::get('/ajax/superadmin/faculty_loading/get_units_loaded', 'SuperAdmin\Ajax\FacultyLoadingAjax@get_units_loaded');
Route::get('/ajax/superadmin/faculty_loading/override_add', 'SuperAdmin\Ajax\FacultyLoadingAjax@override_add');
Route::get('/ajax/superadmin/faculty_loading/get_programs_by_college', 'SuperAdmin\Ajax\FacultyLoadingAjax@get_programs_by_college');
