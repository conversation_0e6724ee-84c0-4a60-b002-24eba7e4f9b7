<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class room_schedules extends Model
{
    protected $fillable = [
        'day',
        'day_type',
        'time_starts',
        'time_end',
        'room',
        'college_code',
        'priority_level',
        'offering_id',
        'instructor',
        'is_active',
        'is_loaded'
    ];

    /**
     * Get the offering that owns the schedule
     */
    public function offering()
    {
        return $this->belongsTo('App\offerings_infos_table', 'offering_id');
    }

    /**
     * Get the instructor assigned to this schedule
     */
    public function instructorUser()
    {
        return $this->belongsTo('App\User', 'instructor');
    }

    /**
     * Get the college that owns the schedule
     */
    public function college()
    {
        return $this->belongsTo('App\College', 'college_code', 'college_code');
    }

    /**
     * Check if this schedule conflicts with another time range for the same room
     *
     * @param string $day
     * @param string $timeStart
     * @param string $timeEnd
     * @param string $room
     * @param int|null $excludeId
     * @return bool
     */
    public static function hasRoomConflict($day, $timeStart, $timeEnd, $room, $excludeId = null)
    {
        $query = self::where('day', $day)
            ->where('room', $room)
            ->where('is_active', 1);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->where(function ($q) use ($timeStart, $timeEnd) {
            // Schedule starts during another schedule
            $q->where(function ($subq) use ($timeStart, $timeEnd) {
                $subq->where('time_starts', '<=', date('H:i:s', strtotime($timeStart)))
                    ->where('time_end', '>', date('H:i:s', strtotime($timeStart)));
            })
                // Schedule ends during another schedule
                ->orWhere(function ($subq) use ($timeStart, $timeEnd) {
                    $subq->where('time_starts', '<', date('H:i:s', strtotime($timeEnd)))
                        ->where('time_end', '>=', date('H:i:s', strtotime($timeEnd)));
                })
                // Schedule contains another schedule
                ->orWhere(function ($subq) use ($timeStart, $timeEnd) {
                    $subq->where('time_starts', '>=', date('H:i:s', strtotime($timeStart)))
                        ->where('time_end', '<=', date('H:i:s', strtotime($timeEnd)));
                });
        })
            ->exists();
    }

    /**
     * Check if this schedule conflicts with another time range for the same instructor
     *
     * @param string $day
     * @param string $timeStart
     * @param string $timeEnd
     * @param int $instructorId
     * @param int|null $excludeId
     * @return bool
     */
    public static function hasInstructorConflict($day, $timeStart, $timeEnd, $instructorId, $excludeId = null)
    {
        $query = self::where('day', $day)
            ->where('instructor', $instructorId)
            ->where('is_active', 1);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->where(function ($q) use ($timeStart, $timeEnd) {
            // Schedule starts during another schedule
            $q->where(function ($subq) use ($timeStart, $timeEnd) {
                $subq->where('time_starts', '<=', date('H:i:s', strtotime($timeStart)))
                    ->where('time_end', '>', date('H:i:s', strtotime($timeStart)));
            })
                // Schedule ends during another schedule
                ->orWhere(function ($subq) use ($timeStart, $timeEnd) {
                    $subq->where('time_starts', '<', date('H:i:s', strtotime($timeEnd)))
                        ->where('time_end', '>=', date('H:i:s', strtotime($timeEnd)));
                })
                // Schedule contains another schedule
                ->orWhere(function ($subq) use ($timeStart, $timeEnd) {
                    $subq->where('time_starts', '>=', date('H:i:s', strtotime($timeStart)))
                        ->where('time_end', '<=', date('H:i:s', strtotime($timeEnd)));
                });
        })
            ->exists();
    }

    /**
     * Check if this schedule conflicts with another time range for the same section
     *
     * @param string $day
     * @param string $timeStart
     * @param string $timeEnd
     * @param string $sectionName
     * @param int|null $excludeId
     * @return bool
     */
    public static function hasSectionConflict($day, $timeStart, $timeEnd, $sectionName, $excludeId = null)
    {
        $query = self::join('offerings_infos', 'offerings_infos.id', '=', 'room_schedules.offering_id')
            ->where('room_schedules.day', $day)
            ->where('offerings_infos.section_name', $sectionName)
            ->where('room_schedules.is_active', 1);

        if ($excludeId) {
            $query->where('room_schedules.id', '!=', $excludeId);
        }

        return $query->where(function ($q) use ($timeStart, $timeEnd) {
            // Schedule starts during another schedule
            $q->where(function ($subq) use ($timeStart, $timeEnd) {
                $subq->where('room_schedules.time_starts', '<=', date('H:i:s', strtotime($timeStart)))
                    ->where('room_schedules.time_end', '>', date('H:i:s', strtotime($timeStart)));
            })
                // Schedule ends during another schedule
                ->orWhere(function ($subq) use ($timeStart, $timeEnd) {
                    $subq->where('room_schedules.time_starts', '<', date('H:i:s', strtotime($timeEnd)))
                        ->where('room_schedules.time_end', '>=', date('H:i:s', strtotime($timeEnd)));
                })
                // Schedule contains another schedule
                ->orWhere(function ($subq) use ($timeStart, $timeEnd) {
                    $subq->where('room_schedules.time_starts', '>=', date('H:i:s', strtotime($timeStart)))
                        ->where('room_schedules.time_end', '<=', date('H:i:s', strtotime($timeEnd)));
                });
        })
            ->exists();
    }

    /**
     * Get all conflicts for a schedule
     *
     * @param string $day
     * @param string $timeStart
     * @param string $timeEnd
     * @param string $room
     * @param int $instructorId
     * @param string $sectionName
     * @param int|null $excludeId
     * @return array
     */
    public static function getAllConflicts($day, $timeStart, $timeEnd, $room, $instructorId, $sectionName, $excludeId = null)
    {
        $conflicts = [];

        if (self::hasRoomConflict($day, $timeStart, $timeEnd, $room, $excludeId)) {
            $conflicts[] = 'Room ' . $room . ' is already scheduled at this time';
        }

        if ($instructorId && self::hasInstructorConflict($day, $timeStart, $timeEnd, $instructorId, $excludeId)) {
            $instructor = \App\User::find($instructorId);
            $instructorName = $instructor ? $instructor->lastname . ', ' . $instructor->name : 'Unknown';
            $conflicts[] = 'Instructor ' . $instructorName . ' is already scheduled at this time';
        }

        if (self::hasSectionConflict($day, $timeStart, $timeEnd, $sectionName, $excludeId)) {
            $conflicts[] = 'Section ' . $sectionName . ' is already scheduled at this time';
        }

        return $conflicts;
    }
}
