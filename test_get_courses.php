<?php
// Include the <PERSON><PERSON> application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// Set up the database connection
$app->make('Illuminate\Database\Eloquent\Factory');
$app->make('db');

// Import the necessary models
use App\offerings_infos_table;
use App\curriculum;

// Test the query for BSIT-A section
$section_name = 'BSIT-A';
$college_code = 'CCS';
$level = 'First Year';

echo "Testing query for section: $section_name, college: $college_code, level: $level\n";

try {
    // Start with a base query for the section
    $query = offerings_infos_table::where('section_name', $section_name);

    // Filter by level if provided
    if ($level) {
        $query->where('level', $level);
    }

    // Filter by college if provided
    if ($college_code) {
        $query->join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
            ->where('curricula.college_code', $college_code);
    }

    // Get the courses
    $courses = $query->get();
    
    echo "Found " . count($courses) . " courses\n";
    
    foreach ($courses as $course) {
        echo "Course: " . $course->course_code . " - " . $course->course_name . "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
}

echo "Done.\n";
