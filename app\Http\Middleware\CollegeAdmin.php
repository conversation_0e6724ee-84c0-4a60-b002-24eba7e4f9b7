<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class CollegeAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::user()->accesslevel != 50) {
            return redirect(url('/401'));
        }

        return $next($request);
    }
}
