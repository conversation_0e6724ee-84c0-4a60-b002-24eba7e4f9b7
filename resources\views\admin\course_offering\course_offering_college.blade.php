<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}


?>
@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-calendar-check-o"></i>
        Course Offerings - {{ $college->college_name }}
        <small></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/admin/course_offerings')}}">Course Offerings</a></li>
        <li class="active">{{ $college->college_name }}</li>
    </ol>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    <div class="box box-default">
        <div class="box-header with-border">
            <h3 class="box-title"><i class="fa fa-graduation-cap"></i> Academic Programs - {{ $college->college_name }}</h3>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <div class="box-body">
            <!-- Search/Filter Section -->
            <div class="row" style="margin-bottom: 15px;">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="program-search" placeholder="Search programs...">
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button" onclick="searchPrograms()"><i class="fa fa-search"></i></button>
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <a href="{{url('/admin/course_offerings')}}" class="btn btn-primary"><i class="fa fa-arrow-left"></i> Back</a>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="programs-table">
                    <thead>
                        <tr>
                            <th width="15%">Program Code</th>
                            <th>Name</th>
                            <th width="5%">Offerings</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(isset($programs) && !$programs->isEmpty())
                            @foreach($programs as $program)
                            <tr>
                                <td>{{$program->program_code}}</td>
                                <td>{{$program->program_name ?? 'N/A'}}</td>
                                <td class="text-center">
                                    <button onclick="viewProgramOfferings('{{$program->program_code}}', '{{$program->program_name}}')"
                                            class="btn btn-success view-offerings-btn"
                                            data-program-code="{{$program->program_code}}"
                                            title="View Program Offerings">
                                        <i class="fa fa-chevron-right"></i>
                                        <span class="loading-spinner" style="display: none;">
                                            <i class="fa fa-spinner fa-spin"></i>
                                        </span>
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="text-center">No programs found for this college</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function searchPrograms() {
    var input, filter, table, tr, td, i, txtValue;
    input = document.getElementById("program-search");
    filter = input.value.toUpperCase();
    table = document.getElementById("programs-table");
    tr = table.getElementsByTagName("tr");

    // Loop through all table rows, and hide those who don't match the search query
    for (i = 0; i < tr.length; i++) {
        // Skip header row
        if (i === 0) continue;

        // Check program code
        td = tr[i].getElementsByTagName("td")[0];
        if (td) {
            txtValue = td.textContent || td.innerText;
            var matchesCode = txtValue.toUpperCase().indexOf(filter) > -1;
        }

        // Check program name
        td = tr[i].getElementsByTagName("td")[1];
        if (td) {
            txtValue = td.textContent || td.innerText;
            var matchesName = txtValue.toUpperCase().indexOf(filter) > -1;
        }

        // Show/hide row based on matches
        if (matchesCode || matchesName) {
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
    }
}

// Add event listener for the search input
document.getElementById("program-search").addEventListener("keyup", searchPrograms);
</script>

<div class="modal fade" id="offeringsModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Program Offerings</h4>
            </div>
            <div class="modal-body" id="offeringsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewProgramOfferings(programCode, programName) {
    const button = $(`button[data-program-code="${programCode}"]`);
    const icon = button.find('.fa-chevron-right');
    const spinner = button.find('.loading-spinner');

    // Show loading state
    icon.hide();
    spinner.show();
    button.prop('disabled', true);

    $.ajax({
        url: '/admin/course_offerings/' + programCode,
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            $('#offeringsContent').html(response);
            $('#offeringsModal').modal('show');
            $('.modal-title').text(`Program Offerings - ${programName}`);
        },
        error: function(xhr) {
            let errorMessage = 'Failed to load program offerings';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            toastr.error(errorMessage);
        },
        complete: function() {
            // Restore button state
            icon.show();
            spinner.hide();
            button.prop('disabled', false);
        }
    });
}
</script>

<style>
.view-offerings-btn {
    position: relative;
    min-width: 40px;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
@endsection



