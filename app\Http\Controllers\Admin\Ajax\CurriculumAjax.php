<?php

namespace App\Http\Controllers\Admin\Ajax;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\College;
use App\curriculum;
use Illuminate\Support\Facades\DB;

class CurriculumAjax extends Controller
{
    /**
     * Filter programs by college code
     */
    public function filterPrograms(Request $request)
    {
        $collegeCode = $request->input('college_code');

        if (!$collegeCode) {
            return response()->json([]);
        }

        // Get the college
        $college = College::where('college_code', $collegeCode)->first();

        if (!$college) {
            return response()->json([]);
        }

        // Get programs for this college using the Program model
        $programsData = \App\Program::where('college_id', $college->id)->get();

        // Format programs for the response
        $programs = $programsData->map(function ($program) {
            return [
                'program_code' => $program->program_code,
                'program_name' => $program->program_name
            ];
        })->toArray();

        // Get additional program details from curriculum table
        foreach ($programs as &$program) {
            // Get curriculum years for this program
            $curriculumYears = curriculum::where('program_code', $program['program_code'])
                ->where('college_code', $collegeCode)
                ->where('is_active', 1)
                ->distinct()
                ->pluck('curriculum_year')
                ->toArray();

            $program['curriculum_years'] = $curriculumYears;

            // Get course count for each curriculum year
            $program['course_counts'] = [];
            foreach ($curriculumYears as $year) {
                $courseCount = curriculum::where('program_code', $program['program_code'])
                    ->where('college_code', $collegeCode)
                    ->where('curriculum_year', $year)
                    ->where('is_active', 1)
                    ->count();

                $program['course_counts'][$year] = $courseCount;
            }

            // Get total course count
            $program['total_courses'] = curriculum::where('program_code', $program['program_code'])
                ->where('college_code', $collegeCode)
                ->where('is_active', 1)
                ->count();

            // Add college information
            $program['college_code'] = $collegeCode;
            $program['college_name'] = $college->college_name;
        }

        return response()->json($programs);
    }
}
