<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Program extends Model
{
    protected $table = 'programs';
    
    // Define composite primary key
    protected $primaryKey = ['college_id', 'program_code'];
    public $incrementing = false;
    
    // No timestamps in this table
    public $timestamps = false;

    protected $fillable = [
        'college_id',
        'program_code',
        'program_name',
    ];

    /**
     * Get the college that owns the program
     */
    public function college()
    {
        return $this->belongsTo('App\College', 'college_id');
    }

    /**
     * Override the default find method to handle composite keys
     */
    public static function find($id)
    {
        if (is_array($id) && count($id) === 2) {
            return self::where('college_id', $id[0])
                ->where('program_code', $id[1])
                ->first();
        }
        return null;
    }
}
