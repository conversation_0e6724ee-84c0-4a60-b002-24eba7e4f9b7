<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}


?>
<?php
$colleges = \App\College::where('is_active', 1)->orderBy('college_code')->get();
$programs = \App\academic_programs::distinct()->orderBy('program_code')->get(array('program_code', 'program_name'));
?>
@extends($layout)
@section('messagemenu')
<li class="dropdown messages-menu">
            <!-- Menu toggle button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-envelope-o"></i>
              <span class="label label-success"></span>
            </a>
</li>
<li class="dropdown notifications-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-bell-o"></i>
              <span class="label label-warning"></span>
            </a>
</li>

<li class="dropdown tasks-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="label label-danger"></span>
            </a>
</li>
@endsection
@section('header')
<section class="content-header">
    <h1>
        Curriculum Management
        <small>Upload or Create Curriculum</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li class="active"><a href="{{ url('/superadmin/curriculum_management/upload_curriculum') }}"> Upload Curriculum</a></li>
    </ol>
</section>
@endsection
@section('maincontent')

<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-default">
                <div class="box-header">
                    <h3 class="box-title">Curriculum Management</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/superadmin/curriculum_management/add_curriculum') }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> Add Individual Courses</a>
                        <a href="{{ url('/superadmin/curriculum_management/colleges') }}" class="btn btn-flat btn-primary"><i class="fa fa-university"></i> View by College</a>
                        <a href="{{ url('/superadmin/college') }}" class="btn btn-flat btn-info"><i class="fa fa-cog"></i> Manage Colleges</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Nav tabs -->
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#upload" aria-controls="upload" role="tab" data-toggle="tab"><i class="fa fa-upload"></i> Upload Excel</a></li>
        <li role="presentation"><a href="#manual" aria-controls="manual" role="tab" data-toggle="tab"><i class="fa fa-pencil"></i> Manual Entry</a></li>
    </ul>

    <!-- Tab panes -->
    <div class="tab-content">
        <!-- Upload Excel Tab -->
        <div role="tabpanel" class="tab-pane active" id="upload">
            <div class="row">
                <div class="col-sm-12">
                    <div class="box box-default">
                        <div class="box-header">
                            <h3 class="box-title">Upload Curriculum</h3>
                            <div class="box-tools pull-right">
                                <a href="{{ url('/superadmin/curriculum_management/colleges') }}" class="btn btn-flat btn-primary"><i class="fa fa-university"></i> View by College</a>
                                <a href="{{ url('/superadmin/college') }}" class="btn btn-flat btn-info"><i class="fa fa-cog"></i> Manage Colleges</a>
                                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                            </div>
                        </div>
                        <div class="box-body">
                            <div class="alert alert-info">
                                <h4><i class="icon fa fa-info"></i> Excel File Format</h4>
                                <p>Please ensure your Excel file has the following columns in order:</p>
                                <ol>
                                    <li>Curriculum Year (e.g., 2023)</li>
                                    <li>Period (e.g., 1st Semester)</li>
                                    <li>Level (e.g., 1st Year)</li>
                                    <li>Program Code (e.g., BSIT)</li>
                                    <li>Course Code (e.g., ITE 101)</li>
                                    <li>Course Name (e.g., Introduction to Computing)</li>
                                    <li>Lecture Units (e.g., 2)</li>
                                    <li>Laboratory Units (e.g., 1)</li>
                                    <li>Total Units (e.g., 3)</li>
                                </ol>
                            </div>
                            <form action="{{ URL::to('superadmin/curriculum_management/upload') }}" class="form-horizontal" method="post" enctype="multipart/form-data">
                                {{ csrf_field() }}
                                <div class="form-group">
                                    <label class="control-label col-sm-2">College:</label>
                                    <div class="col-sm-6">
                                        <select name="college_code" id="college_code" class="form-control select2" required>
                                            <option value="">Select College</option>
                                            @foreach($colleges as $college)
                                            <option value="{{ $college->college_code }}">{{ $college->college_code }} - {{ $college->college_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-2">Excel File:</label>
                                    <div class="col-sm-6">
                                        <input type="file" required name="import_file" class="form-control" accept=".xls,.xlsx" />
                                        <span class="help-block">Excel File Type Only (.xls, .xlsx)</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-offset-2 col-sm-6">
                                        <button class="btn btn-primary" type="submit"><i class="fa fa-upload"></i> Upload Curriculum</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manual Entry Tab -->
        <div role="tabpanel" class="tab-pane" id="manual">
            <div class="row">
                <div class="col-sm-12">
                    <div class="box box-primary">
                        <form action="{{url('/superadmin/curriculum_management/manual_upload')}}" method="post">
                            {{csrf_field()}}
                            <div class="box-header">
                                <h3 class="box-title">Manual Curriculum Entry</h3>
                                <div class="box-tools pull-right">
                                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                                </div>
                            </div>
                            <div class="box-body">
                                <div class="form-group">
                                    <label class="control-label col-sm-2">College:</label>
                                    <div class="col-sm-6">
                                        <select name="college_code" id="manual_college_code" class="form-control select2" required>
                                            <option value="">Select College</option>
                                            @foreach($colleges as $college)
                                            <option value="{{ $college->college_code }}">{{ $college->college_code }} - {{ $college->college_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="dynamic_field">
                                        <thead>
                                            <tr>
                                                <th>Action</th>
                                                <th>Curriculum Year</th>
                                                <th width="15%">Period</th>
                                                <th width="12%">Level</th>
                                                <th width="12%">Program Code</th>
                                                <th width="10%">Course Code</th>
                                                <th width="25%">Course Name</th>
                                                <th width="7%">Lec</th>
                                                <th width="7%">Lab</th>
                                                <th width="7%">Units</th>
                                                <th width="7%">Computer Lab</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><button type="button" class="add btn btn-flat btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                                                <td><input type="text" class="form-control" name="curriculum_year[]" id="c_year1" required></td>
                                                <td>
                                                    <select class="form-control" id="period1" name="period[]" required>
                                                        <option value="1st Trimester">1st Trimester</option>
                                                        <option value="2nd Trimester">2nd Trimester</option>
                                                        <option value="3rd Trimester">3rd Trimester</option>
                                                        <option value="1st Semester">1st Semester</option>
                                                        <option value="2nd Semester">2nd Semester</option>
                                                        <option value="Summer">Summer</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select class="form-control" id="level1" name="level[]" required>
                                                        <option value="1st Year">1st Year</option>
                                                        <option value="2nd Year">2nd Year</option>
                                                        <option value="3rd Year">3rd Year</option>
                                                        <option value="4th Year">4th Year</option>
                                                        <option value="5th Year">5th Year</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select class="form-control select2" id="program1" name="program_code[]" required>
                                                        <option value="">Select Program</option>
                                                        @foreach($programs as $program)
                                                        <option value="{{$program->program_code}}">{{$program->program_code}} - {{$program->program_name}}</option>
                                                        @endforeach
                                                    </select>
                                                </td>
                                                <td><input type="text" class="form-control" name="course_code[]" id="code1" required></td>
                                                <td><input type="text" class="form-control" name="course_name[]" id="name1" required></td>
                                                <td><input type="text" class="form-control" name="lec[]" id="lec1" required></td>
                                                <td><input type="text" class="form-control" name="lab[]" id="lab1" required></td>
                                                <td><input type="text" class="form-control" name="units[]" id="units1" required></td>
                                                <td>
                                                    <select name="complab[]" class="form-control" id="complab1">
                                                        <option value="0">No</option>
                                                        <option value="1">Yes</option>
                                                    </select>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="box-footer">
                                <div class="pull-right">
                                    <button type="submit" class="btn btn-flat btn-success"><i class="fa fa-check-circle"></i> Save Changes</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Data Section (for Excel Upload) -->
    @if(Session::has('upload'))
    <div class='box box-primary'>
        <form action='{{url('/superadmin/curriculum_management/upload/save_changes')}}' method='post'>
            {{csrf_field()}}
            <input type="hidden" name="college_code" value="{{ Session::get('college_code') }}">
            <div class='box-header'>
                <h3 class='box-title'>Preview Data</h3>
                <p class='text-muted'><small>By clicking the 'Save Changes' button, the following will be saved into the database.</small></p>
            </div>
            <div class='box-body'>
                <div class='table-responsive'>
                    <table class='table table-bordered table-striped'>
                        <thead>
                            <tr>
                                <th>College Code</th>
                                <th>Curriculum Year</th>
                                <th>Period</th>
                                <th>Level</th>
                                <th>Program Code</th>
                                <th>Course Code</th>
                                <th>Course Name</th>
                                <th>Lec</th>
                                <th>Lab</th>
                                <th>Units</th>
                                <th>Computer Lab</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($upload as $key=>$load)
                            <tr>
                                <input type='hidden' name='curriculum_year[]' value='{{$load['curriculum_year']}}'>
                                <input type='hidden' name='period[]' value='{{$load['period']}}'>
                                <input type='hidden' name='level[]' value='{{$load['level']}}'>
                                <input type='hidden' name='program_code[]' value='{{$load['program_code']}}'>
                                <input type='hidden' name='course_code[]' value='{{$load['course_code']}}'>
                                <input type='hidden' name='course_name[]' value='{{$load['course_name']}}'>
                                <input type='hidden' name='lec[]' value='{{$load['lec']}}'>
                                <input type='hidden' name='lab[]' value='{{$load['lab']}}'>
                                <input type='hidden' name='units[]' value='{{$load['units']}}'>
                                <input type='hidden' name='complab[]' value='0'>
                                <td>{{ Session::get('college_code') }}</td>
                                <td>{{$load['curriculum_year']}}</td>
                                <td>{{$load['period']}}</td>
                                <td>{{$load['level']}}</td>
                                <td>{{$load['program_code']}}</td>
                                <td>{{$load['course_code']}}</td>
                                <td>{{$load['course_name']}}</td>
                                <td>{{$load['lec']}}</td>
                                <td>{{$load['lab']}}</td>
                                <td>{{$load['units']}}</td>
                                <td>
                                    <select name="complab[]" class="form-control">
                                        <option value="0">No</option>
                                        <option value="1">Yes</option>
                                    </select>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class='box-footer'>
                <div class='box-tools pull-right'>
                    <button type='submit' class='btn btn-flat btn-success'><i class='fa fa-check-circle-o'></i> Save Changes</button>
                </div>
            </div>
        </form>
    </div>
    @endif
</section>

@endsection
@section('footerscript')
<script>
    $(document).ready(function() {
        // Initialize select2
        $('.select2').select2();

        // When college is selected, filter programs
        $('#college_code, #manual_college_code').on('change', function() {
            var collegeCode = $(this).val();
            var targetSelect = $(this).attr('id') === 'college_code' ? '#program_code' : '#program1';

            if (collegeCode) {
                $.ajax({
                    url: '/ajax/superadmin/curriculum/filter_by_college',
                    type: 'GET',
                    data: { college_code: collegeCode },
                    success: function(data) {
                        $(targetSelect).empty();
                        $(targetSelect).append('<option value="">Select Program</option>');
                        $.each(data, function(key, value) {
                            $(targetSelect).append('<option value="' + value.program_code + '">' + value.program_code + ' - ' + value.program_name + '</option>');
                        });

                        // Refresh select2 if it's initialized
                        if ($.fn.select2) {
                            $(targetSelect).trigger('change');
                        }
                    }
                });
            } else {
                $(targetSelect).empty();
                $(targetSelect).append('<option value="">Select Program</option>');

                // Refresh select2 if it's initialized
                if ($.fn.select2) {
                    $(targetSelect).trigger('change');
                }
            }
        });

        // Dynamic row addition for manual entry
        var no = 1;
        $('.add').on('click', function(e) {
            if($("#c_year"+no).val()=="" || $("#code" + no).val()=="" || $("#name" + no).val()=="" ||
               $("#lec" + no).val()=="" || $("#lab" + no).val()=="" || $("#units" + no).val()=="") {
                toastr.warning("Please Fill-up Required Fields", 'Message!');
            } else {
                no++;
                $('#dynamic_field').append(`
                    <tr id='row${no}'>
                        <td><button type='button' class='btn btn-flat btn-danger remove' id='${no}'><i class='fa fa-close'></i></button></td>
                        <td><input type='text' name='curriculum_year[]' class='form-control' id='c_year${no}' required></td>
                        <td>
                            <select class='form-control' name='period[]' id='period${no}' required>
                                <option value='1st Trimester'>1st Trimester</option>
                                <option value='2nd Trimester'>2nd Trimester</option>
                                <option value='3rd Trimester'>3rd Trimester</option>
                                <option value='1st Semester'>1st Semester</option>
                                <option value='2nd Semester'>2nd Semester</option>
                                <option value='Summer'>Summer</option>
                            </select>
                        </td>
                        <td>
                            <select class='form-control' name='level[]' id='level${no}' required>
                                <option value='1st Year'>1st Year</option>
                                <option value='2nd Year'>2nd Year</option>
                                <option value='3rd Year'>3rd Year</option>
                                <option value='4th Year'>4th Year</option>
                                <option value='5th Year'>5th Year</option>
                            </select>
                        </td>
                        <td>
                            <select class='form-control select2' name='program_code[]' id='program${no}' required>
                                <option value=''>Select Program</option>
                                @foreach($programs as $program)
                                <option value='{{$program->program_code}}'>{{$program->program_code}} - {{$program->program_name}}</option>
                                @endforeach
                            </select>
                        </td>
                        <td><input type='text' class='form-control' name='course_code[]' id='code${no}' required></td>
                        <td><input type='text' class='form-control' name='course_name[]' id='name${no}' required></td>
                        <td><input type='text' class='form-control' name='lec[]' id='lec${no}' required></td>
                        <td><input type='text' class='form-control' name='lab[]' id='lab${no}' required></td>
                        <td><input type='text' class='form-control' name='units[]' id='units${no}' required></td>
                        <td>
                            <select name='complab[]' class='form-control' id='complab${no}'>
                                <option value='0'>No</option>
                                <option value='1'>Yes</option>
                            </select>
                        </td>
                    </tr>
                `);

                // Initialize select2 for the new row
                $(`#program${no}`).select2();
            }
            e.preventDefault();
            return false;
        });

        // Remove row
        $('#dynamic_field').on('click', '.remove', function(e) {
            var button_id = $(this).attr("id");
            $("#row" + button_id).remove();
            e.preventDefault();
            return false;
        });

        // If there's a hash in the URL, activate the corresponding tab
        var hash = window.location.hash;
        if (hash) {
            $('a[href="' + hash + '"]').tab('show');
        }

        // Change hash for page-reload
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            window.location.hash = e.target.hash;
        });
    });
</script>
@endsection
