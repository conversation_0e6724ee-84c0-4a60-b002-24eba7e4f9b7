<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\CtrSection;
use App\curriculum;
use App\offerings_infos_table;
use App\College;
use App\Services\ProgramService;

class CourseOfferingCurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    /**
     * Show the course offering curriculum management page
     */
    public function index()
    {
        $colleges = College::where('is_active', 1)->orderBy('college_code')->get();
        return view('super_admin.course_offering.curriculum.index', compact('colleges'));
    }

    /**
     * Show course offerings for a specific college
     */
    public function viewCollegeOfferings($college_code)
    {
        $college = College::where('college_code', $college_code)->first();
        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get programs for this college
        $programService = new ProgramService();
        $programs = $programService->getProgramsForCollege($college_code);

        return view('super_admin.course_offering.curriculum.college_offerings', compact('college', 'programs'));
    }

    /**
     * Show course offerings for a specific program
     */
    public function viewProgramOfferings($program_code)
    {
        // Find the program
        $programService = new ProgramService();
        $program = $programService->getProgramByCode($program_code);

        if (!$program) {
            Session::flash('error', "Program with code {$program_code} not found");
            return redirect('/superadmin/course_offerings/curriculum');
        }

        // Get curriculum years for this program
        $curriculum_years = curriculum::distinct()
            ->where('program_code', $program_code)
            ->pluck('curriculum_year')
            ->toArray();

        // Get active sections for this program
        $sections = CtrSection::where('program_code', $program_code)
            ->where('is_active', 1)
            ->get();

        return view('super_admin.course_offering.curriculum.program_offerings', compact('program', 'curriculum_years', 'sections'));
    }

    /**
     * Show curriculum details for a specific program and year
     */
    public function viewCurriculumDetails($program_code, $curriculum_year)
    {
        // Find the program
        $programService = new ProgramService();
        $program = $programService->getProgramByCode($program_code);

        if (!$program) {
            Session::flash('error', "Program with code {$program_code} not found");
            return redirect('/superadmin/course_offerings/curriculum');
        }

        // Get curriculum subjects for this program and year
        $curricula = curriculum::where('program_code', $program_code)
            ->where('curriculum_year', $curriculum_year)
            ->get();

        return view('super_admin.course_offering.curriculum.curriculum_details', compact('program', 'curriculum_year', 'curricula'));
    }

    /**
     * Show offerings for a specific section
     */
    public function viewSectionOfferings($section_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect('/superadmin/course_offerings/curriculum');
        }

        // Get offered curricula with curriculum and subject data
        $offerings = offerings_infos_table::where('section_name', $section->section_name)
            ->with('curriculum.subject')
            ->get();

        // Get available curricula that can be added
        $availableCurricula = $this->getAvailableCurricula($section);

        return view('super_admin.course_offering.curriculum.section_offerings', compact('section', 'offerings', 'availableCurricula'));
    }

    /**
     * Get available curricula for a section
     */
    private function getAvailableCurricula($section)
    {
        // Get all curricula for this program and level
        $level_map = [
            '1st Year' => '1st Year',
            '2nd Year' => '2nd Year',
            '3rd Year' => '3rd Year',
            '4th Year' => '4th Year',
        ];

        $level = $level_map[$section->level] ?? $section->level;

        // Get all curricula for this program
        $curricula = curriculum::where('program_code', $section->program_code)
            ->where('level', $level)
            ->get();

        // Get IDs of curricula already offered for this section
        $offered_ids = offerings_infos_table::where('section_name', $section->section_name)
            ->pluck('curriculum_id')
            ->toArray();

        // Filter out curricula already offered
        return $curricula->filter(function ($curriculum) use ($offered_ids) {
            return !in_array($curriculum->id, $offered_ids);
        });
    }

    /**
     * Add all available curriculum subjects to a section
     */
    public function addAllCurricula($section_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect('/superadmin/course_offerings/curriculum');
        }

        $availableCurricula = $this->getAvailableCurricula($section);
        $addedCount = 0;

        foreach ($availableCurricula as $curriculum) {
            $added = offerings_infos_table::addToOfferings(
                $curriculum->id,
                $section->section_name,
                $section->level
            );

            if ($added) {
                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            Session::flash('success', "Successfully added {$addedCount} curriculum subjects to {$section->section_name}");
            Log::info("User " . Auth::user()->name . " added {$addedCount} curriculum subjects to section {$section->section_name}");
        } else {
            Session::flash('info', "No new curriculum subjects to add to {$section->section_name}");
        }

        return redirect(url('/superadmin/course_offerings/curriculum/section', [$section_id]));
    }

    /**
     * Add selected curriculum subjects to a section
     */
    public function addSelectedCurricula(Request $request, $section_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect('/superadmin/course_offerings/curriculum');
        }

        // Validate the request
        $this->validate($request, [
            'curriculum_ids' => 'required|array',
            'curriculum_ids.*' => 'required|integer|exists:curricula,id'
        ]);

        $addedCount = 0;
        foreach ($request->curriculum_ids as $curriculum_id) {
            $added = offerings_infos_table::addToOfferings(
                $curriculum_id,
                $section->section_name,
                $section->level
            );

            if ($added) {
                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            Session::flash('success', "Successfully added {$addedCount} curriculum subjects to {$section->section_name}");
            Log::info("User " . Auth::user()->name . " added {$addedCount} curriculum subjects to section {$section->section_name}");
        } else {
            Session::flash('info', "No new curriculum subjects were added to {$section->section_name}");
        }

        return redirect(url('/superadmin/course_offerings/curriculum/section', [$section_id]));
    }

    /**
     * Remove a curriculum subject from a section's offerings
     */
    public function removeCurriculum($section_id, $offering_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect('/superadmin/course_offerings/curriculum');
        }

        $offering = offerings_infos_table::find($offering_id);
        if (!$offering) {
            Session::flash('error', 'Offering not found');
            return redirect(url('/superadmin/course_offerings/curriculum/section', [$section_id]));
        }

        // Check if offering belongs to this section
        if ($offering->section_name !== $section->section_name) {
            Session::flash('error', 'This offering does not belong to the specified section');
            return redirect(url('/superadmin/course_offerings/curriculum/section', [$section_id]));
        }

        // Get curriculum details for logging
        $curriculum = curriculum::find($offering->curriculum_id);
        $course_code = $curriculum ? $curriculum->course_code : 'Unknown';

        // Delete the offering
        $offering->delete();

        Session::flash('success', "Successfully removed {$course_code} from {$section->section_name}");
        Log::info("User " . Auth::user()->name . " removed {$course_code} from section {$section->section_name}");

        return redirect(url('/superadmin/course_offerings/curriculum/section', [$section_id]));
    }
}
