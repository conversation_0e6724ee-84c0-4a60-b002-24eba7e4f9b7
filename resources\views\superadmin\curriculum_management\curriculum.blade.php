<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}


?>
@extends($layout)
@section('messagemenu')
<li class="dropdown messages-menu">
            <!-- Menu toggle button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-envelope-o"></i>
              <span class="label label-success"></span>
            </a>
</li>
<li class="dropdown notifications-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-bell-o"></i>
              <span class="label label-warning"></span>
            </a>
</li>

<li class="dropdown tasks-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="label label-danger"></span>
            </a>
</li>
@endsection
@section('header')
<section class="content-header">
    <h1><i class="fa fa-folder"></i>
        View Curriculum
        <small></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li class="active"><a>View Curriculum</a></li>
    </ol>
</section>
@endsection
@section('main-content')
<link rel="stylesheet" href="{{ asset ('plugins/toastr/toastr.css')}}">
@if(Session::has('success'))
<div class='col-sm-12'>
    <div class='callout callout-success'>
        {{Session::get('success')}}
    </div>
</div>
@endif


<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-primary" id="curriculum-container">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Management</h3>
                    <div class="box-tools pull-right">
                        <a href="{{url('/superadmin/curriculum_management/colleges')}}" class="btn btn-flat btn-primary"><i class="fa fa-university"></i> View by College</a>
                        <a href="{{url('/superadmin/college')}}" class="btn btn-flat btn-info"><i class="fa fa-cog"></i> Manage Colleges</a>
                        <a href="{{url('/superadmin/curriculum_management/add_curriculum')}}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> New Curriculum</a>
                        <a href="{{url('/superadmin/curriculum_management/fix_course_names')}}" class="btn btn-flat btn-warning"><i class="fa fa-wrench"></i> Fix Course Names</a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="college-filter">Filter by College:</label>
                                <div class="input-group">
                                    <select class="form-control select2" id="college-filter">
                                        <option value="">All Colleges</option>
                                        @foreach($colleges as $college)
                                            <option value="{{ $college->college_code }}">{{ $college->college_code }} - {{ $college->college_name }}</option>
                                        @endforeach
                                    </select>
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" id="search-filter" title="Search programs by college"><i class="fa fa-search"></i> Search</button>
                                        <button class="btn btn-default" type="button" id="clear-filter" title="Clear filter (ESC)"><i class="fa fa-times"></i> Clear</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results container will be inserted here -->
                    <div id="search-results-container" style="display: none;">
                        <hr>
                        <h4 id="search-results-title">Search Results</h4>
                        <div class="table-responsive">
                            <table id="programs-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Program Name</th>
                                        <th>Curriculum Years</th>
                                        <th>Total Courses</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Results will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Colleges and Programs Section -->
    @if(count($collegesWithPrograms) > 0)
        @foreach($collegesWithPrograms as $collegeData)
        <div class="row college-section" data-college-code="{{ $collegeData['college']->college_code }}" data-college-name="{{ $collegeData['college']->college_name }}">
            <div class="col-sm-12">
                <div class="box box-default">
                    <div class="box-header">
                        <h3 class="box-title">{{ $collegeData['college']->college_name }} ({{ $collegeData['college']->college_code }})</h3>
                        <div class="box-tools pull-right">
                            <a href="{{url('/superadmin/curriculum_management/college', [$collegeData['college']->college_code])}}" class="btn btn-flat btn-info"><i class="fa fa-graduation-cap"></i> View All Programs</a>
                            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class='table-responsive'>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Program Name</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($collegeData['programs'] as $program)
                                    <tr>
                                        <td>{{ $program['program_name'] }} <small class="text-muted">({{ $program['program_code'] }})</small></td>
                                        <td class="text-center">
                                            <a href="{{url('/superadmin/curriculum_management/view_curriculums', [$program['program_code']])}}" class="btn btn-flat btn-primary" title="View Curricula"><i class="fa fa-eye"></i></a>
                                            <a href="#" class="btn btn-flat btn-success" data-toggle="modal" data-target="#exampleModal" onclick="setProgram('{{ $program['program_code'] }}', '{{ $program['program_name'] }}', '{{ $collegeData['college']->college_code }}')" title="Add Course"><i class="fa fa-plus"></i></a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    @else
        <div class="row">
            <div class="col-sm-12">
                <div class="box box-default">
                    <div class="box-body">
                        <div class="alert alert-info">
                            <h4><i class="icon fa fa-info"></i> No Programs Found</h4>
                            <p>There are no programs associated with any colleges. Please add programs to colleges first.</p>
                            <a href="{{url('/superadmin/college')}}" class="btn btn-flat btn-info"><i class="fa fa-cog"></i> Manage Colleges</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Legacy View (All Programs) -->
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-default collapsed-box">
                <div class="box-header">
                    <h3 class="box-title">All Academic Programs</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <div class='table-responsive'>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Program Name</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($programs as $program)
                                <tr>
                                    <td>{{$program->program_name}} <small class="text-muted">({{$program->program_code}})</small></td>
                                    <td class="text-center"><a href="{{url('/superadmin/curriculum_management/view_curriculums', [$program->program_code])}}" class="btn btn-flat btn-primary"><i class="fa fa-eye"></i></a></td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
        <form id="curriculum_form" action="{{url('/superadmin/curriculum_management/curriculum/addcourse')}}" method="post">
            {{csrf_field()}}
      <div class="modal-header bg-primary">
        <h5 class="modal-title" id="exampleModalLabel">ADD COURSE TO CURRICULUM</h5>
      </div>
      <div class="modal-body">
          <?php $programs= \App\academic_programs::distinct()->get(['program_name','program_code']);?>
          <div class="form-group">


              <label>Academic Program</label>
              <select name="program_code" id="program" class="form-control select2" style="width:100%">
                  <option>Please Select</option>
                  @foreach($programs as $program)
                  <option value="{{$program->program_code}}">{{$program->program_code}} - {{$program->program_name}}</option>
                  @endforeach
              </select>
          </div>
          <div class="form-group">
              <label>Course</label>
              <div class="row">
                  <div class="col-sm-4">
                      <input type="text" class="form-control" name="course_code" placeholder="Course Code">
                  </div>
                  <div class="col-sm-8">
                      <input type="text" class="form-control" name="course_name" placeholder="Course Name">
                  </div>
              </div>
          </div>

          <div class="form-group" id="displaycurriculum">
              <div class="row">
                  <div class="col-sm-4">
                      <label>Curriculum Year</label>
                      <input type="text" name="curriculum_year" class="form-control">
                  </div>
                  <div class="col-sm-4">
                      <label>Level</label>
                      <select name="level" class="form-control">
                          <option>Please Select</option>
                          <option>1st Year</option>
                          <option>2nd Year</option>
                          <option>3rd Year</option>
                          <option>4th Year</option>
                          <option>5th Year</option>
                      </select>
                  </div>
                  <div class="col-sm-4">
                      <label>Period</label>
                      <select name="period" class="form-control">
                          <option>Please Select</option>
                          <option>1st Trimester</option>
                          <option>2nd Trimester</option>
                      </select>
                  </div>
              </div>
              <div class="row" style="margin-top:10px">
                  <div class="col-sm-4">
                    <div class="form form-group">
                        <label>Lec</label>
                        <input type="text" id="lec" name="lec" class="form-control">
                    </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form form-group">
                            <label>Lab</label>
                            <input type="text" id="lab" name="lab" class="form-control">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form form-group">
                            <label>Units</label>
                            <input type="text" id="units" name="units" class="form-control">
                        </div>
                    </div>
              </div>
          </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default btn-flat" data-dismiss="modal">Close</button>
        <button type="submit" class="btn btn-primary btn-flat">Save changes</button>
      </div>
    </form>
    </div>
  </div>
</div>
@endsection
@section('footer-script')

@if(Session::has('success'))
<script type="text/javascript">
    toastr.success(' <?php echo Session::get('success'); ?>', 'Message!');
</script>
@endif

<script>
    // Function to set program and college in the modal form
    function setProgram(programCode, programName, collegeCode) {
        // Set the program dropdown value
        $('#program').val(programCode);
        // If using select2, trigger change event
        if ($.fn.select2) {
            $('#program').trigger('change');
        }

        // Add hidden field for college code if it doesn't exist
        if ($('#college_code_input').length === 0) {
            $('<input>').attr({
                type: 'hidden',
                id: 'college_code_input',
                name: 'college_code',
                value: collegeCode
            }).appendTo('#curriculum_form');
        } else {
            // Update existing field
            $('#college_code_input').val(collegeCode);
        }
    }

    $(document).ready(function() {
        // Initialize select2 if available
        if ($.fn.select2) {
            $('.select2').select2();
        }

        // Initialize tooltips
        if ($.fn.tooltip) {
            $('[title]').tooltip();
        }

        // Initialize select2 for college filter
        $('#college-filter').select2({
            width: '100%',
            placeholder: 'Select a college',
            allowClear: true
        });

        // Function to perform the search
        function performSearch() {
            var selectedCollegeCode = $('#college-filter').val();

            if (selectedCollegeCode) {
                // Show loading indicator
                $('#search-results-container').hide();
                if ($('#loading-indicator').length === 0) {
                    $('<div id="loading-indicator" class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i> Loading programs...</div>').appendTo('#curriculum-container .box-body');
                }

                // Hide all college sections
                $('.college-section').hide();

                // Use AJAX to get programs for the selected college
                $.ajax({
                    url: '/ajax/superadmin/curriculum/filter_programs',
                    type: 'GET',
                    data: { college_code: selectedCollegeCode },
                    dataType: 'json',
                    success: function(programs) {
                        // Remove loading indicator
                        $('#loading-indicator').remove();

                        // Update the search results title
                        $('#search-results-title').text('Programs in ' + selectedCollegeCode);

                        // Clear the table
                        $('#programs-table tbody').empty();

                        // Add programs to the table
                        if (programs.length > 0) {
                            $.each(programs, function(index, program) {
                                // Create curriculum years display
                                var curriculumYearsHtml = '';
                                if (program.curriculum_years && program.curriculum_years.length > 0) {
                                    curriculumYearsHtml += '<ul class="list-unstyled">';
                                    $.each(program.curriculum_years, function(i, year) {
                                        var courseCount = program.course_counts[year] || 0;
                                        curriculumYearsHtml += '<li><a href="/superadmin/curriculum_management/list_curriculum/' + program.program_code + '/' + year + '">' + year + '</a> <span class="badge">' + courseCount + ' courses</span></li>';
                                    });
                                    curriculumYearsHtml += '</ul>';
                                } else {
                                    curriculumYearsHtml = '<span class="text-muted">No curriculum years</span>';
                                }

                                // Create action buttons
                                var actions = '<a href="/superadmin/curriculum_management/view_curriculums/' + program.program_code + '" class="btn btn-sm btn-primary"><i class="fa fa-eye"></i> View</a>';

                                // Add row to table
                                $('#programs-table tbody').append('<tr><td>' + program.program_name + ' <small class="text-muted">(' + program.program_code + ')</small></td><td>' + curriculumYearsHtml + '</td><td>' + (program.total_courses || 0) + '</td><td class="text-center">' + actions + '</td></tr>');
                            });

                            // Show the search results container
                            $('#search-results-container').show();

                            // Remove any no results message
                            $('#no-results-message').remove();
                        } else {
                            // Show no results message
                            if ($('#no-results-message').length === 0) {
                                $('<div id="no-results-message" class="alert alert-info"><i class="fa fa-info-circle"></i> No programs found for the selected college.</div>').appendTo('#curriculum-container .box-body');
                            }
                        }
                    },
                    error: function() {
                        // Remove loading indicator
                        $('#loading-indicator').remove();

                        // Show error message
                        if ($('#error-message').length === 0) {
                            $('<div id="error-message" class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> Error loading programs. Please try again.</div>').appendTo('#curriculum-container .box-body');
                        }
                    }
                });
            } else {
                // If no college is selected (All Colleges), show original view
                $('.college-section').show();
                $('#search-results-container').hide();
                $('#loading-indicator, #no-results-message, #error-message').remove();
            }
        }

        // Search button click event
        $('#search-filter').on('click', function() {
            performSearch();
        });

        // Also allow pressing Enter in the select2 search box to trigger search
        $('#college-filter').on('select2:close', function(e) {
            // Check if Enter key was pressed
            if (e.originalEvent && e.originalEvent.keyCode === 13) {
                performSearch();
            }
        });

        // Add keyboard shortcut for clearing the filter (Escape key)
        $(document).on('keydown', function(e) {
            // If Escape key is pressed and we're not in a modal
            if (e.keyCode === 27 && $('.modal:visible').length === 0) {
                // Only trigger if we have search results showing
                if ($('#search-results-container').is(':visible') || $('#no-results-message').length > 0) {
                    $('#clear-filter').click();
                }
            }
        });

        // Clear filter button
        $('#clear-filter').on('click', function() {
            // Reset the select2 dropdown
            $('#college-filter').val(null).trigger('change');

            // Show all college sections
            $('.college-section').show();

            // Hide the search results container
            $('#search-results-container').hide();

            // Clear the search results table
            $('#programs-table tbody').empty();

            // Remove any messages or indicators
            $('#loading-indicator, #no-results-message, #error-message').remove();

            // Show a success message that fades out
            $('<div id="clear-success-message" class="alert alert-success"><i class="fa fa-check-circle"></i> Filter cleared successfully.</div>')
                .appendTo('#curriculum-container .box-body')
                .delay(1500)
                .fadeOut(500, function() {
                    $(this).remove();
                });

            // Scroll back to the top of the page
            $('html, body').animate({
                scrollTop: $('#curriculum-container').offset().top - 20
            }, 200);
        });
    });
</script>
@endsection
