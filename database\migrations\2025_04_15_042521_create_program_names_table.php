<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProgramNamesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_names', function (Blueprint $table) {
            $table->increments('id');
            $table->string('program_code');
            $table->string('program_name');
            $table->timestamps();

            $table->index('program_code');
        });

        // Migrate data from academic_programs if it exists
        if (Schema::hasTable('academic_programs')) {
            $programs = DB::table('academic_programs')->get();

            foreach ($programs as $program) {
                if (!empty($program->program_code) && !empty($program->program_name)) {
                    DB::table('program_names')->insert([
                        'program_code' => $program->program_code,
                        'program_name' => $program->program_name,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_names');
    }
}
