<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateAcademicProgramsReferences extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // This migration updates all references to academic_programs table to use the programs table instead
        
        // 1. Update curriculum table to use programs table
        $curricula = DB::table('curricula')->get();
        
        foreach ($curricula as $curriculum) {
            // Find the college
            $college = DB::table('colleges')
                ->where('college_code', $curriculum->college_code)
                ->first();
                
            if ($college) {
                // Check if this program exists in the programs table
                $program = DB::table('programs')
                    ->where('college_id', $college->id)
                    ->where('program_code', $curriculum->program_code)
                    ->first();
                    
                if (!$program) {
                    // Add the program to the programs table
                    DB::table('programs')->insert([
                        'college_id' => $college->id,
                        'program_code' => $curriculum->program_code,
                        'program_name' => $curriculum->program_name ?? $curriculum->program_code
                    ]);
                }
            }
        }
        
        // 2. Update sections table to use programs table
        if (Schema::hasTable('ctr_sections')) {
            $sections = DB::table('ctr_sections')->get();
            
            foreach ($sections as $section) {
                // Find the program in academic_programs
                $academicProgram = DB::table('academic_programs')
                    ->where('program_code', $section->program_code)
                    ->first();
                    
                if ($academicProgram && $academicProgram->college_code) {
                    // Find the college
                    $college = DB::table('colleges')
                        ->where('college_code', $academicProgram->college_code)
                        ->first();
                        
                    if ($college) {
                        // Check if this program exists in the programs table
                        $program = DB::table('programs')
                            ->where('college_id', $college->id)
                            ->where('program_code', $section->program_code)
                            ->first();
                            
                        if (!$program) {
                            // Add the program to the programs table
                            DB::table('programs')->insert([
                                'college_id' => $college->id,
                                'program_code' => $section->program_code,
                                'program_name' => $academicProgram->program_name ?? $section->program_code
                            ]);
                        }
                    }
                }
            }
        }
        
        // 3. Update any other tables that reference academic_programs
        // (Add more tables as needed)
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // No need to reverse these changes as they are data migrations
    }
}
