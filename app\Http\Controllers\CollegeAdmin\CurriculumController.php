<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\College;
use App\curriculum;
use App\Program;
use App\Subject;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class CurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Display a listing of curricula for the college
     */
    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        // Get programs for this college
        $programs = $college->getPrograms();

        return view('collegeadmin.curriculum.index', compact('college', 'programs'));
    }

    /**
     * View curricula for a specific program
     */
    public function viewCurricula($programCode)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Verify the program belongs to this college
        $college = College::where('college_code', $collegeCode)->firstOrFail();
        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Get distinct curriculum years for this program
        $curriculumYears = curriculum::where('program_code', $programCode)
            ->where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->distinct()
            ->orderBy('curriculum_year', 'desc')
            ->pluck('curriculum_year');

        return view('collegeadmin.curriculum.view_curricula', compact('programCode', 'curriculumYears', 'college'));
    }

    /**
     * List curriculum subjects for a specific program and year
     */
    public function listCurriculum($programCode, $curriculumYear)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Verify the program belongs to this college
        $college = College::where('college_code', $collegeCode)->firstOrFail();
        if (!$college->hasProgram($programCode)) {
            abort(403, 'Unauthorized action.');
        }

        // Get curriculum subjects
        $curriculum = curriculum::where('program_code', $programCode)
            ->where('curriculum_year', $curriculumYear)
            ->where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->orderBy('level')
            ->orderBy('period')
            ->orderBy('control_code') // Use control_code instead of course_code
            ->get();

        // Create a mapping of subject codes to descriptive names for specific programs
        $subjectNames = [
            // BSCS Program Subjects
            'CC101' => 'Introduction to Computing',
            'CC102' => 'Computer Programming 1',
            'EnviSci101' => 'Environmental Science',
            'MATH101' => 'College Algebra',
            'NSTP1' => 'National Service Training Program 1',
            'PATHFIT101' => 'Physical Fitness and Wellness',
            'PCOM101' => 'Professional Communication',
            'SocSci101' => 'Understanding the Self',

            // Add more mappings as needed for other subjects
        ];

        // Set course_code and course_name for each subject
        foreach ($curriculum as $subject) {
            // Use control_code as subject_code (displayed as Course Code)
            $subject->course_code = $subject->control_code;

            // Use a cascading approach to set the course_name:
            // 1. Keep existing course_name if it's not empty and not the default format
            // 2. Use the mapping if available
            // 3. Fall back to a default format
            if (empty($subject->course_name) || $subject->course_name == "Course: {$subject->control_code}") {
                $subject->course_name = $subjectNames[$subject->control_code] ?? "Course: {$subject->control_code}";
            }
        }

        // Debug: Log the first curriculum item to check if course_code and course_name are present
        if ($curriculum->count() > 0) {
            \Log::info('First curriculum item after fix:', $curriculum->first()->toArray());
        }

        return view('collegeadmin.curriculum.list_curriculum', compact('programCode', 'curriculumYear', 'curriculum', 'college'));
    }

    /**
     * Show the form for adding a new curriculum
     */
    public function create()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = College::where('college_code', $collegeCode)->firstOrFail();

        // Get programs for this college
        $programs = $college->getPrograms();

        return view('collegeadmin.curriculum.create', compact('college', 'programs'));
    }

    /**
     * Store a newly created curriculum
     */
    public function store(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Validate the request
        $request->validate([
            'program_code' => 'required|string|max:255',
            'curriculum_year' => 'required|string|max:255',
            'course_code' => 'required|array',
            'course_code.*' => 'required|string|max:255',
            'course_name' => 'required|array',
            'course_name.*' => 'required|string|max:255',
            'lec' => 'required|array',
            'lec.*' => 'required|numeric',
            'lab' => 'required|array',
            'lab.*' => 'required|numeric',
            'units' => 'required|array',
            'units.*' => 'required|numeric',
            'level' => 'required|array',
            'level.*' => 'required|numeric',
            'period' => 'required|array',
            'period.*' => 'required|numeric',
            'complab' => 'required|array',
            'complab.*' => 'required|numeric',
        ]);

        // Verify the program belongs to this college
        $college = College::where('college_code', $collegeCode)->firstOrFail();
        if (!$college->hasProgram($request->program_code)) {
            abort(403, 'Unauthorized action.');
        }

        DB::beginTransaction();
        try {
            // Get program name
            $program = Program::where('college_id', $college->id)
                ->where('program_code', $request->program_code)
                ->first();

            $programName = $program ? $program->program_name : $request->program_code;

            // Save curriculum subjects
            for ($x = 0; $x < count($request->course_code); $x++) {
                // Find or create the subject
                $subject = Subject::findOrCreateByCode(
                    $request->course_code[$x],
                    [
                        'course_name' => $request->course_name[$x],
                        'lec' => $request->lec[$x],
                        'lab' => $request->lab[$x],
                        'units' => $request->units[$x],
                        'is_complab' => $request->complab[$x]
                    ]
                );

                // Create curriculum entry
                $curricula = new curriculum();
                $curricula->curriculum_year = $request->curriculum_year;
                $curricula->program_code = $request->program_code;
                $curricula->program_name = $programName;
                $curricula->college_code = $collegeCode;
                $curricula->control_code = $request->course_code[$x];
                $curricula->course_code = $request->course_code[$x];
                $curricula->course_name = $request->course_name[$x];
                $curricula->lec = $request->lec[$x];
                $curricula->lab = $request->lab[$x];
                $curricula->units = $request->units[$x];
                $curricula->level = $request->level[$x];
                $curricula->period = $request->period[$x];
                $curricula->percent_tuition = 100;
                $curricula->is_complab = $request->complab[$x];
                $curricula->is_active = 1; // Set as active by default
                $curricula->subject_id = $subject->id; // Link to the subject
                $curricula->save();
            }

            DB::commit();
            Session::flash('success', 'Successfully saved curriculum data!');
            Log::info('User ' . Auth::user()->name . ' added a new curriculum for ' . $request->program_code);

            return redirect()->route('collegeadmin.curriculum.index');
        } catch (\Exception $e) {
            DB::rollback();
            Session::flash('error', 'Error saving curriculum: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Archive a curriculum subject
     */
    public function archiveSubject($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get the curriculum subject
        $subject = curriculum::where('id', $id)
            ->where('college_code', $collegeCode)
            ->firstOrFail();

        // Archive the subject
        $subject->is_active = 0;
        $subject->save();

        Session::flash('success', 'Subject archived successfully!');
        return redirect()->back();
    }

    /**
     * View archived subjects
     */
    public function archivedSubjects()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get archived subjects for this college
        $subjects = curriculum::where('college_code', $collegeCode)
            ->where('is_active', 0)
            ->orderBy('program_code')
            ->orderBy('curriculum_year')
            ->orderBy('level')
            ->orderBy('period')
            ->orderBy('control_code') // Use control_code instead of course_code
            ->get();

        return view('collegeadmin.curriculum.archived_subjects', compact('subjects'));
    }

    /**
     * Restore an archived subject
     */
    public function restoreSubject($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get the curriculum subject
        $subject = curriculum::where('id', $id)
            ->where('college_code', $collegeCode)
            ->firstOrFail();

        // Restore the subject
        $subject->is_active = 1;
        $subject->save();

        Session::flash('success', 'Subject restored successfully!');
        return redirect()->back();
    }

    /**
     * Fix curriculum data display
     */
    public function fixCurriculumData()
    {
        // Display a message to the user about the updated display
        Session::flash('info', "The system is now displaying the actual course codes and names from the database.");
        return redirect()->back();
    }

    /**
     * Edit a curriculum subject
     */
    public function editCurriculum(Request $request)
    {
        // Validate the request
        $request->validate([
            'curriculum_id' => 'required|integer',
            'course_code' => 'required|string|max:255',
            'course_name' => 'required|string|max:255',
            'lec' => 'required|numeric',
            'lab' => 'required|numeric',
            'units' => 'required|numeric',
            'complab' => 'nullable'
        ]);

        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get the curriculum subject
        $curriculum = curriculum::where('id', $request->curriculum_id)
            ->where('college_code', $collegeCode)
            ->firstOrFail();

        // Update the curriculum
        $curriculum->control_code = $request->course_code;
        $curriculum->course_code = $request->course_code;
        $curriculum->course_name = $request->course_name;
        $curriculum->lec = $request->lec;
        $curriculum->lab = $request->lab;
        $curriculum->units = $request->units;
        $curriculum->is_complab = $request->has('complab') ? 1 : 0;
        $curriculum->save();

        // Update the associated subject if it exists
        if ($curriculum->subject_id) {
            $subject = Subject::find($curriculum->subject_id);
            if ($subject) {
                $subject->course_code = $request->course_code;
                $subject->course_name = $request->course_name;
                $subject->lec = $request->lec;
                $subject->lab = $request->lab;
                $subject->units = $request->units;
                $subject->is_complab = $request->has('complab') ? 1 : 0;
                $subject->save();
            }
        }

        Session::flash('success', 'Successfully modified ' . $request->course_code);
        return redirect()->route('collegeadmin.curriculum.list_curriculum', [
            'program_code' => $curriculum->program_code,
            'curriculum_year' => $curriculum->curriculum_year
        ]);
    }
}
