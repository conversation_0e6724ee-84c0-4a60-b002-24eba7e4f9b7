<?php

// <PERSON><PERSON>vel's bootstrap file
require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/bootstrap/app.php';

// Get the application instance
$app = app();

// Boot the application
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Use the Schema and DB facades
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "Checking database tables...\n";

// Check if subjects table exists
if (Schema::hasTable('subjects')) {
    echo "Subjects table exists.\n";
    
    // Count subjects
    $subjectCount = DB::table('subjects')->count();
    echo "Number of subjects: " . $subjectCount . "\n";
    
    // Show a few subjects
    $subjects = DB::table('subjects')->limit(5)->get();
    echo "Sample subjects:\n";
    foreach ($subjects as $subject) {
        echo "- " . $subject->subject_code . ": " . $subject->subject_name . "\n";
    }
} else {
    echo "Subjects table does not exist.\n";
}

// Check if curricula table has subject_id column
if (Schema::hasColumn('curricula', 'subject_id')) {
    echo "Curricula table has subject_id column.\n";
    
    // Count curricula with subject_id
    $curriculaWithSubjectId = DB::table('curricula')
        ->whereNotNull('subject_id')
        ->count();
    echo "Number of curricula with subject_id: " . $curriculaWithSubjectId . "\n";
    
    // Show a few curricula with subject_id
    $curricula = DB::table('curricula')
        ->whereNotNull('subject_id')
        ->limit(5)
        ->get();
    echo "Sample curricula with subject_id:\n";
    foreach ($curricula as $curriculum) {
        echo "- Curriculum ID: " . $curriculum->id . ", Subject ID: " . $curriculum->subject_id . "\n";
    }
} else {
    echo "Curricula table does not have subject_id column.\n";
}

// Check migration status
$migration = DB::table('migrations')
    ->where('migration', '2025_06_01_000000_normalize_curriculum_data')
    ->first();
if ($migration) {
    echo "Migration '2025_06_01_000000_normalize_curriculum_data' has been run (Batch: " . $migration->batch . ").\n";
} else {
    echo "Migration '2025_06_01_000000_normalize_curriculum_data' has not been run.\n";
}
