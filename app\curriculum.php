<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class curriculum extends Model
{
    protected $table = 'curricula';

    protected $fillable = [
        'curriculum_year',
        'program_code',
        'program_name',
        'college_code',
        'control_code',
        'course_code',
        'course_name',
        'lec',
        'lab',
        'units',
        'level',
        'period',
        'is_complab',
        'is_active',
        'subject_id'
    ];

    /**
     * Get the college that owns this curriculum
     */
    public function college()
    {
        return $this->belongsTo('App\College', 'college_code', 'college_code');
    }

    /**
     * Get the program associated with this curriculum
     */
    public function program()
    {
        // First find the college
        $college = $this->college;

        if ($college) {
            // Then find the program in the programs table
            return Program::where('college_id', $college->id)
                ->where('program_code', $this->program_code)
                ->first();
        }

        return null;
    }

    /**
     * Get the academic program associated with this curriculum
     */
    public function academicProgram()
    {
        return $this->belongsTo('App\academic_programs', 'program_code', 'program_code');
    }

    /**
     * Get the subject associated with this curriculum
     */
    public function subject()
    {
        return $this->belongsTo('App\Subject', 'subject_id');
    }

    /**
     * Get the subject code (either from the subject relation or the course_code field)
     */
    public function getSubjectCodeAttribute()
    {
        // Try to get it from the subject relation
        if ($this->subject_id) {
            // Eager load the subject if not already loaded
            if (!$this->relationLoaded('subject')) {
                $this->load('subject');
            }

            if ($this->subject && $this->subject->subject_code) {
                return $this->subject->subject_code;
            }
        }

        // Check if course_code exists
        if (isset($this->attributes['course_code']) && !empty($this->attributes['course_code'])) {
            return $this->attributes['course_code'];
        }

        // Check if control_code exists as a fallback
        if (isset($this->attributes['control_code']) && !empty($this->attributes['control_code'])) {
            return $this->attributes['control_code'];
        }

        return 'Unknown';
    }

    /**
     * Get the subject name (either from the subject relation or the course_name field)
     */
    public function getSubjectNameAttribute()
    {
        // Try to get it from the subject relation
        if ($this->subject_id) {
            // Eager load the subject if not already loaded
            if (!$this->relationLoaded('subject')) {
                $this->load('subject');
            }

            if ($this->subject && $this->subject->subject_name) {
                return $this->subject->subject_name;
            }
        }

        // Check if course_name exists
        if (isset($this->attributes['course_name']) && !empty($this->attributes['course_name'])) {
            return $this->attributes['course_name'];
        }

        // Try to generate a name from the course code
        if (isset($this->attributes['course_code']) && !empty($this->attributes['course_code'])) {
            return "Course: " . $this->attributes['course_code'];
        }

        // Try to generate a name from the control code
        if (isset($this->attributes['control_code']) && !empty($this->attributes['control_code'])) {
            return "Course: " . $this->attributes['control_code'];
        }

        return 'Unnamed Course';
    }

    /**
     * Get the course code (for backward compatibility)
     * This ensures that $curriculum->course_code always works
     */
    public function getCourseCodeAttribute()
    {
        // If the attribute exists directly, return it
        if (isset($this->attributes['course_code']) && !empty($this->attributes['course_code'])) {
            return $this->attributes['course_code'];
        }

        // Try to get it from the subject relation
        if ($this->subject_id) {
            // Eager load the subject if not already loaded
            if (!$this->relationLoaded('subject')) {
                $this->load('subject');
            }

            if ($this->subject && $this->subject->subject_code) {
                return $this->subject->subject_code;
            }
        }

        // Fall back to control_code if available
        if (isset($this->attributes['control_code']) && !empty($this->attributes['control_code'])) {
            return $this->attributes['control_code'];
        }

        return 'Unknown';
    }

    /**
     * Get the course name (for backward compatibility)
     * This ensures that $curriculum->course_name always works
     */
    public function getCourseNameAttribute()
    {
        // Try to get it from the subject relation first (most reliable)
        if ($this->subject_id) {
            // Eager load the subject if not already loaded
            if (!$this->relationLoaded('subject')) {
                $this->load('subject');
            }

            if ($this->subject && $this->subject->subject_name) {
                return $this->subject->subject_name;
            }
        }

        // Try to find a subject with this course code
        $courseCode = null;
        if (isset($this->attributes['course_code']) && !empty($this->attributes['course_code'])) {
            $courseCode = $this->attributes['course_code'];
        } elseif (isset($this->attributes['control_code']) && !empty($this->attributes['control_code'])) {
            $courseCode = $this->attributes['control_code'];
        }

        if ($courseCode) {
            $subject = \App\Subject::where('subject_code', $courseCode)->first();
            if ($subject && $subject->subject_name) {
                // Update the subject_id only (don't try to update course_name)
                $this->subject_id = $subject->id;
                $this->save();
                return $subject->subject_name;
            }
        }

        // If we still don't have a name, use a more descriptive format
        if (isset($this->attributes['course_code']) && !empty($this->attributes['course_code'])) {
            return $this->attributes['course_code'] . " Course";
        }

        // Try to generate a name from the control code
        if (isset($this->attributes['control_code']) && !empty($this->attributes['control_code'])) {
            return $this->attributes['control_code'] . " Course";
        }

        return 'Unnamed Course';
    }
}


