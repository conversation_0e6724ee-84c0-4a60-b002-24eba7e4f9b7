<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixCtrSectionsCollegeCode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            // Check if the column already exists
            if (!Schema::hasColumn('ctr_sections', 'college_code')) {
                // Add the college_code column using raw SQL
                DB::statement('ALTER TABLE ctr_sections ADD COLUMN college_code VARCHAR(20) NULL AFTER section_name');
                echo "Added college_code column to ctr_sections table\n";
            } else {
                echo "college_code column already exists in ctr_sections table\n";
            }

            // Now populate the college_code for existing sections
            $this->populateCollegeCodes();

        } catch (\Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('ctr_sections', 'college_code')) {
            Schema::table('ctr_sections', function (Blueprint $table) {
                $table->dropColumn('college_code');
            });
        }
    }

    /**
     * Populate college_code for existing sections
     */
    private function populateCollegeCodes()
    {
        // Get all colleges
        $colleges = DB::table('colleges')->where('is_active', 1)->get();
        
        if ($colleges->isEmpty()) {
            echo "No active colleges found. Creating sample colleges...\n";
            $this->createSampleColleges();
            $colleges = DB::table('colleges')->where('is_active', 1)->get();
        }

        // Get sections without college_code
        $sectionsWithoutCollege = DB::table('ctr_sections')
            ->whereNull('college_code')
            ->get();

        if ($sectionsWithoutCollege->isEmpty()) {
            echo "All sections already have college_code assigned\n";
            return;
        }

        echo "Found " . $sectionsWithoutCollege->count() . " sections without college_code\n";

        // Assign college codes based on program codes or create default assignments
        foreach ($sectionsWithoutCollege as $section) {
            $collegeCode = $this->determineCollegeCode($section, $colleges);
            
            DB::table('ctr_sections')
                ->where('id', $section->id)
                ->update(['college_code' => $collegeCode]);
                
            echo "Assigned college_code '{$collegeCode}' to section '{$section->section_name}'\n";
        }
    }

    /**
     * Determine college code for a section
     */
    private function determineCollegeCode($section, $colleges)
    {
        // Try to match by program code
        foreach ($colleges as $college) {
            // Check if the college has programs that match this section's program
            $programs = json_decode($college->program_codes ?? '[]', true);
            if (is_array($programs) && in_array($section->program_code, $programs)) {
                return $college->college_code;
            }
        }

        // If no match found, assign to the first available college
        return $colleges->first()->college_code ?? 'DEFAULT';
    }

    /**
     * Create sample colleges if none exist
     */
    private function createSampleColleges()
    {
        $sampleColleges = [
            [
                'college_code' => 'CCS',
                'college_name' => 'College of Computer Studies',
                'program_codes' => json_encode(['BSIT', 'BSCS', 'BSIS']),
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'college_code' => 'COE',
                'college_name' => 'College of Engineering',
                'program_codes' => json_encode(['BSCE', 'BSEE', 'BSME']),
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'college_code' => 'CBA',
                'college_name' => 'College of Business Administration',
                'program_codes' => json_encode(['BSBA', 'BSA', 'BSHRM']),
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($sampleColleges as $college) {
            DB::table('colleges')->insert($college);
            echo "Created college: " . $college['college_name'] . "\n";
        }
    }
}
