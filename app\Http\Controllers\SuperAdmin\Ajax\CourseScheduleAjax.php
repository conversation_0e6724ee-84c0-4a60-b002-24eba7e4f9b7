<?php

namespace App\Http\Controllers\SuperAdmin\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Request;
use App\TimeBlock;
use App\College;
use App\curriculum;
use App\CtrRoom;
use App\CtrSection;
use App\offerings_infos_table;
use App\room_schedules;
use App\User;
use App\SchedulePriority;

class CourseScheduleAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    public function get_sections()
    {
        if (Request::ajax()) {
            $college_code = Input::get('college_code');
            $program_code = Input::get('program_code');
            $level = Input::get('level');

            // Start with all sections
            $query = CtrSection::query();

            // Apply filters if provided
            if ($college_code) {
                $query->where('college_code', $college_code);
            }

            if ($program_code) {
                $query->where('program_code', $program_code);
            }

            if ($level) {
                $query->where('level', $level);
            }

            // Get the filtered sections
            $sections = $query->get();

            return view('admin.course_schedule.ajax.get_sections', compact('sections'));
        }
    }

    public function get_courses_offered()
    {
        if (Request::ajax()) {
            $section_name = Input::get('section_name');
            $level = Input::get('level');
            $college_code = Input::get('college_code');
            $program_code = Input::get('program_code');

            // Log the request parameters for debugging
            \Log::info('Loading courses for section: ' . $section_name . ', college: ' . $college_code . ', program: ' . $program_code . ', level: ' . $level);

            // Get the section details
            try {
                $section = \App\CtrSection::where('section_name', $section_name)->first();
                if (!$section) {
                    \Log::warning('Section not found: ' . $section_name);
                }
            } catch (\Exception $e) {
                \Log::error('Error retrieving section ' . $section_name . ': ' . $e->getMessage());
                $section = null;
            }

            // Get courses offered for this section
            $courses = \App\offerings_infos_table::where('section_name', $section_name)->get();

            return view('admin.course_schedule.ajax.get_courses_offered', compact('courses', 'section_name', 'college_code', 'program_code', 'level'));
        }
    }

    public function get_rooms_available()
    {
        if (Request::ajax()) {
            $time_start = Input::get('time_start');
            $time_end = Input::get('time_end');
            $offering_id = Input::get('offering_id');
            $section_name = Input::get('section_name');
            $day_type = Input::get('day_type');

            // Check if we have multiple days
            $multiple_days = Input::get('multiple_days');
            $day = Input::get('day'); // Single day

            // Get the curriculum and college information
            $offering = offerings_infos_table::find($offering_id);
            $curriculum = curriculum::find($offering->curriculum_id);
            $college_code = $curriculum->college_code;

            // Get all rooms
            $rooms = CtrRoom::where('is_active', 1)->get();

            // Get all instructors
            $instructors = User::where('accesslevel', 1)->get();

            // Filter rooms based on availability
            $available_rooms = [];
            foreach ($rooms as $room) {
                // Check if the room is available for the selected time
                $is_available = true;

                // If we have multiple days, check availability for each day
                if ($multiple_days) {
                    $days = explode(',', $multiple_days);
                    foreach ($days as $day) {
                        if (room_schedules::hasRoomConflict($day, $time_start, $time_end, $room->room)) {
                            $is_available = false;
                            break;
                        }
                    }
                } else {
                    // Check for a single day
                    if (room_schedules::hasRoomConflict($day, $time_start, $time_end, $room->room)) {
                        $is_available = false;
                    }
                }

                if ($is_available) {
                    $available_rooms[] = $room;
                }
            }

            // Return the view with available rooms
            if ($multiple_days) {
                return view('admin.course_schedule.ajax.get_available_rooms', compact('available_rooms', 'instructors', 'offering_id', 'multiple_days', 'day_type', 'time_start', 'time_end', 'section_name'));
            } else {
                return view('admin.course_schedule.ajax.get_available_rooms', compact('available_rooms', 'instructors', 'offering_id', 'day', 'day_type', 'time_start', 'time_end', 'section_name'));
            }
        }
    }

    /**
     * Get time blocks for a specific day type
     */
    public function getTimeBlocks()
    {
        if (Request::ajax()) {
            $dayType = Input::get('day_type');
            $timeBlocks = TimeBlock::getByDayType($dayType);
            return response()->json($timeBlocks);
        }
    }

    /**
     * Get available instructors for a specific time slot
     */
    public function getAvailableInstructors()
    {
        if (Request::ajax()) {
            $day = Input::get('day');
            $timeStart = Input::get('time_start');
            $timeEnd = Input::get('time_end');
            $collegeCode = Input::get('college_code');

            // Get all instructors
            $query = User::where('accesslevel', 1);

            // Filter by college if provided
            if ($collegeCode) {
                $query->where(function ($q) use ($collegeCode) {
                    $q->where('college_code', $collegeCode)
                        ->orWhereHas('instructorInfo', function ($subq) use ($collegeCode) {
                            $subq->where('college', $collegeCode);
                        });
                });
            }

            $instructors = $query->get();

            // Filter out instructors who have conflicts
            $availableInstructors = [];
            foreach ($instructors as $instructor) {
                if (!room_schedules::hasInstructorConflict($day, $timeStart, $timeEnd, $instructor->id)) {
                    $availableInstructors[] = $instructor;
                }
            }

            return response()->json($availableInstructors);
        }
    }

    /**
     * Check for schedule conflicts
     */
    public function checkConflicts()
    {
        if (Request::ajax()) {
            $day = Input::get('day');
            $timeStart = Input::get('time_start');
            $timeEnd = Input::get('time_end');
            $room = Input::get('room');
            $instructorId = Input::get('instructor_id');
            $sectionName = Input::get('section_name');

            $conflicts = room_schedules::getAllConflicts(
                $day,
                $timeStart,
                $timeEnd,
                $room,
                $instructorId,
                $sectionName
            );

            return response()->json([
                'has_conflicts' => !empty($conflicts),
                'conflicts' => $conflicts
            ]);
        }
    }
}
