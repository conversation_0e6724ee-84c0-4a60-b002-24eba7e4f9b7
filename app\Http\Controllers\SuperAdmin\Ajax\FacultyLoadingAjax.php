<?php

namespace App\Http\Controllers\SuperAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Auth;

class FacultyLoadingAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    /**
     * Get programs by college code
     */
    public function get_programs_by_college(Request $request)
    {
        $collegeCode = $request->input('college_code');

        if (!$collegeCode) {
            return response()->json([]);
        }

        // Get college by code
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            return response()->json([]);
        }

        // Get programs for this college
        $programs = $college->getPrograms();

        return response()->json($programs);
    }

    /**
     * Get courses available for loading
     */
    public function courses_to_load(Request $request)
    {
        $level = $request->input('level');
        $instructor = $request->input('instructor');
        $programCode = $request->input('program_code');

        // Get the instructor's college code
        $instructorUser = \App\User::find($instructor);
        $collegeCode = $instructorUser ? $instructorUser->college_code : null;

        // Base query for room schedules
        $query = \App\room_schedules::distinct()
            ->where('is_active', 1)
            ->whereNull('instructor');

        // If we have a college code, filter by it
        if ($collegeCode) {
            $query->where(function ($q) use ($collegeCode) {
                $q->where('college_code', $collegeCode)
                    ->orWhereNull('college_code');
            });
        }

        // Get the offering IDs
        $courses = $query->get(['offering_id']);

        // If program code is specified, we'll filter the results in the view
        return view(
            'superadmin.faculty_loading.ajax.courses_to_load',
            compact('level', 'courses', 'programCode', 'collegeCode')
        );
    }

    /**
     * Get current load for an instructor
     */
    public function current_load(Request $request)
    {
        $instructor = $request->input('instructor');
        $level = $request->input('level');
        $programCode = $request->input('program_code');

        // Get instructor's college code
        $instructorUser = \App\User::find($instructor);
        $collegeCode = $instructorUser ? $instructorUser->college_code : null;

        // Base query for loads
        $loadsQuery = DB::table('curricula')
            ->join('offerings_infos', 'curricula.id', 'offerings_infos.curriculum_id')
            ->join('room_schedules', 'room_schedules.offering_id', 'offerings_infos.id')
            ->where('room_schedules.instructor', $instructor);

        // Filter by program if specified
        if ($programCode) {
            $loadsQuery->where('curricula.program_code', $programCode);
        }

        $loads = $loadsQuery->get();

        // Base query for tabular schedules
        $tabularQuery = \App\room_schedules::distinct()
            ->where('is_active', 1)
            ->where('instructor', $instructor);

        // Get offering IDs for tabular schedules
        $tabular_schedules = $tabularQuery->get(['offering_id']);

        // Base query for schedules
        $schedulesQuery = \App\room_schedules::where('is_active', 1)
            ->where('instructor', $instructor);

        // Get all schedules
        $schedules = $schedulesQuery->get();

        return view(
            'superadmin.faculty_loading.ajax.current_load',
            compact('schedules', 'instructor', 'level', 'tabular_schedules', 'loads', 'programCode', 'collegeCode')
        );
    }

    /**
     * Add faculty load
     */
    public function add_faculty_load(Request $request)
    {
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');

        $loads = DB::table('curricula')
            ->join('offerings_infos', 'curricula.id', 'offerings_infos.curriculum_id')
            ->join('room_schedules', 'room_schedules.offering_id', 'offerings_infos.id')
            ->where('room_schedules.instructor', $instructor)
            ->get();

        $load_units = \App\UnitsLoad::where('instructor_id', $instructor)->get();

        if ($loads->sum('units') >= $load_units->sum('units')) {
            abort(404);
        }

        $schedules = \App\room_schedules::where('offering_id', $offering_id)->get();

        foreach ($schedules as $schedule) {
            $conflicts = \App\room_schedules::where('instructor', $instructor)
                ->where('day', $schedule->day)
                ->where(function ($query) use ($schedule) {
                    $query->whereBetween('time_starts', [$schedule->time_starts, $schedule->time_end])
                        ->orWhereBetween('time_end', [$schedule->time_starts, $schedule->time_end])
                        ->orWhere(function ($q) use ($schedule) {
                            $q->where('time_starts', '<=', $schedule->time_starts)
                                ->where('time_end', '>=', $schedule->time_end);
                        });
                })
                ->get();

            if ($conflicts->count() > 0) {
                abort(500);
            }
        }

        foreach ($schedules as $schedule) {
            $schedule->instructor = $instructor;
            $schedule->is_loaded = 1;
            $schedule->save();
        }
    }

    /**
     * Remove faculty load
     */
    public function remove_faculty_load(Request $request)
    {
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');

        $schedules = \App\room_schedules::where('instructor', $instructor)
            ->where('offering_id', $offering_id)
            ->get();

        if (!$schedules->isEmpty()) {
            foreach ($schedules as $schedule) {
                $schedule->instructor = null;
                $schedule->is_loaded = 0;
                $schedule->save();
            }
        }
    }

    /**
     * Search courses
     */
    public function search_courses(Request $request)
    {
        $value = $request->input('value');
        $level = $request->input('level');
        $programCode = $request->input('program_code');

        // Base query for curriculum
        $query = \App\curriculum::where('course_code', 'like', "%$value%");

        // Filter by program if specified
        if ($programCode) {
            $query->where('program_code', $programCode);
        }

        $curriculum = $query->get();

        return view(
            'superadmin.faculty_loading.ajax.search_courses',
            compact('curriculum', 'level', 'programCode')
        );
    }

    /**
     * Get units loaded
     */
    public function get_units_loaded(Request $request)
    {
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');
        $level = $request->input('level');

        $type = \App\instructors_infos::where('instructor_id', $instructor)->first()->employee_type;
        $units = \App\UnitsLoad::where('instructor_id', $instructor)->first()->units;

        $tabular_schedules = \App\room_schedules::distinct()
            ->where('is_active', 1)
            ->where('instructor', $instructor)
            ->get(['offering_id']);

        return view('superadmin.faculty_loading.ajax.get_units_loaded', compact('instructor', 'tabular_schedules', 'type', 'units', 'offering_id', 'level'));
    }

    /**
     * Override add faculty load
     */
    public function override_add(Request $request)
    {
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');
        $override = $request->input('override');

        if ($override == 1) {
            $schedules = \App\room_schedules::where('offering_id', $offering_id)->get();

            foreach ($schedules as $schedule) {
                $conflicts = \App\room_schedules::where('instructor', $instructor)
                    ->where('day', $schedule->day)
                    ->where(function ($query) use ($schedule) {
                        $query->whereBetween('time_starts', [$schedule->time_starts, $schedule->time_end])
                            ->orWhereBetween('time_end', [$schedule->time_starts, $schedule->time_end])
                            ->orWhere(function ($q) use ($schedule) {
                                $q->where('time_starts', '<=', $schedule->time_starts)
                                    ->where('time_end', '>=', $schedule->time_end);
                            });
                    })
                    ->get();

                if ($conflicts->count() > 0) {
                    abort(500);
                }
            }

            foreach ($schedules as $schedule) {
                $schedule->instructor = $instructor;
                $schedule->is_loaded = 1;
                $schedule->save();
            }
        }
    }
}
