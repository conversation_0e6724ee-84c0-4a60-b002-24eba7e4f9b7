<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}


?>
<?php
// Program is now passed from the controller
// This is just a fallback in case it's not
if (!isset($program) || $program === null) {
    $program = \App\ProgramHelper::getProgramByCode($program_code);

    if (!$program) {
        // Create a basic program object
        $program = (object) [
            'program_code' => $program_code,
            'program_name' => $program_code
        ];
    }
}
?>
@extends($layout)
@section('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection
@section('messagemenu')
<li class="dropdown messages-menu">
            <!-- Menu toggle button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-envelope-o"></i>
              <span class="label label-success"></span>
            </a>
</li>
<li class="dropdown notifications-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-bell-o"></i>
              <span class="label label-warning"></span>
            </a>
</li>

<li class="dropdown tasks-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="label label-danger"></span>
            </a>
</li>
@endsection
@section('header')
<section class="content-header">
    <h1><i class="fa  fa-folder-o"></i>
        View Curriculum
        <small></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li class="active"><a>View Curriculum</a></li>
    </ol>
</section>
@endsection
@section('main-content')
<section class="content">
    <div class="row">
        <div class="col-sm-12" id="displaycurriculum">
            <div class="box box-default">
                <div class="box-header">
                    <h3 class="box-title">{{$program->program_name}} {{ isset($showArchived) && $showArchived ? '(Archived)' : '' }}</h3>
                    <div class="box-tools pull-right">
                        <a href="javascript:void(0)" onclick="viewArchivedCurricula('{{$program_code}}')" class="btn btn-flat btn-default">
                            @if(isset($showArchived) && $showArchived)
                                <i class="fa fa-list"></i> View Active
                            @else
                                <i class="fa fa-archive"></i> View Archived
                            @endif
                        </a>
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <div class='table-responsive'>
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Curriculum Year</th>
                                <th class="text-center" width="30%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($curricula as $curriculum)
                            <tr>
                                <td>{{$curriculum->curriculum_year}} - {{is_numeric($curriculum->curriculum_year) ? (int)$curriculum->curriculum_year + 1 : $curriculum->curriculum_year}}</td>
                                <td class="text-center">
                                    <a href="{{url('/superadmin/curriculum_management/list_curriculum', [$program_code, $curriculum->curriculum_year])}}" class="btn btn-flat btn-success" title="View Curriculum"><i class="fa fa-eye"></i></a>
                                    <a onclick="displayedityear('{{$curriculum->curriculum_year}}','{{$program_code}}')" class="btn btn-flat btn-primary" title="Edit Curriculum Year"><i class="fa fa-pencil"></i></a>
                                    <a onclick="archiveCurriculum('{{$curriculum->curriculum_year}}','{{$program_code}}')" class="btn btn-flat {{ isset($showArchived) && $showArchived ? 'btn-success' : 'btn-danger' }}" title="{{ isset($showArchived) && $showArchived ? 'Restore Curriculum' : 'Archive Curriculum' }}">
                                        <i class="fa {{ isset($showArchived) && $showArchived ? 'fa-recycle' : 'fa-archive' }}"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
        </div>
        <div id="editmodal" class="modal fade" role="dialog">
            <div id="displayedit"></div>
        </div>
    </div>
</section>

@endsection
@section('footer-script')
<script>
    function displayedityear(curriculum_year,program_code){
        var array = {};
        array['curriculum_year'] = curriculum_year;
        array['program_code'] = program_code;
        $.ajax({
            type: "GET",
            url: "/superadmin/curriculum_management/ajax/edityear",
            data: array,
            success: function(data){
                $('#displayedit').html(data).fadeIn();
                $('#editmodal').modal('show');

                toastr.clear();
            }, error: function(){
                toastr.error('Something Went Wrong!', 'Error!');
            }
        })
    }

    function refreshtable(curriculum_year,program_code){
        var array = {};
        array['curriculum_year'] = curriculum_year;
        array['program_code'] = program_code;
        $.ajax({
            type: "GET",
            url: "/superadmin/curriculum_management/ajax/refreshcurriculum",
            data: array,
            success: function(data){
                $('#displaycurriculum').html(data).fadeIn();
            }, error: function(){
                toastr.error('Something Went Wrong!', 'Error!');
            }
        })
    }

    function archiveCurriculum(curriculum_year, program_code){
        if (confirm('Are you sure you want to archive this curriculum? This will hide it from the active list.')) {
            var array = {};
            array['curriculum_year'] = curriculum_year;
            array['program_code'] = program_code;
            $.ajax({
                type: "POST",
                url: "/superadmin/curriculum_management/archive_curriculum",
                data: array,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(data){
                    if (data.success) {
                        toastr.success(data.message, 'Success!');
                        // Refresh the table to show the updated list
                        location.reload();
                    } else {
                        toastr.error(data.message, 'Error!');
                    }
                }, error: function(){
                    toastr.error('Something Went Wrong!', 'Error!');
                }
            });
        }
    }

    function viewArchivedCurricula(program_code){
        // Get the current button
        var button = $('a[onclick="viewArchivedCurricula(\''+program_code+'\')"');

        // Check if we're currently showing archived curricula
        var showArchived = button.find('i').hasClass('fa-list') ? true : false;

        // Toggle the value
        showArchived = !showArchived;

        var array = {};
        array['program_code'] = program_code;
        array['show_archived'] = showArchived ? 1 : 0;

        $.ajax({
            type: "GET",
            url: "/superadmin/curriculum_management/view_curriculums/" + program_code,
            data: array,
            success: function(data){
                $('#displaycurriculum').html(data).fadeIn();

                // Update the page title to reflect the current view
                var title = "Curriculum - " + program_code;
                if (showArchived) {
                    title += " (Archived)";
                }
                document.title = title;
            }, error: function(){
                toastr.error('Something Went Wrong!', 'Error!');
            }
        });
    }
</script>
@endsection
