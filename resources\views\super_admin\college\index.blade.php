<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}

?>

@extends($layout)

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-4">
            <div class="box box-primary">
                <div class="box-header">
                    <h5 class="box-title">Add New College</h5>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                    <div class="alert alert-success alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <h4><i class="icon fa fa-check"></i> Success!</h4>
                        {{ Session::get('success') }}
                    </div>
                    @endif

                    @if(count($errors) > 0)
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <h4><i class="icon fa fa-ban"></i> Error!</h4>
                        <ul>
                            @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <form method="post" action="{{ url('/superadmin/college/post') }}">
                        {{ csrf_field() }}
                        <div class="form-group">
                            <label>College Code</label>
                            <input type="text" name="college_code" class="form-control" placeholder="Enter College Code" value="{{ old('college_code') }}" required>
                        </div>
                        <div class="form-group">
                            <label>College Name</label>
                            <input type="text" name="college_name" class="form-control" placeholder="Enter College Name" value="{{ old('college_name') }}" required>
                        </div>
                        <div class="form-group">
                            <label>Programs</label>
                            <select name="program_code[]" class="form-control select2" multiple="multiple" data-placeholder="Select or enter programs offered by this college">
                                @if(old('program_code'))
                                    @if(is_array(old('program_code')))
                                        @foreach(old('program_code') as $course)
                                            @if(trim($course) != '')
                                                <option value="{{ trim($course) }}" selected>{{ trim($course) }}</option>
                                            @endif
                                        @endforeach
                                    @else
                                        @foreach(explode(',', old('program_code')) as $course)
                                            @if(trim($course) != '')
                                                <option value="{{ trim($course) }}" selected>{{ trim($course) }}</option>
                                            @endif
                                        @endforeach
                                    @endif
                                @endif
                            </select>
                            <small class="text-muted">Enter multiple programs separated by commas <strong>( , )</strong></small>
                            <!--<small class="text-muted">Note: You can also manage programs in detail after creating the college.</small> -->
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-flat">Save</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="box box-primary">
                <div class="box-header">
                    <h5 class="box-title">List of Colleges</h5>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/superadmin/college/archive') }}" class="btn btn-flat btn-danger">
                        <i class="fa fa-warning"></i> Archive Colleges</a>
                    </div>
                </div>
                <hr>
                <div class="box-body">
                    <!-- Search and Filter Section -->
                    <div class="row" style="margin-bottom: 15px;">
                        <div class="col-md-12">
                            <div class="box box-solid box-default">
                                <div class="box-header with-border">
                                    <h3 class="box-title"><i class="fa fa-search"></i> Search & Filter</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>College Code</label>
                                                <select class="form-control select2-code" id="search-code" style="width: 100%;">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>College Name</label>
                                                <select class="form-control select2-name" id="search-name" style="width: 100%;"></select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>Program <span id="courses-filter-indicator" class="text-muted" style="display: none;">(Filtered by college)</span></label>
                                                <select class="form-control select2-courses" id="search-description" style="width: 100%;"></select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <button type="button" id="btn-search" class="btn btn-primary btn-flat"><i class="fa fa-search"></i> Search</button>
                                            <button type="button" id="btn-reset" class="btn btn-default btn-flat"><i class="fa fa-refresh"></i> Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Search and Filter Section -->

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="colleges-table">
                            <thead>
                                <tr>
                                    <th width='15%'>College Code</th>
                                    <th width='25%'>College Name</th>
                                    <th width='40%'>Programs</th>
                                    <th width='20%'>Action</th>
                                </tr>
                            </thead>
                            <tbody id="colleges-table-body">
                                @if(!$colleges->isEmpty())
                                @foreach($colleges as $college)
                                <tr>
                                    <td>{{ $college->college_code }}</td>
                                    <td>{{ $college->college_name }}</td>
                                    <td>
                                        @if(count($college->courses_array) > 0)
                                            <ul class="list-unstyled">
                                                @foreach($college->courses_array as $course)
                                                    <li><span class="label label-info">{{ $course }}</span></li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <span class="text-muted">No programs specified</span>
                                        @endif
                                    </td>
                                    <td>
                                        <button data-toggle="modal" data-target="#myModal" onclick="editcollege('{{ $college->id }}')" title="Edit Record" class="btn btn-flat btn-primary"><i class="fa fa-pencil"></i></button>
                                        <a href="{{ url('/superadmin/curriculum_management/college', [$college->college_code]) }}" class="btn btn-flat btn-info" title="View Curricula"><i class="fa fa-graduation-cap"></i></a>
                                        <!--<button onclick="syncPrograms('{{ $college->id }}')" title="Sync Programs" class="btn btn-flat btn-success"><i class="fa fa-refresh"></i></button> -->
                                        <a href="{{ url('/superadmin/college/archive', [$college->id]) }}" class="btn btn-flat btn-danger" title="Change to Inactive Status?" onclick="return confirm('Do you wish to archive the Record?')"><i class="fa fa-times"></i></a>
                                    </td>
                                </tr>
                                @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Edit College</h4>
            </div>
            <div class="modal-body">
                <div id="displayedit"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer-script')
<link rel='stylesheet' href='{{ asset('plugins/select2/select2.css') }}'>
<script src='{{ asset('plugins/select2/select2.js') }}'></script>
<script>
// Initialize Select2 Elements
$(document).ready(function() {
    // Initialize Select2 with tags support
    $('.select2').select2({
        tags: true,
        tokenSeparators: [','],
        placeholder: "Select or enter courses offered by this college",
        allowClear: true
    });

    // Initialize Select2 in modal when it's shown
    $('#myModal').on('shown.bs.modal', function () {
        $('.select2').select2({
            tags: true,
            tokenSeparators: [','],
            placeholder: "Select or enter courses offered by this college",
            allowClear: true
        });
    });

    // Load saved search values from session storage
    function loadSavedSearchValues() {
        console.log('Loading saved search values...');
        var savedCode = sessionStorage.getItem('superadmin_college_search_code');
        var savedCodeText = sessionStorage.getItem('superadmin_college_search_code_text');
        var savedName = sessionStorage.getItem('superadmin_college_search_name');
        var savedNameText = sessionStorage.getItem('superadmin_college_search_name_text');
        var savedDescription = sessionStorage.getItem('superadmin_college_search_description');

        console.log('Saved values:', {
            code: savedCode,
            codeText: savedCodeText,
            name: savedName,
            nameText: savedNameText,
            description: savedDescription
        });

        // Clear existing selections first
        $('#search-code').empty();
        $('#search-name').empty();
        $('#search-description').empty();

        // First handle the college code since it affects the courses dropdown
        if (savedCode) {
            console.log('Setting college code:', savedCode, 'Text:', savedCodeText || savedCode);
            // Create the option and append it to the college code dropdown
            var codeOption = new Option(savedCodeText || savedCode, savedCode, true, true);
            $('#search-code').append(codeOption);

            // Initialize the courses dropdown with the saved college code
            initializeCoursesDropdown(savedCode);
        } else {
            // Reset the courses dropdown if no college code is saved
            initializeCoursesDropdown();
        }

        if (savedName) {
            console.log('Setting college name:', savedName, 'Text:', savedNameText || savedName);
            // Create the option and append it
            var nameOption = new Option(savedNameText || savedName, savedName, true, true);
            $('#search-name').append(nameOption);
        }

        // Handle the course description after a short delay to ensure the dropdown is initialized
        setTimeout(function() {
            if (savedDescription) {
                console.log('Setting course description:', savedDescription);
                try {
                    // Create the option and append it
                    var descOption = new Option(savedDescription, savedDescription, true, true);
                    $('#search-description').empty().append(descOption);

                    // Store it in a data attribute for backup
                    $('#search-description').attr('data-selected-value', savedDescription);
                } catch (e) {
                    console.error('Error setting course description:', e);
                }
            }
        }, 100);

        // Trigger change events after all options are set
        if (savedCode) $('#search-code').trigger('change', [true]);
        if (savedName) $('#search-name').trigger('change');
        // Don't trigger change for description as it's handled in the setTimeout
    }

    // Initialize Select2 for College Code search
    $('.select2-code').select2({
        placeholder: "Search by college code",
        allowClear: true,
        minimumInputLength: 0,
        ajax: {
            url: '/ajax/superadmin/college/get_college_codes',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    term: params.term || '',
                    page: params.page || 1
                };
            },
            cache: true
        }
    }).on('change', function(e, skipInitialization) {
        // When college code changes, reset and reload the courses dropdown
        var collegeCode = $(this).val();
        var collegeCodeText = '';

        // Get the text of the selected option
        if (collegeCode && $(this).select2('data').length > 0) {
            collegeCodeText = $(this).select2('data')[0].text;
        }

        console.log('College code changed to:', collegeCode, 'Text:', collegeCodeText, 'Skip:', skipInitialization);

        // Store the selected college code and text in session storage
        if (collegeCode) {
            sessionStorage.setItem('superadmin_college_search_code', collegeCode);
            sessionStorage.setItem('superadmin_college_search_code_text', collegeCodeText);
        } else {
            sessionStorage.removeItem('superadmin_college_search_code');
            sessionStorage.removeItem('superadmin_college_search_code_text');
        }

        // Skip reinitialization if this change was triggered by loadSavedSearchValues
        if (!skipInitialization) {
            // Clear the courses dropdown
            $('#search-description').val(null).trigger('change');

            // Reinitialize the courses dropdown with the selected college code
            initializeCoursesDropdown(collegeCode);
        }
    });

    // Initialize Select2 for College Name search
    $('.select2-name').select2({
        placeholder: "Search by college name",
        allowClear: true,
        minimumInputLength: 0,
        ajax: {
            url: '/ajax/superadmin/college/get_college_names',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    term: params.term || '',
                    page: params.page || 1
                };
            },
            cache: true
        }
    }).on('change', function() {
        // When college name changes, we need to find the corresponding college code
        var collegeName = $(this).val();
        var collegeNameText = '';

        // Get the text of the selected option
        if (collegeName && $(this).select2('data').length > 0) {
            collegeNameText = $(this).select2('data')[0].text;
        }

        console.log('College name changed to:', collegeName, 'Text:', collegeNameText);

        // Store the selected college name and text in session storage
        if (collegeName) {
            sessionStorage.setItem('superadmin_college_search_name', collegeName);
            sessionStorage.setItem('superadmin_college_search_name_text', collegeNameText);

            // Make an AJAX call to get the college code for this name
            $.ajax({
                type: "GET",
                url: "/ajax/superadmin/college/get_college_by_name",
                data: {
                    name: collegeName
                },
                success: function(response) {
                    console.log('College code response:', response);
                    if (response.college_code) {
                        // Set the college code dropdown to this value
                        var collegeCode = response.college_code;
                        console.log('Setting college code to:', collegeCode);

                        // Create the option and append it
                        var codeOption = new Option(collegeCode, collegeCode, true, true);
                        $('#search-code').empty().append(codeOption).trigger('change', [true]);

                        // Reset and initialize the courses dropdown
                        $('#search-description').val(null).trigger('change');
                        initializeCoursesDropdown(collegeCode);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error getting college code:", error);
                }
            });
        } else {
            sessionStorage.removeItem('superadmin_college_search_name');
            sessionStorage.removeItem('superadmin_college_search_name_text');
        }
    });

    // Function to initialize the courses dropdown
    function initializeCoursesDropdown(collegeCode) {
        console.log('Initializing courses dropdown with college code:', collegeCode);

        // Show/hide the filter indicator based on whether a college is selected
        if (collegeCode) {
            $('#courses-filter-indicator').show();
        } else {
            $('#courses-filter-indicator').hide();
        }

        // Destroy existing select2 instance if it exists
        if ($('.select2-courses').data('select2')) {
            $('.select2-courses').select2('destroy');
        }

        // Initialize the select2 dropdown
        $('.select2-courses').select2({
            placeholder: collegeCode ? "Programs for " + collegeCode : "Search by program",
            allowClear: true,
            minimumInputLength: 0,
            ajax: {
                url: '/ajax/superadmin/college/get_courses',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        term: params.term || '',
                        page: params.page || 1,
                        college_code: collegeCode || ''
                    };
                },
                cache: true
            }
        });

        // Add change event handler separately to avoid multiple bindings
        $('.select2-courses').off('change').on('change', function() {
            var courseValue = $(this).val();
            console.log('Course selection changed to:', courseValue);

            // Store the selected course in session storage
            if (courseValue) {
                sessionStorage.setItem('superadmin_college_search_description', courseValue);
            } else {
                sessionStorage.removeItem('superadmin_college_search_description');
            }
        });
    }

    // Initialize Select2 for Courses search (initially with no college filter)
    initializeCoursesDropdown();

    // Load saved search values after a short delay to ensure all dropdowns are initialized
    setTimeout(function() {
        try {
            console.log('Loading saved search values with delay...');
            loadSavedSearchValues();

            // Add a backup check to ensure the course value is set
            setTimeout(function() {
                var savedDescription = sessionStorage.getItem('superadmin_college_search_description');
                if (savedDescription && !$('#search-description').val()) {
                    console.log('Backup: Setting course description:', savedDescription);
                    try {
                        var descOption = new Option(savedDescription, savedDescription, true, true);
                        $('#search-description').empty().append(descOption);
                    } catch (e) {
                        console.error('Error in backup course setting:', e);
                    }
                }
            }, 500);
        } catch (e) {
            console.error('Error loading saved search values:', e);
        }
    }, 500);

    // Search button click event
    $('#btn-search').click(function() {
        searchColleges();
    });

    // Reset button click event
    $('#btn-reset').click(function() {
        // Clear Select2 selections
        $('#search-code').val(null).trigger('change');
        $('#search-name').val(null).trigger('change');
        $('#search-description').val(null).trigger('change');

        // Reset the courses dropdown to show all courses
        initializeCoursesDropdown();

        // Clear session storage
        sessionStorage.removeItem('superadmin_college_search_code');
        sessionStorage.removeItem('superadmin_college_search_code_text');
        sessionStorage.removeItem('superadmin_college_search_name');
        sessionStorage.removeItem('superadmin_college_search_name_text');
        sessionStorage.removeItem('superadmin_college_search_description');

        // Perform the search
        searchColleges();
    });

    // Enter key press event for search inputs
    $('#search-code, #search-name, #search-description').keypress(function(e) {
        if(e.which == 13) { // Enter key
            searchColleges();
        }
    });

    // Function to perform the search
    function searchColleges() {
        var code = '';
        var name = '';
        var description = '';

        console.log('Performing search...');

        // Get selected values from Select2
        try {
            if ($('#search-code').select2('data').length > 0 && $('#search-code').select2('data')[0].id) {
                code = $('#search-code').select2('data')[0].id;
                console.log('Selected code:', code);
            }
        } catch (e) {
            console.error('Error getting code value:', e);
        }

        try {
            if ($('#search-name').select2('data').length > 0 && $('#search-name').select2('data')[0].id) {
                name = $('#search-name').select2('data')[0].id;
                console.log('Selected name:', name);
            }
        } catch (e) {
            console.error('Error getting name value:', e);
        }

        try {
            // Get the course value directly from the element or from session storage
            description = $('#search-description').val() || sessionStorage.getItem('superadmin_college_search_description') || '';
            console.log('Selected description:', description);

            // If we still can't get the value, try the select2 data as a fallback
            if (!description && $('#search-description').select2('data').length > 0 && $('#search-description').select2('data')[0].id) {
                description = $('#search-description').select2('data')[0].id;
                console.log('Selected description from select2 data:', description);
            }
        } catch (e) {
            console.error('Error getting description value:', e);
            // Try to get the value from session storage as a last resort
            description = sessionStorage.getItem('superadmin_college_search_description') || '';
            console.log('Using description from session storage:', description);
        }

        // Store the selected values in session storage for persistence
        if (code) {
            var codeText = '';
            try {
                if ($('#search-code').select2('data').length > 0) {
                    codeText = $('#search-code').select2('data')[0].text;
                }
            } catch (e) {
                console.error('Error getting code text:', e);
            }

            sessionStorage.setItem('superadmin_college_search_code', code);
            sessionStorage.setItem('superadmin_college_search_code_text', codeText || code);
        } else {
            sessionStorage.removeItem('superadmin_college_search_code');
            sessionStorage.removeItem('superadmin_college_search_code_text');
        }

        if (name) {
            var nameText = '';
            try {
                if ($('#search-name').select2('data').length > 0) {
                    nameText = $('#search-name').select2('data')[0].text;
                }
            } catch (e) {
                console.error('Error getting name text:', e);
            }

            sessionStorage.setItem('superadmin_college_search_name', name);
            sessionStorage.setItem('superadmin_college_search_name_text', nameText || name);
        } else {
            sessionStorage.removeItem('superadmin_college_search_name');
            sessionStorage.removeItem('superadmin_college_search_name_text');
        }

        if (description) sessionStorage.setItem('superadmin_college_search_description', description);
        else sessionStorage.removeItem('superadmin_college_search_description');

        console.log('Stored values in session storage:', {
            code: sessionStorage.getItem('superadmin_college_search_code'),
            codeText: sessionStorage.getItem('superadmin_college_search_code_text'),
            name: sessionStorage.getItem('superadmin_college_search_name'),
            nameText: sessionStorage.getItem('superadmin_college_search_name_text'),
            description: sessionStorage.getItem('superadmin_college_search_description')
        });

        $.ajax({
            type: "GET",
            url: "/ajax/superadmin/college/search",
            data: {
                code: code,
                name: name,
                description: description
            },
            success: function(data) {
                $('#colleges-table-body').html(data);
                console.log('Search completed successfully');
            },
            error: function(xhr, status, error) {
                console.error("Error searching colleges:", error);
                toastr.error("An error occurred while searching. Please try again.");
            }
        });
    }
});

function editcollege(college_id) {
    var array = {};
    array['college_id'] = college_id;
    $.ajax({
        type: "GET",
        url: "/ajax/superadmin/college/edit_college",
        data: array,
        success: function(data) {
            $('#displayedit').html(data).fadeIn();
            $('#myModal').modal('show');

            // Ensure the modal is fully rendered before initializing Select2
            setTimeout(function() {
                // Reinitialize Select2 in the modal
                $('.select2-modal').select2({
                    tags: true,
                    tokenSeparators: [','],
                    placeholder: "Select or enter programs offered by this college",
                    allowClear: true,
                    width: '100%'
                });

                // Force the text inputs to show their values
                var collegeCode = $('#edit-college-code').val();
                var collegeName = $('#edit-college-name').val();

                console.log('Modal opened with college code:', collegeCode);
                console.log('Modal opened with college name:', collegeName);
            }, 200);
        }
    });
}

function syncPrograms(collegeId) {
    if (!confirm('This will sync programs between the college description and academic programs table. Continue?')) {
        return;
    }

    $.ajax({
        url: '/superadmin/curriculum_management/college/sync_programs',
        type: 'POST',
        data: {
            college_id: collegeId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success('Programs synchronized successfully');
                // Reload the page to show updated data
                location.reload();
            } else {
                toastr.error(response.message || 'Failed to synchronize programs');
            }
        },
        error: function() {
            toastr.error('Failed to synchronize programs');
        }
    });
}
</script>
@endsection
