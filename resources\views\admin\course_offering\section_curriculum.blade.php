<?php
use Illuminate\Support\Facades\Schema;

$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-cubes"></i>
        Section Management
        <small>with Curriculum Integration</small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Section Management</li>
      </ol>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    @if(Session::has('success'))
    <div class='col-sm-12'>
        <div class='callout callout-success'>
            {{Session::get('success')}}
        </div>
    </div>
    @endif

    @if(Session::has('error'))
    <div class='col-sm-12'>
        <div class='callout callout-danger'>
            {{Session::get('error')}}
        </div>
    </div>
    @endif
    <div class='row'>
        <div class='col-sm-5'>
            <div class='box box-solid box-primary'>
                <div class='box-header bg-navy-active'>
                    <h5 class="box-title">New Section with Curriculum</h5>
                </div>
                <div class='box-body'>
                    <form action="{{url('/admin/section_curriculum/create')}}" method="post">
                    {{csrf_field()}}
                    @if(Schema::hasColumn('ctr_sections', 'college_code'))
                    <div class='form-group'>
                        <label>College</label>
                        <select class="select2 form-control" name="college_code" id="college_code">
                            <option value="">All Colleges</option>
                            @foreach($colleges as $college)
                            <option value="{{$college->college_code}}">{{$college->college_code}}</option>
                            @endforeach
                        </select>
                    </div>
                    @endif
                    <div class='form-group'>
                        <label>Academic Program</label>
                        <select class="select2 form-control" name="program_code" id="program_code"
                        required class='form-control'>
                            <option value="">Please Select</option>
                            @foreach($programs as $program)
                            <option value="{{$program->program_code}}">{{$program->program_code}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class='form-group'>
                        <label>Level</label>
                        <select name="level" id="level" required class='select2 form-control'>
                            <option value="">Please Select</option>
                            <option value="1st Year">1st Year</option>
                            <option value="2nd Year">2nd Year</option>
                            <option value="3rd Year">3rd Year</option>
                            <option value="4th Year">4th Year</option>
                            <option value="5th Year">5th Year</option>
                        </select>
                    </div>
                    <div class='form-group'>
                        <label>Section</label>
                        <input id="section_name" required name="section_name" type="text" class="form-control">
                    </div>
                    <div class='form-group'>
                        <label>Add Curriculum Subjects</label>
                        <div class="radio">
                            <label>
                                <input type="radio" name="add_curriculum" id="add_curriculum_yes" value="yes" checked>
                                Yes - Automatically add all curriculum subjects for this program and level
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="add_curriculum" id="add_curriculum_no" value="no">
                                No - Create section only
                            </label>
                        </div>
                        <small class="text-muted" id="curricula_info">Checking for available curriculum subjects...</small>
                    </div>
                    <div class='form-group'>
                        <button onclick='return confirm("Clicking the OK button will save the record? Do you wish to continue?")' type='submit' class='btn btn-flat btn-success btn-block'>Save and Submit</button>
                    </div>
                    </form>
                </div>
            </div>
        </div>
        <div class='col-sm-7'>
            <div class="box box-default">
                <div class="box-header">
                    <h5 class="box-title">Sections with Curriculum Information</h5>
                    <div class="box-tools pull-right">
                        <a href="{{url('/admin/section_curriculum/archive')}}" class="btn btn-flat btn-danger"><i class="fa fa-archive"></i> Archive</a>
                    </div>
                </div>
                <div class="box-body">
                    <!-- Search and Filter Section -->
                    <div class="row" style="margin-bottom: 15px;">
                        <div class="col-md-12">
                            <div class="box box-solid box-default">
                                <div class="box-header with-border">
                                    <h3 class="box-title"><i class="fa fa-search"></i> Search & Filter</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        @if(Schema::hasColumn('ctr_sections', 'college_code'))
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="search-college">College</label>
                                                <select id="search-college" class="form-control select2">
                                                    <option value="">All Colleges</option>
                                                    @foreach($colleges as $college)
                                                        <option value="{{$college->college_code}}">{{$college->college_code}} - {{$college->college_name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        @endif
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="search-program">Program</label>
                                                <select id="search-program" class="form-control select2">
                                                    <option value="">All Programs</option>
                                                    @foreach($uniquePrograms as $program)
                                                        <option value="{{$program}}">{{$program}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="search-level">Level</label>
                                                <select id="search-level" class="form-control select2">
                                                    <option value="">All Levels</option>
                                                    @foreach($uniqueLevels as $level)
                                                        <option value="{{$level}}">{{$level}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="search-section">Section</label>
                                                <div class="input-group">
                                                    <input type="text" id="search-section" class="form-control" placeholder="Search section...">
                                                    <span class="input-group-btn">
                                                        <button class="btn btn-default" type="button" onclick="searchSections()"><i class="fa fa-search"></i></button>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="sections-table">
                            <thead>
                                <tr>
                                    @if(Schema::hasColumn('ctr_sections', 'college_code'))
                                    <th>College</th>
                                    @endif
                                    <th>Program</th>
                                    <th>Level</th>
                                    <th>Section</th>
                                    <th>Curriculum Subjects</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="sections-table-body">
                                @if(!$sections->isEmpty())
                                @foreach($sections as $section)
                                <?php
                                    $offered_count = \App\offerings_infos_table::where('section_name', $section->section_name)->count();
                                    $available_count = $section->getAvailableCurricula()->count();
                                ?>
                                <tr>
                                    @if(Schema::hasColumn('ctr_sections', 'college_code'))
                                    <td>{{$section->college_code ?? 'N/A'}}</td>
                                    @endif
                                    <td>{{$section->program_code}}</td>
                                    <td>{{$section->level}}</td>
                                    <td>{{$section->section_name}}</td>
                                    <td>
                                        <span class="label label-primary">{{$offered_count}} Offered</span>
                                        @if($available_count > 0)
                                            <span class="label label-warning">{{$available_count}} Available</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($section->is_active == 1)
                                        <label class='label label-success'>Active</label>
                                        @else
                                        <label class='label label-danger'>Inactive</label>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{url('/admin/section_curriculum/view',[$section->id])}}" class="btn btn-flat btn-primary" title="Manage Curriculum"><i class="fa fa-book"></i></a>
                                        <a href="{{url('/admin/section_curriculum/add_all',[$section->id])}}" class="btn btn-flat btn-success" title="Add All Available Curriculum Subjects" onclick="return confirm('Add all available curriculum subjects to this section?')"><i class="fa fa-plus-circle"></i></a>
                                        <button data-toggle="modal" data-target="#myModal" onclick="editsection('{{$section->id}}')" title="Edit Section" class="btn btn-flat btn-info"><i class="fa fa-pencil"></i></button>
                                        <a href="{{url('/admin/section_management/archive',[$section->id])}}" class="btn btn-flat btn-danger" title="Archive Section" onclick="return confirm('Do you wish to archive this section?')"><i class="fa fa-archive"></i></a>
                                    </td>
                                </tr>
                                @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div id="myModal" class="modal fade" role="dialog">
    <div id='displayedit'>
    </div>
</div>

@endsection

@section('footer-script')
<script src='{{ asset('plugins/select2/select2.js') }}'></script>
<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%'
    });

    // College dropdown change event - filter programs
    $('#college_code').on('change', function() {
        var collegeCode = $(this).val();
        filterProgramsByCollege(collegeCode, '#program_code');
    });

    // Search college dropdown change event - filter programs
    $('#search-college').on('change', function() {
        var collegeCode = $(this).val();
        filterProgramsByCollege(collegeCode, '#search-program');
        searchSections();
    });

    // Initialize program code and section name relationship
    $('#program_code').on('change', function() {
        if (this.value != '') {
            $('#section_name').val(this.value + '-');
            checkAvailableCurricula();
        } else {
            $('#section_name').val('');
            $('#curricula_info').text('Please select a program and level to check for available curriculum subjects');
        }
    });

    // Check for available curricula when level changes
    $('#level').on('change', function() {
        checkAvailableCurricula();
    });

    // Search filters
    $('#search-program, #search-level').on('change', function() {
        searchSections();
    });

    // Enter key press event for section input
    $('#search-section').keypress(function(e) {
        if(e.which == 13) { // Enter key
            searchSections();
        }
    });
});

// Check for available curricula for the selected program and level
function checkAvailableCurricula() {
    const programCode = $('#program_code').val();
    const level = $('#level').val();

    if (!programCode || !level) {
        $('#curricula_info').text('Please select a program and level to check for available curriculum subjects');
        return;
    }

    // Show loading message
    $('#curricula_info').text('Checking for available curriculum subjects...');

    // Make AJAX request to check for curricula
    $.ajax({
        type: "GET",
        url: "{{ url('/ajax/admin/section_curriculum/get_curricula') }}",
        data: {
            program_code: programCode,
            level: level
        },
        success: function(response) {
            if (response.success) {
                if (response.count > 0) {
                    $('#curricula_info').html('<span class="text-success"><i class="fa fa-check-circle"></i> ' +
                        response.count + ' curriculum subject(s) found for this program and level.</span>');
                    $('#add_curriculum_yes').prop('disabled', false);
                } else {
                    $('#curricula_info').html('<span class="text-warning"><i class="fa fa-exclamation-circle"></i> ' +
                        'No curriculum subjects found for this program and level.</span>');
                    $('#add_curriculum_yes').prop('disabled', true);
                    $('#add_curriculum_no').prop('checked', true);
                }
            } else {
                $('#curricula_info').html('<span class="text-danger"><i class="fa fa-times-circle"></i> ' +
                    'Error checking for curriculum subjects: ' + response.message + '</span>');
                $('#add_curriculum_yes').prop('disabled', true);
                $('#add_curriculum_no').prop('checked', true);
            }
        },
        error: function() {
            $('#curricula_info').html('<span class="text-danger"><i class="fa fa-times-circle"></i> ' +
                'Error connecting to server. Please try again.</span>');
            $('#add_curriculum_yes').prop('disabled', true);
            $('#add_curriculum_no').prop('checked', true);
        }
    });
}

// Function to perform the search
function searchSections() {
    var data = {
        program: $('#search-program').val(),
        level: $('#search-level').val(),
        section: $('#search-section').val()
    };

    // Only add college parameter if the element exists
    if ($('#search-college').length) {
        data.college = $('#search-college').val();
    }

    $.ajax({
        type: "GET",
        url: "{{ url('/ajax/admin/section_curriculum/search') }}",
        data: data,
        success: function(data) {
            $('#sections-table-body').html(data);
        },
        error: function(xhr, status, error) {
            console.error("Error searching sections:", error);
            toastr.error("An error occurred while searching. Please try again.");
        }
    });
}

// Function to filter programs by college
function filterProgramsByCollege(collegeCode, programSelector) {
    if (!collegeCode) {
        // If no college selected, show all programs
        loadAllPrograms(programSelector);
        return;
    }

    // Show loading indicator
    $(programSelector).prop('disabled', true);

    $.ajax({
        type: "GET",
        url: "{{ url('/ajax/admin/section_curriculum/get_programs_by_college') }}",
        data: {
            college_code: collegeCode
        },
        success: function(response) {
            if (response.success) {
                // Clear current options
                $(programSelector).empty();

                // Add default option
                $(programSelector).append('<option value="">Please Select</option>');

                // Add programs from response
                $.each(response.programs, function(index, program) {
                    var programName = program.program_name ? program.program_name : 'N/A';
                    $(programSelector).append('<option value="' + program.program_code + '">' +
                        program.program_code + ' - ' + programName + '</option>');
                });

                // Re-enable select
                $(programSelector).prop('disabled', false);

                // Trigger change event to update dependent fields
                $(programSelector).trigger('change');
            } else {
                console.error("Error fetching programs:", response.message);
                toastr.error("An error occurred while fetching programs. Please try again.");
                $(programSelector).prop('disabled', false);
            }
        },
        error: function(xhr, status, error) {
            console.error("Error fetching programs:", error);
            toastr.error("An error occurred while fetching programs. Please try again.");
            $(programSelector).prop('disabled', false);
        }
    });
}

// Function to load all programs
function loadAllPrograms(programSelector) {
    // Show loading indicator
    $(programSelector).prop('disabled', true);

    $.ajax({
        type: "GET",
        url: "{{ url('/ajax/admin/section_curriculum/get_all_programs') }}",
        success: function(response) {
            if (response.success) {
                // Clear current options
                $(programSelector).empty();

                // Add default option
                $(programSelector).append('<option value="">Please Select</option>');

                // Add programs from response
                $.each(response.programs, function(index, program) {
                    var programName = program.program_name ? program.program_name : 'N/A';
                    $(programSelector).append('<option value="' + program.program_code + '">' +
                        program.program_code + ' - ' + programName + '</option>');
                });

                // Re-enable select
                $(programSelector).prop('disabled', false);

                // Trigger change event to update dependent fields
                $(programSelector).trigger('change');
            } else {
                console.error("Error fetching programs:", response.message);
                toastr.error("An error occurred while fetching programs. Please try again.");
                $(programSelector).prop('disabled', false);
            }
        },
        error: function(xhr, status, error) {
            console.error("Error fetching programs:", error);
            toastr.error("An error occurred while fetching programs. Please try again.");
            $(programSelector).prop('disabled', false);
        }
    });
}

function editsection(section_id){
    var array = {};
    array['section_id'] = section_id;
    $.ajax({
        type: "GET",
        url: "/ajax/admin/section_management/edit_section",
        data: array,
        success: function(data){
            $('#displayedit').html(data).fadeIn();
            $('#myModal').modal('show');
        }
    })
}
</script>
@endsection
