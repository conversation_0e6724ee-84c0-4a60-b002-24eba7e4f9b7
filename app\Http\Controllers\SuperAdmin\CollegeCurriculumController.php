<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\academic_programs;
use App\curriculum;
use Auth;
use Session;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Helper;

class CollegeCurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    // View all colleges for curriculum management
    public function index()
    {
        $colleges = College::where('is_active', 1)->get();
        return view('super_admin.curriculum_management.colleges', compact('colleges'));
    }

    // View programs for a specific college
    public function viewPrograms($college_code)
    {
        $college = College::where('college_code', $college_code)->first();
        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        $programs = \App\ProgramHelper::getProgramsByCollegeCode($college_code);
        return view('super_admin.curriculum_management.programs_by_college', compact('college', 'programs'));
    }

    // View curricula for a specific college
    public function viewCurricula($college_code)
    {
        $college = College::where('college_code', $college_code)->first();
        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        $curriculumYears = curriculum::where('college_code', $college_code)
            ->distinct()
            ->pluck('curriculum_year');

        return view('super_admin.curriculum_management.college_curricula', compact('college', 'curriculumYears'));
    }

    // View curriculum for a specific college and year
    public function viewCurriculum($college_code, $curriculum_year)
    {
        $college = College::where('college_code', $college_code)->first();
        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        $programs = \App\ProgramHelper::getProgramsByCollegeCode($college_code);
        $curricula = curriculum::where('college_code', $college_code)
            ->where('curriculum_year', $curriculum_year)
            ->get();

        return view('super_admin.curriculum_management.college_curriculum', compact('college', 'programs', 'curricula', 'curriculum_year'));
    }

    // Sync programs between College and programs table
    public function syncPrograms(Request $request)
    {
        $college = College::find($request->college_id);
        if (!$college) {
            return response()->json(['success' => false, 'message' => 'College not found']);
        }

        // Get programs from courses_array
        $descPrograms = $college->courses_array;

        // Get all programs from programs table
        $dbPrograms = \App\Program::where('college_id', $college->id)
            ->pluck('program_code')
            ->toArray();

        // Find programs in courses_array but not in programs table
        $toAdd = array_diff($descPrograms, $dbPrograms);

        // Add missing programs to programs table
        foreach ($toAdd as $programCode) {
            // Use the College model's addProgram method
            $college->addProgram($programCode);

            // Log the action
            Log::info('User ' . Auth::user()->name . " added program " . $programCode . " to college " . $college->college_code);
        }

        return response()->json(['success' => true, 'message' => 'Programs synchronized successfully']);
    }
}
