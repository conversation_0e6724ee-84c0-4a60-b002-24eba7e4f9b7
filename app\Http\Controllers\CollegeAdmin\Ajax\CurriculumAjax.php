<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\curriculum;
use App\College;

class CurriculumAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Return the edit modal content for a curriculum
     */
    public function editModal(Request $request)
    {
        $curriculum_id = $request->input('curriculum_id');
        $course = curriculum::find($curriculum_id);

        if (!$course) {
            return response()->json(['error' => 'Curriculum not found'], 404);
        }

        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Verify the curriculum belongs to this college
        if ($course->college_code !== $collegeCode) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return view('collegeadmin.curriculum_management.edit_curriculum', compact('course'));
    }
}
