<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('header')
<!-- Content Header (Page header) -->
<section class="content-header">
    <h1>
        Archives for Subjects
        <small></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li class="active"><a href="{{ url('/superadmin/curriculum_management/archived_subjects') }}"> Archives for Subjects</a></li>
    </ol>
</section>
@endsection

@section('main-content')
<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-default">
                <div class="box-header">
                    <h3 class="box-title">Archived Curriculum Subjects</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/superadmin/curriculum_management/colleges') }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back to Colleges</a>
                    </div>
                </div>
                <div class="box-body">
                    @if(count($groupedCurricula) > 0)
                        <div class="panel-group" id="accordion">
                            @foreach($groupedCurricula as $key => $curriculum)
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" data-parent="#accordion" href="#collapse{{ str_replace('-', '_', $key) }}">
                                                <strong>{{ $curriculum['program_code'] }}</strong> - {{ $curriculum['program_name'] }} ({{ $curriculum['curriculum_year'] }})
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapse{{ str_replace('-', '_', $key) }}" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Course Code</th>
                                                            <th>Course Name</th>
                                                            <th>Level</th>
                                                            <th>Period</th>
                                                            <th>Lec</th>
                                                            <th>Lab</th>
                                                            <th>Units</th>
                                                            <th class="text-center">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($curriculum['subjects'] as $subject)
                                                            <tr>
                                                                <td>{{ $subject->course_code }}</td>
                                                                <td>{{ $subject->course_name }}</td>
                                                                <td>{{ $subject->level }}</td>
                                                                <td>{{ $subject->period }}</td>
                                                                <td>{{ $subject->lec }}</td>
                                                                <td>{{ $subject->lab }}</td>
                                                                <td>{{ $subject->units }}</td>
                                                                <td class="text-center">
                                                                    <a onclick="archiveCurriculum('{{ $subject->curriculum_year }}','{{ $subject->program_code }}')" class="btn btn-flat btn-success" title="Restore Curriculum">
                                                                        <i class="fa fa-recycle"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info">
                            <h4><i class="icon fa fa-info"></i> No archived subjects found!</h4>
                            <p>There are no archived curriculum subjects at this time.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
    function archiveCurriculum(curriculum_year, program_code){
        if (confirm('Are you sure you want to restore this curriculum? This will make it active again.')) {
            var array = {};
            array['curriculum_year'] = curriculum_year;
            array['program_code'] = program_code;
            $.ajax({
                type: "GET",
                url: "/admin/curriculum_management/archive_curriculum",
                data: array,
                success: function(data){
                    if (data.success) {
                        toastr.success(data.message, 'Success!');
                        // Refresh the page to show the updated list
                        location.reload();
                    } else {
                        toastr.error(data.message, 'Error!');
                    }
                }, error: function(){
                    toastr.error('Something Went Wrong!', 'Error!');
                }
            });
        }
    }
</script>
@endsection
