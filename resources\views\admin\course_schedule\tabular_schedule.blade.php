<?php
$layout = '';

if (Auth::user()->is_first_login == 1) {
    $layout = 'layouts.first_login';
} else {
    if (Auth::user()->accesslevel == 100) {
        $layout = 'layouts.superadmin';
    } elseif (Auth::user()->accesslevel == 1) {
        $layout = 'layouts.instructor';
    } elseif (Auth::user()->accesslevel == 0) {
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-table"></i>
        Tabular Schedule View - {{ $sectionName }}
        <small></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/') }}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ url('/admin/course_scheduling') }}">Course Scheduling</a></li>
        <li class="active">Tabular Schedule</li>
    </ol>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    <div class="box box-default">
        <div class="box-header">
            <h3 class="box-title">Schedule for {{ $sectionName }}</h3>
            <div class="box-tools pull-right">
                <a href="{{ url('/admin/course_scheduling') }}" class="btn btn-flat btn-default">
                    <i class="fa fa-arrow-left"></i> Back to Course Scheduling
                </a>
                <button class="btn btn-flat btn-primary" onclick="printSchedule()">
                    <i class="fa fa-print"></i> Print Schedule
                </button>
            </div>
        </div>
        <div class="box-body">
            <div id="printable-area">
                <h3 class="text-center">Schedule for {{ $sectionName }}</h3>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Day</th>
                                <th>Time</th>
                                <th>Course Code</th>
                                <th>Course Name</th>
                                <th>Room</th>
                                <th>Instructor</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(count($schedules) > 0)
                                @foreach($schedules as $schedule)
                                    <tr>
                                        <td>{{ $schedule->day }}</td>
                                        <td>{{ date('g:i A', strtotime($schedule->time_starts)) }} - {{ date('g:i A', strtotime($schedule->time_end)) }}</td>
                                        <td>{{ $schedule->course_code }}</td>
                                        <td>{{ $schedule->course_name }}</td>
                                        <td>{{ $schedule->room }}</td>
                                        <td>
                                            @if($schedule->instructor)
                                                {{ $schedule->instructor_lastname }}, {{ $schedule->instructor_name }}
                                            @else
                                                <span class="text-danger">Not Assigned</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="6" class="text-center">No schedules found for this section.</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="box box-primary">
        <div class="box-header">
            <h3 class="box-title">Calendar View</h3>
        </div>
        <div class="box-body">
            <div id="calendar"></div>
        </div>
    </div>
</div>
@endsection

@section('footer-script')
<script type="text/javascript" src="{{ asset('/plugins/moment/moment.js') }}"></script>
<script src="{{ asset('plugins/fullcalendar/fullcalendar.js') }}"></script>
<link rel="stylesheet" href="{{ asset('plugins/fullcalendar/fullcalendar.css') }}">

<script>
    $(document).ready(function() {
        // Initialize calendar
        $('#calendar').fullCalendar({
            firstDay: 1,
            columnFormat: 'ddd',
            defaultView: 'agendaWeek',
            hiddenDays: [0],
            minTime: '07:00:00',
            maxTime: '22:00:00',
            header: false,
            allDaySlot: false,
            eventSources: [{
                events: [
                    @foreach($schedules as $schedule)
                    {
                        id: {{ $schedule->id }},
                        title: '{{ $schedule->course_code }} - {{ $schedule->room }} - {{ $schedule->instructor_lastname ?? "Not Assigned" }}',
                        start: '{{ date("Y-m-d", strtotime("this week " . getDayName($schedule->day))) }}T{{ $schedule->time_starts }}',
                        end: '{{ date("Y-m-d", strtotime("this week " . getDayName($schedule->day))) }}T{{ $schedule->time_end }}',
                        color: '{{ getColorForDay($schedule->day) }}',
                        textColor: 'black'
                    },
                    @endforeach
                ]
            }],
            eventRender: function(event, element) {
                element.find('div.fc-title').html(element.find('div.fc-title').text());
            }
        });
    });

    function printSchedule() {
        var printContents = document.getElementById('printable-area').innerHTML;
        var originalContents = document.body.innerHTML;
        document.body.innerHTML = '<html><head><title>Schedule for {{ $sectionName }}</title>' +
            '<link rel="stylesheet" href="{{ asset("css/bootstrap.min.css") }}">' +
            '<style>body { padding: 20px; } table { width: 100%; border-collapse: collapse; } ' +
            'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } ' +
            'th { background-color: #f2f2f2; }</style>' +
            '</head><body>' + printContents + '</body></html>';
        window.print();
        document.body.innerHTML = originalContents;
    }

    <?php
    function getDayName($day) {
        switch ($day) {
            case 'M': return 'Monday';
            case 'T': return 'Tuesday';
            case 'W': return 'Wednesday';
            case 'Th': return 'Thursday';
            case 'F': return 'Friday';
            case 'Sa': return 'Saturday';
            default: return 'Monday';
        }
    }

    function getColorForDay($day) {
        switch ($day) {
            case 'M': return 'LightSalmon';
            case 'W': return 'LightSalmon';
            case 'F': return 'LightSalmon';
            case 'T': return 'lightblue';
            case 'Th': return 'lightblue';
            case 'Sa': return 'lightblue';
            default: return 'lightgray';
        }
    }
    ?>
</script>
@endsection
