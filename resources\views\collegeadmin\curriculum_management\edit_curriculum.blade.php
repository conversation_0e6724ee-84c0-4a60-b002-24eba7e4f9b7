<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label>Course Code</label>
            <input type="text" name="course_code" class="form-control" value="{{ $course->control_code }}" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label>Course Name</label>
            <input type="text" name="course_name" class="form-control" value="{{ $course->course_name }}" required>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label>Lecture Units</label>
            <input type="number" id="lec-input" name="lec" class="form-control" min="0" step="0.5" value="{{ $course->lec }}" required>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>Laboratory Units</label>
            <input type="number" id="lab-input" name="lab" class="form-control" min="0" step="0.5" value="{{ $course->lab }}" required>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>Total Units</label>
            <input type="number" id="units-input" name="units" class="form-control" min="0" step="0.5" value="{{ $course->units }}" required readonly>
        </div>
    </div>
</div>

<script>
    // Function to calculate units based on lec, lab, and complab values
    function calculateUnits() {
        var lec = parseFloat(document.getElementById('lec-input').value) || 0;
        var lab = parseFloat(document.getElementById('lab-input').value) || 0;
        var isComplab = document.querySelector('input[name="complab"]').checked;

        // Calculate total units based on CompLab status
        var units;
        if (isComplab) {
            // If CompLab is Yes: Units = Lec + 1
            units = lec + 1;
        } else {
            // If CompLab is No: Units = Lec + Lab
            units = lec + lab;
        }

        // Set the calculated value
        document.getElementById('units-input').value = units.toFixed(1);
    }

    // Add event listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('lec-input').addEventListener('input', calculateUnits);
        document.getElementById('lab-input').addEventListener('input', calculateUnits);
        document.querySelector('input[name="complab"]').addEventListener('change', calculateUnits);

        // Initialize calculation
        calculateUnits();
    });
</script>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>
                <input type="checkbox" name="complab" value="1" {{ $course->is_complab ? 'checked' : '' }}> Computer Laboratory
            </label>
        </div>
    </div>
</div>
