<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Teaching Schedule Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .instructor-info {
            margin-bottom: 20px;
        }
        .instructor-info p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>INSTRUCTOR TEACHING SCHEDULE</h1>
        <p>{{ date('F d, Y') }}</p>
    </div>

    <div class="instructor-info">
        <p><strong>Instructor:</strong> {{ $instructor->name }} {{ $instructor->lastname }}</p>
        <p><strong>ID:</strong> {{ $instructor->id }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Day</th>
                <th>Time</th>
                <th>Course Code</th>
                <th>Course Name</th>
                <th>Section</th>
                <th>Room</th>
            </tr>
        </thead>
        <tbody>
            @foreach($schedules as $schedule)
            <tr>
                <td>{{ $schedule['day'] }}</td>
                <td>{{ date('h:i A', strtotime($schedule['time_starts'])) }} - {{ date('h:i A', strtotime($schedule['time_end'])) }}</td>
                <td>{{ $schedule['course_code'] }}</td>
                <td>{{ $schedule['course_name'] }}</td>
                <td>{{ $schedule['section_name'] }}</td>
                <td>{{ $schedule['room'] }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Generated on {{ date('Y-m-d H:i:s') }}</p>
        <p>This is an official document of the CLASSMOSS.</p>
    </div>
</body>
</html>
