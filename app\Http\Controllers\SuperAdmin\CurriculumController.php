<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\curriculum;
use App\academic_programs;
use Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

class CurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    public function index()
    {
        // Get all active colleges
        $colleges = \App\College::where('is_active', 1)->orderBy('college_code')->get();

        // Create an array to store colleges with their programs
        $collegesWithPrograms = [];

        foreach ($colleges as $college) {
            // Get programs for this college
            $programsList = $college->getPrograms();

            // Only include colleges that have programs
            if (count($programsList) > 0) {
                $collegesWithPrograms[] = [
                    'college' => $college,
                    'programs' => $programsList
                ];
            }
        }

        // Get all programs using ProgramHelper
        $programs = \App\ProgramHelper::getAllPrograms();

        return view('/superadmin/curriculum_management/curriculum', compact('collegesWithPrograms', 'programs', 'colleges'));
    }

    public function viewcurricula($program_code, Request $request)
    {
        // Check if we should show archived curricula
        $showArchived = $request->has('show_archived') && $request->show_archived == 1;

        // Get program using the ProgramHelper
        $program = \App\ProgramHelper::getProgramByCode($program_code);

        // If not found in academic_programs, try to get from curriculum table
        if (!$program) {
            $program = \App\curriculum::where('program_code', $program_code)->first();
        }

        // If still not found, create a basic program object with the code
        if (!$program) {
            // Try to get program name from program_names table
            $programName = DB::table('program_names')
                ->where('program_code', $program_code)
                ->first();

            // Create a program object with available information
            $program = (object) [
                'program_code' => $program_code,
                'program_name' => $programName ? $programName->program_name : $program_code
            ];
        }

        // Get curricula based on active status
        $query = \App\curriculum::distinct()
            ->where('program_code', $program_code);

        if (!$showArchived) {
            // Only show active curricula
            $query->where('is_active', 1);
        } else {
            // Only show archived curricula
            $query->where('is_active', 0);
        }

        $curricula = $query->get(['curriculum_year']);

        if ($request->ajax()) {
            return view(
                '/superadmin/curriculum_management/ajax/refresh_curriculum',
                compact('curricula', 'program', 'program_code', 'showArchived')
            );
        }

        return view(
            '/superadmin/curriculum_management/view_curriculums',
            compact('curricula', 'program', 'program_code', 'showArchived')
        );
    }

    public function archived_subjects()
    {
        // Get all archived curricula
        $archivedCurricula = \App\curriculum::where('is_active', 0)
            ->orderBy('program_code')
            ->orderBy('curriculum_year')
            ->get();

        // Group by program code and curriculum year
        $groupedCurricula = [];
        foreach ($archivedCurricula as $curriculum) {
            $key = $curriculum->program_code . '-' . $curriculum->curriculum_year;
            if (!isset($groupedCurricula[$key])) {
                $groupedCurricula[$key] = [
                    'program_code' => $curriculum->program_code,
                    'program_name' => $curriculum->program_name,
                    'curriculum_year' => $curriculum->curriculum_year,
                    'college_code' => $curriculum->college_code,
                    'subjects' => []
                ];
            }
            $groupedCurricula[$key]['subjects'][] = $curriculum;
        }

        return view('/superadmin/curriculum_management/archived_subjects', compact('groupedCurricula'));
    }

    /**
     * Fix curriculum course names
     * This method updates course names that are in the format "Course: XXX" to more descriptive names
     */
    public function fixCourseNames()
    {
        // Create a mapping of subject codes to descriptive names
        $subjectNames = [
            // Computer Science/IT Subjects
            'CSP107' => 'Introduction to Programming',
            'CSP108' => 'Data Structures and Algorithms',
            'CSP109' => 'Database Management Systems',
            'CSA101' => 'Computer Architecture',
            'CSA102' => 'Operating Systems',
            'CSP110' => 'Web Development',
            'CSP111' => 'Software Engineering',
            'CSA103' => 'Computer Networks',
            'CSA104' => 'Information Security',
            'CSA105' => 'Mobile Application Development',
            'CSE3' => 'Computer Ethics',
            'CSP112' => 'Advanced Programming',
            'CSP113' => 'Artificial Intelligence',

            // Add more mappings as needed for other subjects
        ];

        $updatedCount = 0;
        $createdCount = 0;

        // First, ensure all subjects exist in the subjects table
        foreach ($subjectNames as $code => $name) {
            $subject = \App\Subject::where('subject_code', $code)->first();

            if ($subject) {
                // Update the subject name if it's different
                if ($subject->subject_name !== $name) {
                    $subject->subject_name = $name;
                    $subject->save();
                    $updatedCount++;
                }
            } else {
                // Create the subject if it doesn't exist
                \App\Subject::create([
                    'subject_code' => $code,
                    'subject_name' => $name,
                    'lec' => 3, // Default values
                    'lab' => 0,
                    'units' => 3,
                    'is_complab' => 0
                ]);
                $createdCount++;
            }
        }

        // Now update curriculum records
        $fixedCount = 0;

        // Find all curriculum records with course names in the format "Course: XXX"
        $coursePrefixRecords = \App\curriculum::where('course_name', 'like', 'Course: %')->get();

        foreach ($coursePrefixRecords as $curriculum) {
            // Extract the course code from the name
            $codeMatch = [];
            if (preg_match('/Course: (.+)/', $curriculum->course_name, $codeMatch)) {
                $extractedCode = $codeMatch[1];

                // Check if we have a mapping for this code
                if (isset($subjectNames[$extractedCode])) {
                    $curriculum->course_name = $subjectNames[$extractedCode];

                    // Also update the subject_id if possible
                    $subject = \App\Subject::where('subject_code', $extractedCode)->first();
                    if ($subject) {
                        $curriculum->subject_id = $subject->id;
                    }

                    $curriculum->save();
                    $fixedCount++;
                }
            }
        }

        // Also update any curriculum records that have matching course_code but no proper name
        foreach ($subjectNames as $code => $name) {
            $curricula = \App\curriculum::where('course_code', $code)
                ->where(function ($query) {
                    $query->whereNull('course_name')
                        ->orWhere('course_name', '')
                        ->orWhere('course_name', 'like', 'Course: %');
                })
                ->get();

            foreach ($curricula as $curriculum) {
                $curriculum->course_name = $name;

                // Also update the subject_id if possible
                $subject = \App\Subject::where('subject_code', $code)->first();
                if ($subject) {
                    $curriculum->subject_id = $subject->id;
                }

                $curriculum->save();
                $fixedCount++;
            }
        }

        // Set flash message with results
        Session::flash('success', "Fixed {$fixedCount} curriculum course names. Created {$createdCount} new subjects and updated {$updatedCount} existing subjects.");

        // Redirect back to the curriculum management page
        return redirect()->back();
    }
}
