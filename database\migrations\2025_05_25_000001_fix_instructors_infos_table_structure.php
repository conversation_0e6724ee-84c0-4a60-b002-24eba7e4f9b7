<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixInstructorsInfosTableStructure extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, check if the table exists and fix the structure
        if (Schema::hasTable('instructors_infos')) {
            // Add tel_no column if it doesn't exist
            if (!Schema::hasColumn('instructors_infos', 'tel_no')) {
                Schema::table('instructors_infos', function (Blueprint $table) {
                    $table->string('tel_no')->nullable()->after('municipality');
                });
            }
            
            // Add emerg_cont_# column if it doesn't exist
            if (!Schema::hasColumn('instructors_infos', 'emerg_cont_#')) {
                Schema::table('instructors_infos', function (Blueprint $table) {
                    $table->string('emerg_cont_#')->nullable()->after('cell_no');
                });
            }
            
            // Fix the id column to have AUTO_INCREMENT
            DB::statement('ALTER TABLE instructors_infos MODIFY id INT(10) UNSIGNED NOT NULL AUTO_INCREMENT');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove the AUTO_INCREMENT from id column
        DB::statement('ALTER TABLE instructors_infos MODIFY id INT(10) UNSIGNED NOT NULL');
        
        // Remove tel_no column if it exists
        if (Schema::hasColumn('instructors_infos', 'tel_no')) {
            Schema::table('instructors_infos', function (Blueprint $table) {
                $table->dropColumn('tel_no');
            });
        }
        
        // Note: We don't remove emerg_cont_# as it might be needed by existing data
    }
}
