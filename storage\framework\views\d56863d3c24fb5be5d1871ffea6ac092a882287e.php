<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>



<?php $__env->startSection('main-content'); ?>
<section class="content-header">
    <h1><i class="fa fa-user-plus"></i>
        Add New Faculty
        <small><?php echo e(Auth::user()->college_code); ?></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/')); ?>"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="<?php echo e(route('collegeadmin.faculty.index')); ?>">Faculty Management</a></li>
        <li class="active">Add New Faculty</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Information</h3>
                </div>
                <form action="<?php echo e(route('collegeadmin.faculty.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="box-body">
                        <?php if(Session::has('success')): ?>
                            <div class="alert alert-success">
                                <?php echo e(Session::get('success')); ?>

                            </div>
                        <?php endif; ?>

                        <?php if(Session::has('error')): ?>
                            <div class="alert alert-danger">
                                <?php echo e(Session::get('error')); ?>

                            </div>
                        <?php endif; ?>

                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul>
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username">Username</label>
                                    <input type="text" class="form-control" id="username" name="username" value="<?php echo e(old('username')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email')); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="name">First Name</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="middlename">Middle Name</label>
                                    <input type="text" class="form-control" id="middlename" name="middlename" value="<?php echo e(old('middlename')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="lastname">Last Name</label>
                                    <input type="text" class="form-control" id="lastname" name="lastname" value="<?php echo e(old('lastname')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="extensionname">Extension Name</label>
                                    <input type="text" class="form-control" id="extensionname" name="extensionname" value="<?php echo e(old('extensionname')); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password_confirmation">Confirm Password</label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                </div>
                            </div>
                        </div>

                        <hr>
                        <h4><i class="fa fa-info-circle"></i> Additional Information</h4>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="department">Department</label>
                                    <input type="text" class="form-control" id="department" name="department" value="<?php echo e(old('department')); ?>" placeholder="e.g., Computer Science">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="employee_type">Faculty Status</label>
                                    <select class="form-control" id="employee_type" name="employee_type">
                                        <option value="">Select Status</option>
                                        <option value="Regular" <?php echo e(old('employee_type') == 'Regular' ? 'selected' : ''); ?>>Regular</option>
                                        <option value="Part-time" <?php echo e(old('employee_type') == 'Part-time' ? 'selected' : ''); ?>>Part-time</option>
                                        <option value="Contractual" <?php echo e(old('employee_type') == 'Contractual' ? 'selected' : ''); ?>>Contractual</option>
                                        <option value="Visiting" <?php echo e(old('employee_type') == 'Visiting' ? 'selected' : ''); ?>>Visiting</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="gender">Gender</label>
                                    <select class="form-control" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="Male" <?php echo e(old('gender') == 'Male' ? 'selected' : ''); ?>>Male</option>
                                        <option value="Female" <?php echo e(old('gender') == 'Female' ? 'selected' : ''); ?>>Female</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="cell_no">Mobile Number</label>
                                    <input type="text" class="form-control" id="cell_no" name="cell_no" value="<?php echo e(old('cell_no')); ?>" placeholder="e.g., 09123456789">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="emerg_cont_#">Emergency Contact Number</label>
                                    <input type="text" class="form-control" id="emerg_cont_#" name="emerg_cont_#" value="<?php echo e(old('emerg_cont_#')); ?>" placeholder="e.g., (02) 123-4567">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="program_graduated">Program Graduated</label>
                                    <input type="text" class="form-control" id="program_graduated" name="program_graduated" value="<?php echo e(old('program_graduated')); ?>" placeholder="e.g., Master of Science in Computer Science">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-footer">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <a href="<?php echo e(route('collegeadmin.faculty.index')); ?>" class="btn btn-default">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($layout, array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>