<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-user"></i>
        Faculty Details
        <small>{{ Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.faculty.index') }}">Faculty Management</a></li>
        <li class="active">Faculty Details</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Information</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.faculty.edit', $faculty->id) }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-pencil"></i> Edit
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h4><i class="fa fa-user"></i> Personal Information</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 35%">Faculty ID</th>
                                    <td>{{ $faculty->username ?? $faculty->id }}</td>
                                </tr>
                                <tr>
                                    <th>Full Name</th>
                                    <td>
                                        <strong>{{ strtoupper($faculty->lastname) }}, {{ strtoupper($faculty->name) }}</strong>
                                        @if($faculty->middlename)
                                            {{ strtoupper($faculty->middlename) }}
                                        @endif
                                        @if($faculty->extensionname)
                                            {{ strtoupper($faculty->extensionname) }}
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ $faculty->email }}</td>
                                </tr>
                                <tr>
                                    <th>College</th>
                                    <td><span class="label label-primary">{{ $college->college_name ?? $faculty->college_code }}</span></td>
                                </tr>
                                @if($info)
                                <tr>
                                    <th>Gender</th>
                                    <td>{{ $info->gender ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Mobile Number</th>
                                    <td>{{ $info->cell_no ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Emergency Contact</th>
                                    <td>{{ $info->{'emerg_cont_#'} ?? 'N/A' }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>

                        <!-- Professional Information -->
                        <div class="col-md-6">
                            <h4><i class="fa fa-briefcase"></i> Professional Information</h4>
                            @if($info)
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 35%">Department</th>
                                    <td>{{ $info->department ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Faculty Status</th>
                                    <td>
                                        @if($info->employee_type)
                                            <span class="label label-success">{{ $info->employee_type }}</span>
                                        @else
                                            <span class="label label-default">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Program Graduated</th>
                                    <td>{{ $info->program_graduated ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Degree Status</th>
                                    <td>{{ $info->degree_status ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Account Status</th>
                                    <td>
                                        @if($faculty->is_first_login)
                                            <span class="label label-warning">First Login Required</span>
                                        @else
                                            <span class="label label-success">Active</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                            @else
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> No additional professional information available.
                            </div>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <!-- Teaching Load Section -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="box box-info">
                                <div class="box-header with-border">
                                    <h3 class="box-title"><i class="fa fa-calendar"></i> Current Teaching Load</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" onclick="refreshTeachingLoad()">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div id="teaching-load">
                                        <p><i class="fa fa-spinner fa-spin"></i> Loading teaching load data...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.faculty.index') }}" class="btn btn-default">Back to List</a>
                    <a href="{{ route('collegeadmin.faculty_loading.generate_schedule', $faculty->id) }}" class="btn btn-success pull-right">
                        <i class="fa fa-calendar"></i> View Schedule
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
$(function() {
    loadTeachingLoad();
});

function loadTeachingLoad() {
    $('#teaching-load').html('<p><i class="fa fa-spinner fa-spin"></i> Loading teaching load data...</p>');

    $.ajax({
        url: "{{ url('/ajax/collegeadmin/faculty_loading/current_load') }}",
        type: 'GET',
        data: { instructor_id: {{ $faculty->id }} },
        success: function(data) {
            if (data && data.trim() !== '') {
                $('#teaching-load').html(data);
            } else {
                $('#teaching-load').html('<div class="alert alert-info"><i class="fa fa-info-circle"></i> No current teaching load assigned.</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading teaching load:', error);
            $('#teaching-load').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Error loading teaching load data. Please try again.</div>');
        }
    });
}

function refreshTeachingLoad() {
    loadTeachingLoad();
}
</script>
@endsection
