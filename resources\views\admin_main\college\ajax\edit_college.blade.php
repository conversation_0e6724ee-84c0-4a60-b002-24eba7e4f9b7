<form id="edit-college-form">
    {{ csrf_field() }}
    <input type="hidden" name="college_id" value="{{ $college->id }}">
    <div class="form-group">
        <label>College Code</label>
        <input type="text" name="college_code" id="edit-college-code" class="form-control" placeholder="Enter College Code" value="{{ $college->college_code }}" required>
    </div>
    <div class="form-group">
        <label>College Name</label>
        <input type="text" name="college_name" id="edit-college-name" class="form-control" placeholder="Enter College Name" value="{{ $college->college_name }}" required>
    </div>
    <div class="form-group">
        <label>Programs</label>
        <select name="program_code[]" class="form-control select2-modal" multiple="multiple" data-placeholder="Select or enter programs offered by this college">
            @if($college->program_code)
                @foreach(explode(',', $college->program_code) as $course)
                    @if(trim($course) != '')
                        <option value="{{ trim($course) }}" selected>{{ trim($course) }}</option>
                    @endif
                @endforeach
            @endif
        </select>
        <small class="text-muted">Enter multiple programs separated by commas <strong>( , )</strong></small>
        <!--<small class="text-muted">Note: You can also manage programs in detail using the Manage Programs button.</small> -->
    </div>
    <div class="form-group">
        <button type="button" id="btn-update-college" class="btn btn-primary btn-flat">Update</button>
    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2 with tags support for the modal
    $('.select2-modal').select2({
        tags: true,
        tokenSeparators: [','],
        placeholder: "Select or enter programs offered by this college",
        allowClear: true,
        width: '100%'
    });

    // Ensure the college code and name are properly displayed
    setTimeout(function() {
        // Force the text inputs to show their values
        var collegeCode = "{{ $college->college_code }}";
        var collegeName = "{{ $college->college_name }}";

        $('#edit-college-code').val(collegeCode);
        $('#edit-college-name').val(collegeName);

        console.log('Set college code to:', collegeCode);
        console.log('Set college name to:', collegeName);
    }, 100);

    // Handle form submission via AJAX
    $('#btn-update-college').click(function() {
        // Get form data
        var formData = $('#edit-college-form').serialize();

        // Send AJAX request
        $.ajax({
            type: "POST",
            url: "{{ url('/superadmin/college/update') }}",
            data: formData,
            success: function(response) {
                // Show success message
                toastr.success('College has been updated successfully!');

                // Close the modal
                $('#myModal').modal('hide');

                // Refresh the colleges table
                refreshCollegesTable();
            },
            error: function(xhr, status, error) {
                // Show error message
                var errors = xhr.responseJSON.errors;
                if (errors) {
                    var errorMessage = '';
                    $.each(errors, function(key, value) {
                        errorMessage += value + '<br>';
                    });
                    toastr.error(errorMessage);
                } else {
                    toastr.error('An error occurred while updating the college. Please try again.');
                }
            }
        });
    });

    // Function to refresh the colleges table
    function refreshCollegesTable() {
        var code = '';
        var name = '';
        var description = '';

        // Get selected values from Select2
        if ($('#search-code').length > 0 && $('#search-code').select2('data').length > 0 && $('#search-code').select2('data')[0].id) {
            code = $('#search-code').select2('data')[0].id;
        }

        if ($('#search-name').length > 0 && $('#search-name').select2('data').length > 0 && $('#search-name').select2('data')[0].id) {
            name = $('#search-name').select2('data')[0].id;
        }

        if ($('#search-description').length > 0 && $('#search-description').select2('data').length > 0 && $('#search-description').select2('data')[0].id) {
            description = $('#search-description').select2('data')[0].id;
        }

        $.ajax({
            type: "GET",
            url: "/ajax/superadmin/college/search",
            data: {
                code: code,
                name: name,
                description: description
            },
            success: function(data) {
                $('#colleges-table-body').html(data);
            },
            error: function(xhr, status, error) {
                console.error("Error refreshing colleges table:", error);
                toastr.error("An error occurred while refreshing the table. Please try again.");
            }
        });
    }
});
</script>
