<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request as RequestFacade;
use App\College;
use App\curriculum;
use App\CtrSection;
use App\offerings_infos_table;
use App\Services\ProgramService;

class CourseOfferingCurriculumAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get programs for the college admin's college
     */
    public function getPrograms()
    {
        if (RequestFacade::ajax()) {
            // Get the college code associated with the logged-in college admin
            $collegeCode = Auth::user()->college_code;
            
            // Get college information
            $college = College::where('college_code', $collegeCode)->first();
            
            if (!$college) {
                return response()->json([]);
            }
            
            // Get programs for this college
            $programService = new ProgramService();
            $programs = $programService->getProgramsForCollege($collegeCode);
            
            return response()->json($programs);
        }
    }

    /**
     * Get curriculum years for a program
     */
    public function getCurriculumYears()
    {
        if (RequestFacade::ajax()) {
            $programCode = Input::get('program_code');
            
            if (!$programCode) {
                return response()->json([]);
            }
            
            // Get the college code associated with the logged-in college admin
            $collegeCode = Auth::user()->college_code;
            
            // Get college information
            $college = College::where('college_code', $collegeCode)->first();
            
            if (!$college || !$college->hasProgram($programCode)) {
                return response()->json([]);
            }
            
            // Get curriculum years for this program
            $curriculumYears = curriculum::distinct()
                ->where('program_code', $programCode)
                ->where('college_code', $collegeCode)
                ->pluck('curriculum_year')
                ->toArray();
                
            return response()->json($curriculumYears);
        }
    }

    /**
     * Get sections for a program
     */
    public function getSections()
    {
        if (RequestFacade::ajax()) {
            $programCode = Input::get('program_code');
            
            if (!$programCode) {
                return response()->json([]);
            }
            
            // Get the college code associated with the logged-in college admin
            $collegeCode = Auth::user()->college_code;
            
            // Get college information
            $college = College::where('college_code', $collegeCode)->first();
            
            if (!$college || !$college->hasProgram($programCode)) {
                return response()->json([]);
            }
            
            // Get active sections for this program
            $sections = CtrSection::where('program_code', $programCode)
                ->where('is_active', 1)
                ->get();
                
            return response()->json($sections);
        }
    }

    /**
     * Get curriculum details for a program and year
     */
    public function getCurriculumDetails()
    {
        if (RequestFacade::ajax()) {
            $programCode = Input::get('program_code');
            $curriculumYear = Input::get('curriculum_year');
            
            if (!$programCode || !$curriculumYear) {
                return response()->json([]);
            }
            
            // Get the college code associated with the logged-in college admin
            $collegeCode = Auth::user()->college_code;
            
            // Get college information
            $college = College::where('college_code', $collegeCode)->first();
            
            if (!$college || !$college->hasProgram($programCode)) {
                return response()->json([]);
            }
            
            // Get curriculum subjects
            $subjects = curriculum::where('program_code', $programCode)
                ->where('curriculum_year', $curriculumYear)
                ->where('college_code', $collegeCode)
                ->where('is_active', 1)
                ->orderBy('level')
                ->orderBy('period')
                ->orderBy('control_code')
                ->get();
                
            return response()->json($subjects);
        }
    }

    /**
     * Get available curricula for a section
     */
    public function getAvailableCurricula()
    {
        if (RequestFacade::ajax()) {
            $sectionId = Input::get('section_id');
            
            if (!$sectionId) {
                return response()->json([]);
            }
            
            // Get the college code associated with the logged-in college admin
            $collegeCode = Auth::user()->college_code;
            
            // Get section information
            $section = CtrSection::find($sectionId);
            
            if (!$section) {
                return response()->json([]);
            }
            
            // Verify the section belongs to a program in this college
            $programCode = $section->program_code;
            $college = College::where('college_code', $collegeCode)->first();
            
            if (!$college || !$college->hasProgram($programCode)) {
                return response()->json([]);
            }
            
            // Get offered curricula IDs
            $offeredIds = offerings_infos_table::where('section_name', $section->section_name)
                ->pluck('curriculum_id')
                ->toArray();
                
            // Get available curricula
            $curricula = curriculum::where('program_code', $section->program_code)
                ->where('college_code', $collegeCode)
                ->where('level', $section->level)
                ->where('is_active', 1)
                ->whereNotIn('id', $offeredIds)
                ->get();
                
            return response()->json($curricula);
        }
    }

    /**
     * Get offered curricula for a section
     */
    public function getSectionOfferings()
    {
        if (RequestFacade::ajax()) {
            $sectionId = Input::get('section_id');
            
            if (!$sectionId) {
                return response()->json([]);
            }
            
            // Get the college code associated with the logged-in college admin
            $collegeCode = Auth::user()->college_code;
            
            // Get section information
            $section = CtrSection::find($sectionId);
            
            if (!$section) {
                return response()->json([]);
            }
            
            // Verify the section belongs to a program in this college
            $programCode = $section->program_code;
            $college = College::where('college_code', $collegeCode)->first();
            
            if (!$college || !$college->hasProgram($programCode)) {
                return response()->json([]);
            }
            
            // Get offered curricula
            $offerings = offerings_infos_table::where('section_name', $section->section_name)
                ->get();
                
            // Get curriculum details for each offering
            $result = [];
            foreach ($offerings as $offering) {
                $curriculum = curriculum::find($offering->curriculum_id);
                if ($curriculum) {
                    $offering->curriculum = $curriculum;
                    $result[] = $offering;
                }
            }
                
            return response()->json($result);
        }
    }
}
