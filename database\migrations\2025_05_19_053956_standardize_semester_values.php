<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class StandardizeSemesterValues extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Standardize semester values in the curriculum table
        DB::table('curricula')
            ->where('period', 'like', '%first%')
            ->orWhere('period', 'like', '%1st%')
            ->update(['period' => 'First Semester']);

        DB::table('curricula')
            ->where('period', 'like', '%second%')
            ->orWhere('period', 'like', '%2nd%')
            ->update(['period' => 'Second Semester']);

        DB::table('curricula')
            ->where('period', 'like', '%summer%')
            ->orWhere('period', 'like', '%mid%')
            ->orWhere('period', 'like', '%mid-year%')
            ->update(['period' => 'Mid-year']);

        // Copy semester values from curriculum to offerings_infos
        DB::statement('
            UPDATE offerings_infos oi
            JOIN curricula c ON oi.curriculum_id = c.id
            SET oi.semester = c.period
            WHERE oi.semester IS NULL
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // No need to revert standardization, but we can clear the semester field in offerings_infos
        DB::table('offerings_infos')->update(['semester' => null]);
    }
}
