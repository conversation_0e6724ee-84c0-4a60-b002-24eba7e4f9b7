<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Auth;
use Excel;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use App\Subject;

class AddCurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    function index()
    {
        $colleges = \App\College::where('is_active', 1)->get();

        // Get all program codes and names from program_names table
        $programNames = DB::table('program_names')->get();
        $programNameMap = [];
        foreach ($programNames as $program) {
            $programNameMap[$program->program_code] = $program->program_name;
        }

        // Get all program codes and names
        $allPrograms = [];

        // Get programs grouped by college for JavaScript
        $programsByCollege = [];
        foreach ($colleges as $college) {
            $programCodes = $college->courses_array;

            if (!empty($programCodes)) {
                $programsByCollege[$college->college_code] = [];

                foreach ($programCodes as $code) {
                    $name = $programNameMap[$code] ?? $code;

                    // Add to programs by college
                    $programsByCollege[$college->college_code][] = [
                        'program_code' => $code,
                        'program_name' => $name
                    ];

                    // Add to all programs list
                    $allPrograms[] = [
                        'program_code' => $code,
                        'program_name' => $name,
                        'college_code' => $college->college_code
                    ];
                }
            }
        }

        // Remove duplicates from all programs list
        $uniquePrograms = [];
        $seenCodes = [];

        foreach ($allPrograms as $program) {
            if (!in_array($program['program_code'], $seenCodes)) {
                $uniquePrograms[] = (object) $program; // Convert to object to match previous format
                $seenCodes[] = $program['program_code'];
            }
        }

        return view('/superadmin/curriculum_management/add_curriculum', compact('uniquePrograms', 'colleges', 'programsByCollege'));
    }

    public function save_changes(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'curriculum_year' => 'required|array',
            'program_code' => 'required|array',
            'course_code' => 'required|array',
            'course_name' => 'required|array',
            'lec' => 'required|array',
            'lab' => 'required|array',
            'units' => 'required|array',
            'complab' => 'required|array',
            'period' => 'required|array',
            'level' => 'required|array'
        ]);

        // Process college codes
        $collegeCodeMap = [];
        foreach ($request->program_code as $index => $programCode) {
            // First try to get college from hidden_college_code
            if (isset($request->hidden_college_code[$index]) && !empty($request->hidden_college_code[$index])) {
                $collegeCodeMap[$index] = $request->hidden_college_code[$index];
            }
            // Then try to get from college_code
            elseif (isset($request->college_code[$index]) && !empty($request->college_code[$index])) {
                $collegeCodeMap[$index] = $request->college_code[$index];
            }
            // If still not found, try to find a college that has this program
            else {
                $found = false;
                $colleges = \App\College::all();
                foreach ($colleges as $college) {
                    if ($college->hasProgram($programCode)) {
                        $collegeCodeMap[$index] = $college->college_code;
                        $found = true;
                        break;
                    }
                }

                // If no college found, assign to unassigned college
                if (!$found) {
                    $unassignedCollege = \App\College::firstOrCreate(
                        ['college_code' => 'UNASSIGNED'],
                        [
                            'college_name' => 'Unassigned College',
                            'description' => $programCode,
                            'is_active' => 1
                        ]
                    );
                    $collegeCodeMap[$index] = 'UNASSIGNED';
                }
            }
        }

        // Now save all curriculum entries
        for ($x = 0; $x < count($request->curriculum_year); $x++) {
            $curricula = new \App\curriculum;
            $curricula->curriculum_year = $request->curriculum_year[$x];
            $curricula->program_code = $request->program_code[$x];

            // Set college code
            $curricula->college_code = $collegeCodeMap[$x];

            // Get program name from program_names table or use program code as fallback
            $programName = DB::table('program_names')
                ->where('program_code', $request->program_code[$x])
                ->first();
            $curricula->program_name = $programName ? $programName->program_name : $request->program_code[$x];

            // Save program name if it doesn't exist in program_names table
            if (!$programName) {
                DB::table('program_names')->insert([
                    'program_code' => $request->program_code[$x],
                    'program_name' => $request->program_code[$x], // Use code as name if not found
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            // Check if program needs to be added to this college
            $college = \App\College::where('college_code', $curricula->college_code)->first();
            if ($college && !$college->hasProgram($request->program_code[$x])) {
                // Add program to college description
                $college->addProgram($request->program_code[$x], $curricula->program_name);
            }

            // Find or create the subject
            $subject = Subject::findOrCreateByCode(
                $request->course_code[$x],
                [
                    'course_name' => $request->course_name[$x],
                    'lec' => $request->lec[$x],
                    'lab' => $request->lab[$x],
                    'units' => $request->units[$x],
                    'is_complab' => $request->complab[$x]
                ]
            );

            // Set curriculum fields
            $curricula->control_code = $request->course_code[$x];
            $curricula->course_code = $request->course_code[$x];
            $curricula->course_name = $request->course_name[$x];
            $curricula->lec = $request->lec[$x];
            $curricula->lab = $request->lab[$x];
            $curricula->units = $request->units[$x];
            $curricula->level = $request->level[$x];
            $curricula->period = $request->period[$x];
            $curricula->percent_tuition = 100;
            $curricula->is_complab = $request->complab[$x];
            $curricula->is_active = 1; // Set as active by default
            $curricula->subject_id = $subject->id; // Link to the subject
            $curricula->save();
        }

        Session::flash('success', 'Successfully saved curriculum data!');
        Log::info('User ' . Auth::user()->name . " added a new curriculum");

        return redirect(url('/superadmin/curriculum_management/curriculum'));
    }
}
