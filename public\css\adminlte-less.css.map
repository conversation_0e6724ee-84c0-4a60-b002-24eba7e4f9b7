{"version": 3, "sources": ["AdminLTE.css", "core.less", "../bootstrap-less/mixins/clearfix.less", "../bootstrap-less/mixins/vendor-prefixes.less", "../bootstrap-less/mixins/grid.less", "header.less", "mixins.less", "sidebar.less", "sidebar-mini.less", "control-sidebar.less", "dropdown.less", "../bootstrap-less/mixins/border-radius.less", "forms.less", "progress-bars.less", "../bootstrap-less/mixins/progress-bar.less", "../bootstrap-less/mixins/gradients.less", "small-box.less", "boxes.less", "info-box.less", "timeline.less", "buttons.less", "../bootstrap-less/mixins/opacity.less", "callout.less", "alerts.less", "navs.less", "products.less", "table.less", "labels.less", "direct-chat.less", "users-list.less", "carousel.less", "modal.less", "social-widgets.less", "mailbox.less", "lockscreen.less", "login_and_register.less", "404_500_errors.less", "invoice.less", "profile.less", "bootstrap-social.less", "../bootstrap-less/mixins/buttons.less", "fullcalendar.less", "select2.less", "miscellaneous.less", "print.less"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,mHAAmH;AACnH;;;GAGG;ACPH;;EAEE,aAAA;CDSD;ACRC;;EACE,aAAA;CDWH;ACPD;EACE,+EAAA;EACA,iBAAA;EACA,mBAAA;EACA,iBAAA;CDSD;AACD,YAAY;ACNZ;EAEE,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,iBAAA;CDOD;AEnBC;;EAEE,aAAA;EACA,eAAA;CFqBH;AEnBC;EACE,YAAA;CFqBH;ACdC;EACE,kBAAA;EACA,eAAA;EACA,iBAAA;EACA,uCAAA;EACA,mBAAA;CDgBH;ACZD;EACE,oDAAA;CDcD;AACD;;;GAGG;ACXH;;;EEoME,gFAAA;EAGA,gFAAA;EAAA,wEAAA;EAAA,gEAAA;EAAA,oGAAA;EFhMA,mBAAA;EACA,aAAA;CDaD;ACXC;;;EACE,eAAA;CDeH;ACbC;EAqHF;;;IApHI,eAAA;GDkBD;CACF;ACfG;EAgHJ;;;IA/GM,eAAA;GDoBH;CACF;AChBG;EA0GJ;;;IEdE,uCAAA;IAGA,+BAAA;GHtEC;CACF;ACpBD;;EAEE,iBAAA;EACA,0BAAA;EACA,aAAA;CDsBD;ACnBD;EACE,iBAAA;EACA,cAAA;EACA,YAAA;EACA,8BAAA;CDqBD;AACD,kBAAkB;AClBlB;;;EAII,gBAAA;CDmBH;ACvBD;EAOI,OAAA;EACA,SAAA;EACA,QAAA;CDmBH;AC5BD;;EAaI,kBAAA;CDmBH;AClBG;EAuEJ;;IAtEM,mBAAA;GDsBH;CACF;ACpBC;EAEI,gBAAA;CDqBL;AChBD;;;;;;;EDwBE,gBAAgB;EG4EhB,yBAAA;EAEA,iBAAA;CH1ED;AACD,aAAa;ACfb;EACE,kBAAA;EACA,cAAA;EG3HA,mBAAA;EACA,kBAAA;EACA,mBAAA;EACA,oBAAA;CJ6ID;AACD,kBAAkB;ACjBlB;;;;;;;;;;;;EAYE,2CAAA;CDmBD;AACD,mBAAmB;AChBnB;EACE,eAAA;CDkBD;ACfD;;;EAGE,cAAA;EACA,sBAAA;EACA,eAAA;CDiBD;AACD,iBAAiB;ACdjB;EACE,sBAAA;EACA,gBAAA;CDgBD;AClBD;EAKI,YAAA;EACA,eAAA;EACA,gBAAA;CDgBH;AACD;;;GAGG;AKzLH;EACE,mBAAA;EACA,kBAAA;EACA,cAAA;CL2LD;AK9LD;EFgNE,iDAAA;EAEA,yCAAA;EE3ME,iBAAA;EACA,mBAAA;EACA,aAAA;EACA,iBAAA;EACA,iBAAA;CL6LH;AK5LG;EACE,eAAA;CL8LL;AK3MD;EAkBI,qCAAA;EACA,0BAAA;CL4LH;AK3LG;;EAEE,iCAAA;EACA,qCAAA;CL6LL;AK3LG;EACE,YAAA;EACA,WAAA;CL6LL;AK3LG;EACE,YAAA;CL6LL;AK3LG;EACE,YAAA;CL6LL;AK9ND;;EAuCI,aAAA;CL2LH;AK1LG;EA2MJ;;IAzMQ,eAAA;IACA,wBAAA;GL6LL;CACF;AKzLG;EAmMJ;IAlMM,YAAA;GL4LH;EK3LG;IACE,oBAAA;GL6LL;EKGH;IA5LQ,eAAA;IACA,UAAA;GL4LL;CACF;AKrPD;EA8DI,YAAA;EACA,8BAAA;EACA,uBAAA;EACA,mBAAA;EAEA,yBAAA;CLyLH;AKxLG;EACE,iBAAA;CL0LL;AKxLG;EACE,YAAA;CL0LL;AKxLG;;EAEE,wBAAA;CL0LL;AKtQD;EAgFI,cAAA;CLyLH;AKzQD;;;EAuFM,kBAAA;CLuLL;AK9QD;EA6FI,mBAAA;EACA,SAAA;EACA,WAAA;EACA,mBAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;CLoLH;AKvRD;EFgNE,2CAAA;EAEA,mCAAA;EEzGE,eAAA;EACA,YAAA;EACA,aAAA;EACA,gBAAA;EACA,kBAAA;EACA,mBAAA;EACA,aAAA;EACA,4DAAA;EACA,gBAAA;EACA,iBAAA;EACA,iBAAA;CLoLH;AKvSD;EAwHM,eAAA;CLkLL;AK1SD;EA2HM,cAAA;CLkLL;AK7SD;EAgII,YAAA;CLgLH;AK3KD;EACE,mBAAA;EACA,0BAAA;CL6KD;AK/KD;EAKI,UAAA;EACA,gBAAA;CL6KH;AKnLD;EAQM,gBAAA;EACA,sBAAA;EACA,kBAAA;EACA,iBAAA;CL8KL;AKzLD;EAgBI,aAAA;EACA,wBAAA;EACA,cAAA;EACA,iBAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;EACA,UAAA;EACA,YAAA;EC1FF,mBAAA;CNuQD;AKrMD;EA2BM,YAAA;EACA,sBAAA;EACA,sBAAA;CL6KL;AK1MD;;;EA+BQ,kBAAA;CLgLP;AK/MD;EAmCM,kBAAA;CL+KL;AK3KC;EAuEF;IArEM,mBAAA;IACA,gBAAA;IACA,OAAA;IACA,SAAA;IACA,YAAA;IACA,oBAAA;IACA,mBAAA;GL6KH;EK9GH;IA7DQ,eAAA;GL8KL;CACF;AKzKD;EACE,YAAA;EACA,UAAA;EACA,UAAA;EACA,mBAAA;CL2KD;AKvKD;EACE;IACE,YAAA;GLyKD;EKrKD;IACE,UAAA;IACA,YAAA;GLuKD;EKpKD;IACE,kBAAA;IACA,qBAAA;IACA,kBAAA;GLsKD;CACF;AKlKD;EACE;IACE,mBAAA;GLoKD;EKrKD;;IAII,YAAA;IACA,YAAA;GLqKH;EK1KD;IAQI,UAAA;GLqKH;EK7KD;IAWI,aAAA;GLqKH;CACF;AKhKC;EAUF;IATI,uBAAA;GLmKD;EK1JH;IAPM,eAAA;IACA,mBAAA;IACA,OAAA;IACA,YAAA;GLoKH;CACF;AACD;;;GAGG;AOtZH;;EAEE,mBAAA;EACA,OAAA;EACA,QAAA;EACA,kBAAA;EACA,iBAAA;EACA,aAAA;EACA,aAAA;EJgOA,+EAAA;EAGA,+EAAA;EAAA,uEAAA;EAAA,+DAAA;EAAA,mGAAA;CHyLD;AOvZC;EAqJF;;IApJI,mBAAA;GP2ZD;CACF;AO1ZC;EAkJF;;IJVE,wCAAA;IAGA,gCAAA;GHuRC;CACF;AO/ZG;EA8IJ;;IJVE,wCAAA;IAGA,gCAAA;GHgSC;CACF;AOnaG;EAyIJ;;IJVE,mCAAA;IAGA,2BAAA;GHySC;CACF;AOtaD;EACE,qBAAA;CPwaD;AOpaD;EAEI,0BAAA;CPqaH;AOhaD;EACE,mBAAA;EACA,YAAA;EACA,cAAA;EACA,iBAAA;CPkaD;AE3cC;;EAEE,aAAA;EACA,eAAA;CF6cH;AE3cC;EACE,YAAA;CF6cH;AO9aD;EAOI,YAAA;EACA,gBAAA;EACA,aAAA;CP0aH;AOnbD;EAYI,0BAAA;EACA,eAAA;EACA,mBAAA;EACA,WAAA;CP0aH;AOzbD;EAiBM,iBAAA;EACA,mBAAA;CP2aL;AO7bD;EAqBM,sBAAA;EACA,mBAAA;EACA,gBAAA;EACA,gBAAA;CP2aL;AOncD;;;EA4BQ,kBAAA;CP4aP;AOraD;EACE,iBAAA;EACA,UAAA;EACA,WAAA;CPuaD;AO1aD;EAMI,mBAAA;EACA,UAAA;EACA,WAAA;CPuaH;AO/aD;EAUM,4BAAA;EACA,eAAA;CPwaL;AOnbD;;;EAeQ,YAAA;CPyaP;AOxbD;;EAoBM,kBAAA;CPwaL;AO5bD;EAuBM,gBAAA;CPwaL;AO/bD;EA2BI,6BAAA;EACA,gBAAA;CPuaH;AOncD;;EAgCI,YAAA;EACA,aAAA;EACA,WAAA;EACA,mBAAA;CPuaH;AO1cD;EAsCI,mBAAA;EACA,SAAA;EACA,YAAA;EACA,iBAAA;CPuaH;AOhdD;;EJsFE,kCAAA;EAGA,0BAAA;CH8XD;AOvdD;EAiDM,eAAA;CPyaL;AO1dD;EAuDI,cAAA;EACA,iBAAA;EACA,WAAA;EACA,UAAA;EACA,kBAAA;CPsaH;AOjeD;EA6DM,mBAAA;CPuaL;AOpeD;EAgEM,UAAA;CPuaL;AOveD;EAkEQ,0BAAA;EACA,eAAA;EACA,gBAAA;CPwaP;AO5eD;;;EAwEU,YAAA;CPyaT;AOjfD;;;;EA8EU,YAAA;CPyaT;AACD;;GAEG;AQxkBD;EAEE;;;IAMI,6BAAA;IACA,aAAA;GRskBL;EQ7kBC;ILsJF,mCAAA;IAGA,2BAAA;IK3IM,uBAAA;IACA,aAAA;GRskBL;EQrlBC;IAoBM,mBAAA;GRokBP;EQxlBC;IAsBQ,gBAAA;GRqkBT;EQ3lBC;IAyBQ,6BAAA;GRqkBT;EQlkBO;IAEI,gCAAA;GRmkBX;EQjmBC;IAoCQ,iBAAA;IACA,oBAAA;IACA,gCAAA;GRgkBT;EQ5jBO;;IAMI,0BAAA;IACA,mBAAA;IACA,aAAA;IACA,WAAA;GR0jBX;EQnkBO;IAcI,OAAA;IACA,kBAAA;IACA,4BAAA;IACA,0BAAA;GRwjBX;EQzkBO;IAqBI,6BAAA;IACA,aAAA;IACA,sBAAA;IACA,uBAAA;IACA,sBAAA;IACA,aAAA;GRujBX;EQjlBO;IA4BM,cAAA;GRwjBb;EQplBO;IAgCI,UAAA;IACA,eAAA;GRujBX;EQloBC;;;;;;IAyFI,yBAAA;IACA,iCAAA;GRijBL;EQ3oBC;IAgGM,YAAA;GR8iBP;EQ9oBC;IAkGQ,eAAA;IACA,mBAAA;IACA,oBAAA;IACA,gBAAA;GR+iBT;EQppBC;IAwGQ,cAAA;GR+iBT;EQvpBC;IA8GM,kBAAA;GR4iBP;CACF;AQriBD;;;EAGE,oBAAA;EACA,iBAAA;CRuiBD;AQpiBD;EACE,kBAAA;CRsiBD;AQniBD;;EAEE,iBAAA;EACA,oBAAA;CRqiBD;AQliBD;EACE,mBAAA;CRoiBD;AQriBD;EAGI,mBAAA;EACA,YAAA;EACA,SAAA;EACA,iBAAA;CRqiBH;AACD;;GAEG;ASzrBH;EACE,gBAAA;EACA,cAAA;EACA,UAAA;CT2rBD;ASvrBD;;EAEE,OAAA;EACA,cAAA;EACA,aAAA;ENqMA,2CAAA;EAEA,mCAAA;CHqfD;ASvrBD;EACE,mBAAA;EACA,kBAAA;EACA,cAAA;CTyrBD;ASvrBC;EAuQF;IAtQI,mBAAA;GT0rBD;CACF;ASjsBD;EAUI,mBAAA;CT0rBH;AStrBG;;EAEE,SAAA;CTwrBL;ASlrBD;;EAGI,SAAA;CTmrBH;ASjrBC;EAiPF;;;IA7OM,oBAAA;GTmrBH;CACF;AS3qBK;;;EAGE,qBAAA;CT6qBP;ASnrBD;EHcE,iBAAA;CNwqBD;ASzqBK;;EAEE,iBAAA;EACA,mBAAA;EACA,mCAAA;EACA,qCAAA;CT2qBP;AS7rBD;EAqBQ,gBAAA;CT2qBP;ASrqBO;;;;EAIE,iBAAA;EACA,mBAAA;EACA,oBAAA;CTuqBT;ASjqBC;EAgMF;IA/LI,eAAA;GToqBD;ESreH;IA7LM,oBAAA;GTqqBH;CACF;AShqBD;EACE,iBAAA;EACA,gBAAA;EACA,gBAAA;EACA,oBAAA;CTkqBD;AS9pBD;EACE,eAAA;EACA,iBAAA;EACA,gBAAA;CTgqBD;AS5pBD;EACE,iBAAA;EACA,WAAA;EACA,gBAAA;CT8pBD;ASjqBD;EAMI,eAAA;EACA,mBAAA;CT8pBH;AEjxBC;;EAEE,aAAA;EACA,eAAA;CFmxBH;AEjxBC;EACE,YAAA;CFmxBH;AS7qBD;EASM,cAAA;CTuqBL;AShrBD;EAaI,YAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,kBAAA;CTsqBH;ASxrBD;EAqBI,kBAAA;EACA,gBAAA;CTsqBH;AS5rBD;EAwBM,UAAA;CTuqBL;AS/rBD;EA2BM,UAAA;EACA,gBAAA;CTuqBL;ASnsBD;EAgCI,UAAA;CTsqBH;ASjqBD;EACE,eAAA;CTmqBD;ASjqBC;;EAEE,oBAAA;CTmqBH;ASxqBD;EASI,uBAAA;CTkqBH;AS3qBD;EAYQ,oBAAA;EACA,eAAA;CTkqBP;AShqBO;;;EAGE,2BAAA;EACA,6BAAA;CTkqBT;AShqBO;;;EAGE,oBAAA;CTkqBT;AShqBO;EACE,YAAA;CTkqBT;AS5pBS;;;;EAIE,oBAAA;EACA,YAAA;CT8pBX;ASpsBD;;EA+CI,YAAA;CTypBH;ASnpBO;EACE,oBAAA;CTqpBT;AS3sBD;EA0DY,eAAA;CTopBX;AS3oBD;EACE,eAAA;CT6oBD;AS3oBC;;EAEE,oBAAA;EACA,+BAAA;CT6oBH;ASnpBD;EAUI,uBAAA;CT4oBH;AStpBD;EAaQ,oBAAA;EACA,YAAA;CT4oBP;AS1oBO;;;EAGE,2BAAA;EACA,6BAAA;CT4oBT;AS1oBO;;;EAGE,oBAAA;CT4oBT;AStoBS;;;;EAIE,oBAAA;EACA,YAAA;CTwoBX;AS5qBD;;EA6CI,YAAA;CTmoBH;AShrBD;EAiDI,mBAAA;CTkoBH;AS/nBO;EACE,oBAAA;CTioBT;AStrBD;EAyDY,eAAA;CTgoBX;AACD;;;GAGG;AACH,wBAAwB;AUz5BxB;EACE,iBAAA;EACA,mBAAA;CV25BD;AU75BD;EAII,YAAA;CV45BH;AUh6BD;;;EASI,mBAAA;CV45BH;AUr6BD;EAYI,0BAAA;EACA,YAAA;CV45BH;AUz6BD;EAgBI,uBAAA;CV45BH;AUv5BD;;;EAQI,aAAA;EAEA,iBAAA;EACA,UAAA;EACA,UAAA;CVm5BH;AU/5BD;;;EAMM,mBAAA;CV85BL;AUp6BD;;;EJyDE,4BAAA;EAEA,6BAAA;EAEA,8BAAA;EAEA,6BAAA;EI9CE,0BAAA;EACA,kBAAA;EACA,iCAAA;EACA,eAAA;EACA,gBAAA;CV45BH;AUj7BD;;;EJyDE,0BAAA;EAEA,2BAAA;EAEA,gCAAA;EAEA,+BAAA;EIpCE,gBAAA;EACA,uBAAA;EACA,kBAAA;EACA,iCAAA;EACA,uBAAA;EAKA,mBAAA;CV25BH;AU/5BG;EAmSJ;;;IAlSM,4BAAA;IACA,uBAAA;GVo6BH;CACF;AUj6BG;;;EACE,sBAAA;EACA,oBAAA;CVq6BL;AU78BD;;;EA8CI,kBAAA;EACA,UAAA;EACA,WAAA;EACA,iBAAA;EACA,mBAAA;CVo6BH;AUt9BD;;;EAoDM,eAAA;EACA,oBAAA;EVu6BJ,gCAAgC;EUt6B5B,iCAAA;CVw6BL;AUt6BK;;;EACE,oBAAA;EACA,sBAAA;CV06BP;AUn6BD;EAIM,eAAA;EACA,iBAAA;EACA,wBAAA;EACA,cAAA;CVk6BL;AUz6BD;;;EAYQ,YAAA;CVk6BP;AU15BD;EAKM,UAAA;EAEA,mBAAA;CVu5BL;AU95BD;EAUQ,4BAAA;EACA,YAAA;EACA,aAAA;CVu5BP;AUn6BD;EAgBQ,WAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;EACA,mBAAA;CVs5BP;AU16BD;EAuBU,eAAA;EACA,gBAAA;EACA,mBAAA;EACA,OAAA;EACA,SAAA;CVs5BT;AUj7BD;EAgCQ,mBAAA;EACA,gBAAA;EACA,eAAA;CVo5BP;AEzhCC;;EAEE,aAAA;EACA,eAAA;CF2hCH;AEzhCC;EACE,YAAA;CF2hCH;AUj5BD;EAGM,cAAA;CVi5BL;AUp5BD;EAMQ,gBAAA;EACA,WAAA;EACA,mBAAA;EACA,eAAA;CVi5BP;AU15BD;EAaQ,WAAA;EACA,UAAA;CVg5BP;AUz4BD;EC/KE,2BAAA;EACA,0BAAA;EDiLE,mBAAA;EACA,oBAAA;EACA,aAAA;CV24BH;AUz4BG;;EC5KF,gCAAA;EACA,+BAAA;CXyjCD;AUr5BD;EAaM,cAAA;EACA,cAAA;EACA,mBAAA;CV24BL;AU15BD;EAkBQ,WAAA;EACA,aAAA;EACA,YAAA;EACA,kBAAA;EACA,0BAAA;EACA,uCAAA;CV24BP;AUl6BD;EA0BQ,WAAA;EACA,YAAA;EACA,gCAAA;EACA,gBAAA;EAEA,iBAAA;CV04BP;AUz6BD;EAiCU,eAAA;EACA,gBAAA;CV24BT;AU76BD;EAyCM,cAAA;EACA,iCAAA;EACA,8BAAA;CVu4BL;AEvlCC;;EAEE,aAAA;EACA,eAAA;CFylCH;AEvlCC;EACE,YAAA;CFylCH;AU17BD;EA8CQ,uBAAA;CV+4BP;AU94BO;EA6HR;IA5HU,4BAAA;IACA,uBAAA;GVi5BP;CACF;AUn8BD;EAwDM,0BAAA;EACA,cAAA;CV84BL;AE5mCC;;EAEE,aAAA;EACA,eAAA;CF8mCH;AE5mCC;EACE,YAAA;CF8mCH;AU/8BD;EA4DQ,eAAA;CVs5BP;AUp5BS;EA8GV;IA7GY,0BAAA;GVu5BT;CACF;AUv9BD;EAsEI,YAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,iBAAA;CVo5BH;AUn5BG;EAgGJ;IA/FM,YAAA;IACA,gBAAA;IACA,iBAAA;IACA,kBAAA;GVs5BH;CACF;AACD;qEACqE;AUl5BrE;EACE,gDAAA;EAAA,wCAAA;EPxPA,qCAAA;EAEA,6BAAA;CH6oCD;AUl5BD;EACE;IACE,+DAAA;IAAA,uDAAA;IACA,4CAAA;IAAA,oCAAA;IACA,WAAA;GVo5BD;EUj5BD;IACE,gEAAA;IAAA,wDAAA;IACA,4CAAA;IAAA,oCAAA;GVm5BD;EUh5BD;IACE,+DAAA;IAAA,uDAAA;IACA,WAAA;GVk5BD;EU/4BD;IACE,+DAAA;IAAA,uDAAA;GVi5BD;EU94BD;IACE,sCAAA;IAAA,8BAAA;GVg5BD;CACF;AU74BD;EACE;IACE,+DAAA;IACA,4CAAA;IACA,WAAA;GV+4BD;EU54BD;IACE,gEAAA;IACA,4CAAA;GV84BD;EU34BD;IACE,+DAAA;IACA,WAAA;GV64BD;EU14BD;IACE,+DAAA;GV44BD;EUz4BD;IACE,sCAAA;GV24BD;CACF;AACD,kCAAkC;AUx4BlC;EAEI,mBAAA;CVy4BH;AU34BD;EAIM,mBAAA;EACA,SAAA;EACA,WAAA;CV04BL;AUr4BD;EACE;IACE,aAAA;GVu4BD;EUx4BD;IAGI,iBAAA;GVw4BH;EU34BD;IAKM,mBAAA;IACA,UAAA;IACA,WAAA;IACA,uBAAA;IACA,iBAAA;GVy4BL;CACF;AACD;;;GAGG;AYnuCH;ENoEE,iBAAA;EMlEA,iBAAA;EACA,sBAAA;CZquCD;AYpuCC;EACE,sBAAA;EACA,iBAAA;CZsuCH;AYpuCC;;;EAGE,YAAA;EACA,WAAA;CZsuCH;AYnuCC;EACE,yBAAA;EACA,sBAAA;EACA,iBAAA;CZquCH;AYhuCC;EAEI,eAAA;CZiuCL;AYnuCC;;EAMI,sBAAA;EACA,iBAAA;CZiuCL;AYxuCC;EAUI,eAAA;CZiuCL;AY7tCC;EAEI,eAAA;CZ8tCL;AYhuCC;;EAMI,sBAAA;EACA,iBAAA;CZ8tCL;AYruCC;EAUI,eAAA;CZ8tCL;AY1tCC;EAEI,eAAA;CZ2tCL;AY7tCC;;EAMI,sBAAA;EACA,iBAAA;CZ2tCL;AYluCC;EAUI,eAAA;CZ2tCL;AACD,iBAAiB;AYttCjB;ENCE,iBAAA;EMEE,sBAAA;EACA,uBAAA;CZutCH;AACD,mBAAmB;AYjtCf;;ENVF,iBAAA;CN+tCD;AY/sCD;EACE,gBAAA;CZitCD;AACD,gDAAgD;AY9sChD;EACE,kBAAA;CZgtCD;AY7sCD;;;EAGE,kBAAA;CZ+sCD;AY5sCD;;;EAGE,kBAAA;CZ8sCD;AACD;;;GAGG;AatzCH;;EVqEE,iBAAA;CHsvCD;AaxzCC;;;;EP+DA,mBAAA;CN+vCD;AACD,oBAAoB;AazzCpB;;EAEE,aAAA;Cb2zCD;Aa1zCC;;;;EPsDA,mBAAA;CN0wCD;Aa3zCD;;EAEE,YAAA;Cb6zCD;Aa5zCC;;;;EP8CA,mBAAA;CNoxCD;Aa7zCD;;EAEE,YAAA;Cb+zCD;Aa9zCC;;;;EPsCA,mBAAA;CN8xCD;AACD,mBAAmB;Aa/zCnB;EACE,mBAAA;EACA,YAAA;EACA,cAAA;EACA,sBAAA;EACA,mBAAA;Cbi0CD;Aat0CD;EAOI,YAAA;EACA,mBAAA;EACA,UAAA;Cbk0CH;Aa9zCC;;EAEE,YAAA;Cbg0CH;Aa7zCC;;EAEE,YAAA;Cb+zCH;Aa7zCC;;EAEE,WAAA;Cb+zCH;Aa1zCD;EAEI,iBAAA;Cb2zCH;Aa7zCD;EAKI,aAAA;Cb2zCH;AACD,2DAA2D;AavzC3D;EAEI,UAAA;CbwzCH;AalzCD;;ECpFE,0BAAA;Cd04CD;Acv4CC;;ECgDE,8MAAA;EAEA,sMAAA;Cf21CH;AavzCD;;ECzFE,0BAAA;Cdo5CD;Acj5CC;;ECgDE,8MAAA;EAEA,sMAAA;Cfq2CH;Aa5zCD;;EC9FE,0BAAA;Cd85CD;Ac35CC;;ECgDE,8MAAA;EAEA,sMAAA;Cf+2CH;Aaj0CD;;ECnGE,0BAAA;Cdw6CD;Acr6CC;;ECgDE,8MAAA;EAEA,sMAAA;Cfy3CH;Aat0CD;;ECxGE,0BAAA;Cdk7CD;Ac/6CC;;ECgDE,8MAAA;EAEA,sMAAA;Cfm4CH;AACD;;;GAGG;AgB17CH;EVmEE,mBAAA;EUjEA,mBAAA;EACA,eAAA;EACA,oBAAA;EACA,yCAAA;ChB47CD;AgBj8CD;EAQI,cAAA;ChB47CH;AgBp8CD;EAYI,mBAAA;EACA,mBAAA;EACA,eAAA;EACA,YAAA;EACA,gCAAA;EACA,eAAA;EACA,YAAA;EACA,+BAAA;EACA,sBAAA;ChB27CH;AgB17CG;EACE,YAAA;EACA,gCAAA;ChB47CL;AgBn9CD;EA4BI,gBAAA;EACA,kBAAA;EACA,mBAAA;EACA,oBAAA;EACA,WAAA;ChB07CH;AgB19CD;EAqCI,gBAAA;ChBw7CH;AgB79CD;EAuCM,eAAA;EACA,eAAA;EACA,gBAAA;EACA,gBAAA;ChBy7CL;AgBn+CD;;EA+CI,WAAA;ChBw7CH;AgBv+CD;EbgNE,oCAAA;EAEA,4BAAA;Ea7JE,mBAAA;EACA,WAAA;EACA,YAAA;EACA,WAAA;EACA,gBAAA;EACA,2BAAA;ChBw7CH;AgBp7CC;EACE,sBAAA;EACA,eAAA;ChBs7CH;AgBx7CC;EAKI,gBAAA;ChBs7CL;AgBj7CD;EAEE;IACE,mBAAA;GhBk7CD;EgBn7CD;IAGI,cAAA;GhBm7CH;EgBt7CD;IAMI,gBAAA;GhBm7CH;CACF;AACD;;;GAGG;AiBzgDH;EACE,mBAAA;EXmEA,mBAAA;EWjEA,oBAAA;EACA,8BAAA;EACA,oBAAA;EACA,YAAA;EACA,yCAAA;CjB2gDD;AiBxgDC;EACE,0BAAA;CjB0gDH;AiBxgDC;EACE,0BAAA;CjB0gDH;AiBxgDC;EACE,0BAAA;CjB0gDH;AiBxgDC;EACE,0BAAA;CjB0gDH;AiBxgDC;EACE,0BAAA;CjB0gDH;AiBxgDC;EACE,0BAAA;CjB0gDH;AiBtgDC;;EAGI,cAAA;CjBugDL;AiBxiDD;EAuCM,iCAAA;EACA,UAAA;CjBogDL;AiBngDK;EACE,oBAAA;CjBqgDP;AiB//CC;EAEI,kBAAA;EACA,eAAA;CjBggDL;AiBnjDD;EAwDI,gCAAA;CjB8/CH;AiBtjDD;EA2DI,+BAAA;CjB8/CH;AiBv/CC;EACE,cAAA;CjBy/CH;AiB1/CC;EAIM,wBAAA;CjBy/CP;AiBr/CO;;EACE,+BAAA;CjBw/CT;AiBl/CG;EXxCF,0BAAA;CN6hDD;AiBr/CG;EXtCA,YAAA;EACA,oBAAA;EACA,0BAAA;CN8hDH;AiB1/CG;;EXjCE,YAAA;CN+hDL;AiB3/CG;EX3CF,0BAAA;CNyiDD;AiB9/CG;EXzCA,YAAA;EACA,oBAAA;EACA,0BAAA;CN0iDH;AiBngDG;;EXpCE,YAAA;CN2iDL;AiBpgDG;EX9CF,0BAAA;CNqjDD;AiBvgDG;EX5CA,YAAA;EACA,oBAAA;EACA,0BAAA;CNsjDH;AiB5gDG;;EXvCE,YAAA;CNujDL;AiB7gDG;EXjDF,0BAAA;CNikDD;AiBhhDG;EX/CA,YAAA;EACA,oBAAA;EACA,0BAAA;CNkkDH;AiBrhDG;;EX1CE,YAAA;CNmkDL;AiBthDG;EXpDF,0BAAA;CN6kDD;AiBzhDG;EXlDA,YAAA;EACA,oBAAA;EACA,0BAAA;CN8kDH;AiB9hDG;;EX7CE,YAAA;CN+kDL;AiB/hDG;EXvDF,0BAAA;CNylDD;AiBliDG;EXrDA,YAAA;EACA,oBAAA;EACA,0BAAA;CN0lDH;AiBviDG;;EXhDE,YAAA;CN2lDL;AiBzkDC;EAmCI,UAAA;EACA,iBAAA;CjByiDL;AiBriDG;EAEI,YAAA;CjBsiDP;AiBlpDD;EAqHM,mBAAA;CjBgiDL;AiBrpDD;EA2HI,mBAAA;EACA,YAAA;EACA,iBAAA;EACA,gBAAA;EACA,qBAAA;CjB6hDH;AiBzhDD;;;;EAKI,mBAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;EACA,aAAA;CjB0hDH;AiBniDD;;EAaI,YAAA;EACA,qCAAA;EX7EF,mBAAA;CNwmDD;AiBziDD;;EAiBM,mBAAA;EACA,SAAA;EACA,UAAA;EACA,mBAAA;EACA,kBAAA;EACA,YAAA;EACA,gBAAA;CjB4hDL;AiBnjDD;;EA4BI,+BAAA;CjB2hDH;AEjrDC;;;;;;EAEE,aAAA;EACA,eAAA;CFurDH;AErrDC;;;EACE,YAAA;CFyrDH;AiB7hDD;EACE,YAAA;EACA,eAAA;EACA,cAAA;EACA,mBAAA;CjB+hDD;AiB5hDC;EACE,iCAAA;CjB8hDH;AiB7hDG;EACE,oBAAA;CjB+hDL;AiBziDD;;;;EAmBI,sBAAA;EACA,gBAAA;EACA,UAAA;EACA,eAAA;CjB4hDH;AiBljDD;;;EA2BI,kBAAA;CjB4hDH;AiBvjDD;EA8BI,mBAAA;EACA,YAAA;EACA,SAAA;CjB4hDH;AiB5jDD;EAkCM,mBAAA;CjB6hDL;AiB1hDG;EAEI,SAAA;EACA,WAAA;CjB2hDP;AiBnkDD;EA6CM,sBAAA;CjByhDL;AiBnhDD;EACE,aAAA;EACA,gBAAA;EACA,wBAAA;EACA,eAAA;CjBqhDD;AiBphDC;;EAEE,eAAA;CjBshDH;AiBphDC;EACE,iBAAA;CjBshDH;AiBjhDD;EX7JE,0BAAA;EAEA,2BAAA;EAEA,gCAAA;EAEA,+BAAA;EWyJA,cAAA;CjBshDD;AiBrhDC;ENjPA,6BAAA;EACA,4BAAA;CXywDD;AiB5hDD;EAQI,iBAAA;CjBuhDH;AiB/hDD;EAaI,gBAAA;CjBqhDH;AiBliDD;EAiBI,cAAA;CjBohDH;AiBlhDC;EACE,aAAA;CjBohDH;AiBxiDD;EX7JE,0BAAA;EAEA,2BAAA;EAEA,8BAAA;EAEA,+BAAA;CNqsDD;AiB9iDD;EX7JE,0BAAA;EAEA,2BAAA;EAEA,gCAAA;EAEA,6BAAA;CN2sDD;AiBphDD;EX7LE,0BAAA;EAEA,2BAAA;EAEA,gCAAA;EAEA,+BAAA;EWyLA,8BAAA;EACA,cAAA;EACA,uBAAA;CjByhDD;AiBthDD;EAEE,eAAA;CjBuhDD;AiBrhDG;EA6MJ;IA5MM,YAAA;IACA,mBAAA;GjBwhDH;CACF;AiBnhDD;EACE,oBAAA;CjBqhDD;AiBthDD;EAII,eAAA;EACA,8BAAA;CjBqhDH;AEjzDC;;EAEE,aAAA;EACA,eAAA;CFmzDH;AEjzDC;EACE,YAAA;CFmzDH;AiB5hDG;EACE,iBAAA;CjB8hDL;AiB5hDG;EACE,eAAA;CjB8hDL;AiBxiDD;EAcM,YAAA;CjB6hDL;AiB3iDD;EAkBI,kBAAA;EACA,YAAA;CjB4hDH;AiB/iDD;EAsBI,YAAA;EACA,eAAA;EACA,iBAAA;CjB4hDH;AiBpjDD;EA2BI,iBAAA;EACA,gBAAA;CjB4hDH;AACD,uBAAuB;AiBphDvB;EACE,UAAA;EACA,WAAA;EACA,iBAAA;EACA,eAAA;CjBshDD;AiB1hDD;EXjQE,mBAAA;EWyQE,cAAA;EACA,oBAAA;EACA,mBAAA;EACA,+BAAA;EACA,YAAA;CjBshDH;AiBrhDG;EACE,iBAAA;CjBuhDL;AiBriDD;EAkBM,qBAAA;CjBshDL;AiBxiDD;EAsBM,sBAAA;EACA,iBAAA;EACA,iBAAA;CjBqhDL;AiB7iDD;EA6BM,kBAAA;EACA,eAAA;CjBmhDL;AiBjjDD;EAmCM,cAAA;EACA,aAAA;EACA,eAAA;CjBihDL;AiBtjDD;;;EAwCQ,kBAAA;EACA,gBAAA;CjBmhDP;AiB/gDG;EACE,sBAAA;CjBihDL;AiB9gDG;EACE,YAAA;CjBghDL;AiBjhDG;EAGI,8BAAA;EACA,iBAAA;CjBihDP;AiBrhDG;EAQI,+BAAA;CjBghDP;AiBzkDD;EAgEI,2BAAA;CjB4gDH;AiB5kDD;EAmEI,2BAAA;CjB4gDH;AiB/kDD;EAsEI,2BAAA;CjB4gDH;AiBllDD;EAyEI,2BAAA;CjB4gDH;AiBrlDD;EA4EI,2BAAA;CjB4gDH;AiBxlDD;EAgFI,sBAAA;EACA,aAAA;EACA,cAAA;CjB2gDH;AACD,uGAAuG;AiBpgDvG;EACE,2BAAA;CjBsgDD;AiBvgDD;EAKI,oBAAA;CjBqgDH;AEh6DC;;EAEE,aAAA;EACA,eAAA;CFk6DH;AEh6DC;EACE,YAAA;CFk6DH;AiBlhDD;EAQM,YAAA;EACA,aAAA;EACA,8BAAA;EXrWJ,mBAAA;CNm3DD;AiBxhDD;EAeM,0BAAA;CjB4gDL;AiB3hDD;EAkBM,0BAAA;CjB4gDL;AiB9hDD;EAuBM,kBAAA;EACA,kBAAA;CjB0gDL;AiBliDD;EA0BQ,eAAA;EACA,iBAAA;CjB2gDP;AiBtiDD;EX3VE,mBAAA;EW6XI,oBAAA;EACA,kBAAA;EACA,mBAAA;EACA,cAAA;CjBwgDL;AiB7iDD;EAuCQ,kBAAA;EACA,iBAAA;EACA,gBAAA;CjBygDP;AiBljDD;;EA4CQ,iBAAA;EACA,gBAAA;EACA,mBAAA;EACA,UAAA;CjB0gDP;AE/8DC;;EAEE,aAAA;EACA,eAAA;CFi9DH;AE/8DC;EACE,YAAA;CFi9DH;AiBtgDD;EACE,iBAAA;CjBwgDD;AiBngDD;EAEI,YAAA;CjBogDH;AACD;;;GAGG;AkB1+DH;EACE,eAAA;EACA,iBAAA;EACA,iBAAA;EACA,YAAA;EACA,yCAAA;EZ+DA,mBAAA;EY7DA,oBAAA;ClB4+DD;AkBn/DD;EASI,gBAAA;ClB6+DH;AkBt/DD;EAYI,+BAAA;EACA,4BAAA;EACA,YAAA;ClB6+DH;AkB5+DG;;EZqDF,iBAAA;CN27DD;AkB//DD;EAoBM,iBAAA;ClB8+DL;AkBz+DD;EZuDE,4BAAA;EAEA,2BAAA;EAEA,8BAAA;EAEA,+BAAA;EY3DA,eAAA;EACA,YAAA;EACA,aAAA;EACA,YAAA;EACA,mBAAA;EACA,gBAAA;EACA,kBAAA;EACA,+BAAA;ClB8+DD;AkBv/DD;EAWI,gBAAA;ClB++DH;AkB3+DD;EACE,kBAAA;EACA,kBAAA;ClB6+DD;AkB1+DD;EACE,eAAA;EACA,kBAAA;EACA,gBAAA;ClB4+DD;AkBz+DD;;EAEE,eAAA;EACA,gBAAA;EACA,oBAAA;EACA,iBAAA;EACA,wBAAA;ClB2+DD;AkBx+DD;EACE,0BAAA;ClB0+DD;AkBv+DD;EACE,eAAA;ClBy+DD;AkBt+DD;EACE,UAAA;ClBw+DD;AACD;;;GAGG;AmBhjEH;EACE,mBAAA;EACA,mBAAA;EACA,WAAA;EACA,iBAAA;CnBkjED;AmB/iEC;EACE,YAAA;EACA,mBAAA;EACA,OAAA;EACA,UAAA;EACA,WAAA;EACA,iBAAA;EACA,WAAA;EACA,UAAA;EboDF,mBAAA;CN8/DD;AmBjkED;EAoBI,mBAAA;EACA,mBAAA;EACA,oBAAA;CnBgjEH;AE9jEC;;EAEE,aAAA;EACA,eAAA;CFgkEH;AE9jEC;EACE,YAAA;CFgkEH;AmB9kED;EhBsEE,yCAAA;EGHA,mBAAA;EatCI,cAAA;EACA,iBAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,WAAA;EACA,mBAAA;CnBujEL;AmB1lED;EAuCQ,YAAA;EACA,aAAA;EACA,cAAA;EACA,gBAAA;CnBsjEP;AmBhmED;EA6CQ,UAAA;EACA,YAAA;EACA,iCAAA;EACA,cAAA;EACA,gBAAA;EACA,iBAAA;CnBsjEP;AmBxmED;EAoDU,iBAAA;CnBujET;AmB3mED;;EAyDQ,cAAA;CnBsjEP;AmB/mED;;;EAkEM,YAAA;EACA,aAAA;EACA,gBAAA;EACA,kBAAA;EACA,mBAAA;EACA,YAAA;EACA,oBAAA;EACA,mBAAA;EACA,mBAAA;EACA,WAAA;EACA,OAAA;CnBkjEL;AmB9nED;EAmFM,iBAAA;EACA,aAAA;EACA,sBAAA;EACA,uBAAA;EbnBJ,mBAAA;CNkkED;AmBxiED;EAGM,oBAAA;EACA,uBAAA;EhB3BJ,iBAAA;CHqkED;AmB9iED;EAOQ,0BAAA;CnB0iEP;AACD;;;GAGG;AoBlpEH;EdmEE,mBAAA;EHGA,iBAAA;EiBnEA,8BAAA;CpBqpED;AoBnpEC;EACE,0BAAA;CpBqpEH;AoBjpEC;EdyDA,iBAAA;EcrDE,iBAAA;EACA,kBAAA;CpBmpEH;AoB/oEC;EAGE,iDAAA;CpBipEH;AoB9oEC;EACE,cAAA;CpBgpEH;AoB5oEC;EACE,mBAAA;EACA,iBAAA;CpB8oEH;AoBhpEC;EAII,mBAAA;EACA,OAAA;EACA,SAAA;EACA,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,kBAAA;EC1CJ,WAAA;EAGA,yBAAA;EDyCI,cAAA;EACA,kBAAA;EACA,gBAAA;EACA,eAAA;CpBgpEL;AoB1oED;EACE,0BAAA;EACA,YAAA;EACA,mBAAA;CpB4oED;AoB3oEC;;;EAGE,0BAAA;CpB6oEH;AoBzoED;EACE,0BAAA;EACA,sBAAA;CpB2oED;AoB1oEC;;;EACE,0BAAA;CpB8oEH;AoB1oED;EACE,0BAAA;EACA,sBAAA;CpB4oED;AoB3oEC;;;EACE,0BAAA;CpB+oEH;AoB3oED;EACE,0BAAA;EACA,sBAAA;CpB6oED;AoB5oEC;;;EACE,0BAAA;CpBgpEH;AoB5oED;EACE,0BAAA;EACA,sBAAA;CpB8oED;AoB7oEC;;;EACE,0BAAA;CpBipEH;AoB7oED;EACE,0BAAA;EACA,sBAAA;CpB+oED;AoB9oEC;;;EACE,0BAAA;CpBkpEH;AoB9oED;EACE,uBAAA;EACA,wBAAA;EACA,YAAA;CpBgpED;AoB/oEC;;;EAGE,gCAAA;EACA,uCAAA;CpBipEH;AoB7oED;EjB5CE,iBAAA;CH6rED;AoB5oED;EjBjDE,+CAAA;CHisED;AoB3oED;EdzDE,mBAAA;Ec2DA,mBAAA;EACA,kBAAA;EACA,sBAAA;EACA,gBAAA;EACA,aAAA;EACA,mBAAA;EACA,YAAA;EACA,uBAAA;EACA,0BAAA;EACA,gBAAA;CpB6oED;AoBxpED;;;EAcI,gBAAA;EACA,eAAA;CpB+oEH;AoB5oEC;EACE,oBAAA;EACA,YAAA;EACA,mBAAA;CpB8oEH;AoB3oEC;;EAGE,iDAAA;CpB8oEH;AoBzqED;EAgCI,mBAAA;EACA,UAAA;EACA,aAAA;EACA,gBAAA;EACA,iBAAA;CpB4oEH;AACD;;;GAGG;AsB/yEH;EhBkEE,mBAAA;EgBhEA,mBAAA;EACA,6BAAA;EACA,4BAAA;CtBizED;AsBrzED;EAMI,YAAA;EACA,2BAAA;CtBkzEH;AsBjzEG;EACE,YAAA;CtBmzEL;AsB5zED;EAaI,cAAA;EACA,iBAAA;CtBkzEH;AsBh0ED;EAiBI,iBAAA;CtBkzEH;AsBn0ED;;EAqBI,uBAAA;CtBkzEH;AsB9yEC;EAEE,sBAAA;CtB+yEH;AsB7yEC;EAEE,sBAAA;CtB8yEH;AsB5yEC;EAEE,sBAAA;CtB6yEH;AsB3yEC;EAEE,sBAAA;CtB4yEH;AACD;;;GAGG;AuBx1EH;EjBmEE,mBAAA;CNwxED;AuB31ED;EAGI,iBAAA;CvB21EH;AuB91ED;EAMI,mBAAA;CvB21EH;AuBj2ED;EASI,YAAA;EFXF,aAAA;EAGA,0BAAA;CrBq2ED;AuB31EG;EFbF,aAAA;EAGA,0BAAA;CrBy2ED;AuB12ED;EAgBI,YAAA;EACA,2BAAA;CvB61EH;AuBx1ED;EAEE,sBAAA;CvBy1ED;AuBt1ED;;EAGE,sBAAA;CvBu1ED;AuBp1ED;EAEE,sBAAA;CvBq1ED;AuBl1ED;EAEE,sBAAA;CvBm1ED;AACD;;;GAGG;AwB/3EH;;;EAII,YAAA;EACA,oBAAA;CxBg4EH;AACD,eAAe;AwB53Ef;ElByDE,iBAAA;EkBtDE,kCAAA;EACA,YAAA;CxB63EH;AwBj4ED;;;EAQM,kBAAA;CxB83EL;AwBt4ED;;;EAcI,0BAAA;CxB63EH;AwB34ED;EAiBI,iBAAA;CxB63EH;AACD,iBAAiB;AwBz3EjB;ElBmCE,iBAAA;EkBhCE,cAAA;EACA,mCAAA;EACA,YAAA;CxB03EH;AwB/3ED;;EASI,wBAAA;EACA,YAAA;EACA,cAAA;EACA,2BAAA;CxB03EH;AwBt4ED;EAgBI,8BAAA;EACA,YAAA;EACA,oBAAA;EACA,kBAAA;EACA,0BAAA;CxBy3EH;AACD,cAAc;AwBr3Ed;EACE,oBAAA;EACA,iBAAA;EACA,yCAAA;EACA,mBAAA;CxBu3ED;AwB33ED;EAMI,UAAA;EACA,6BAAA;EblEF,6BAAA;EACA,4BAAA;CX27ED;AwBj4ED;EAUM,kCAAA;EACA,oBAAA;EAuBA,kBAAA;CxBo2EL;AwBt4ED;EAaQ,YAAA;ElBHN,iBAAA;CNg4ED;AwB33EO;EACE,YAAA;CxB63ET;AwB33EO;;EAEE,wBAAA;EACA,UAAA;CxB63ET;AwB33EO;EACE,YAAA;CxB63ET;AwB13EK;;;EAII,0BAAA;CxB23ET;AwB15ED;EAsCM,0BAAA;CxBu3EL;AwBt3EK;;EAEE,uBAAA;EACA,YAAA;CxBw3EP;AwBl6ED;EA6CQ,8BAAA;EACA,2BAAA;EACA,4BAAA;CxBw3EP;AwBv6ED;EAqDM,eAAA;CxBq3EL;AwBp3EK;EAEI,+BAAA;CxBq3ET;AwB/2EG;EACE,uBAAA;CxBi3EL;AwBl3EG;EAGI,aAAA;CxBk3EP;AwBr3EG;EAMI,gBAAA;CxBk3EP;AwBx3EG;EAQM,uBAAA;CxBm3ET;AwBj3EO;EAEI,2BAAA;EACA,gCAAA;CxBk3EX;AwB77ED;EAkFM,kBAAA;EACA,gBAAA;EACA,gBAAA;EACA,YAAA;CxB82EL;AwBn8ED;;;EAyFQ,kBAAA;CxB+2EP;AwBx8ED;EA+FI,iBAAA;EACA,cAAA;EbjJF,gCAAA;EACA,+BAAA;CX8/ED;AwBz2EG;;EAEE,wBAAA;EACA,YAAA;CxB22EL;AwBv2EC;EAGM,0BAAA;CxBu2EP;AwBn2EC;EAGM,0BAAA;CxBm2EP;AwB/1EC;EAGM,0BAAA;CxB+1EP;AwB31EC;EAGM,0BAAA;CxB21EP;AwBv1EC;EAGM,0BAAA;CxBu1EP;AwBn1EC;EAGM,0BAAA;CxBm1EP;AACD,gBAAgB;AwB70EhB;EAEI,oBAAA;EACA,YAAA;CxB80EH;AwB50EC;ElBpJA,4BAAA;CNm+ED;AACD;;;GAGG;AyB3iFH;EACE,iBAAA;EACA,UAAA;EACA,WAAA;CzB6iFD;AyBhjFD;EnBoEE,mBAAA;EHGA,yCAAA;EsB/DE,gBAAA;EACA,iBAAA;CzB8iFH;AE9iFC;;EAEE,aAAA;EACA,eAAA;CFgjFH;AE9iFC;EACE,YAAA;CFgjFH;AyB/jFD;EAYI,YAAA;CzBsjFH;AyBlkFD;EAcM,YAAA;EACA,aAAA;CzBujFL;AyBtkFD;EAmBI,kBAAA;CzBsjFH;AyBzkFD;EAsBI,iBAAA;CzBsjFH;AyB5kFD;EAyBI,eAAA;EACA,YAAA;EACA,iBAAA;EACA,oBAAA;EACA,wBAAA;CzBsjFH;AyBljFD;EtBsCE,iBAAA;EGHA,iBAAA;EmBhCA,iCAAA;CzBqjFD;AyBpjFC;EACE,uBAAA;CzBsjFH;AACD;;;GAGG;A0B/lFH;;;;;;EAQQ,8BAAA;C1B+lFP;A0BvmFD;EAcI,iCAAA;C1B4lFH;A0B1mFD;EAkBI,gBAAA;C1B2lFH;A0BtlFD;EACE,0BAAA;C1BwlFD;A0BzlFD;;;;;;EAQQ,0BAAA;C1BylFP;A0BjmFD;;EAeM,yBAAA;C1BslFL;A0BhlFC;;;EAGE,UAAA;C1BklFH;AACD,4BAA4B;A0B7kF1B;;;EACE,mBAAA;C1BilFH;A0B7kFD;EAEI,iBAAA;C1B8kFH;A0BhlFD;EAKI,kBAAA;C1B8kFH;AACD;;;GAGG;A2BlpFH;EACE,0BAAA;EACA,YAAA;C3BopFD;AACD;;;GAGG;A4B1pFH;EjBSE,8BAAA;EACA,6BAAA;EiBPE,mBAAA;EACA,mBAAA;EACA,WAAA;C5B4pFH;A4B1pFC;EzBoJA,mCAAA;EAGA,2BAAA;CHygFD;A4BzpFD;EzB6IE,mCAAA;EAGA,2BAAA;EyB9IA,cAAA;EACA,cAAA;EACA,eAAA;C5B8pFD;A4B3pFD;;EAEE,eAAA;C5B6pFD;A4B1pFD;EAEE,oBAAA;C5B2pFD;AE9qFC;;EAEE,aAAA;EACA,eAAA;CFgrFH;AE9qFC;EACE,YAAA;CFgrFH;A4BhqFD;;EzB4ME,uDAAA;EAGA,+CAAA;EAAA,uCAAA;EAAA,2EAAA;CHw9ED;A4BlqFD;EtBgCE,mBAAA;EsB9BA,mBAAA;EACA,kBAAA;EACA,oBAAA;EACA,0BAAA;EACA,qBAAA;EACA,YAAA;C5BoqFD;A4BjqFC;;EAEE,mBAAA;EACA,YAAA;EACA,UAAA;EACA,0BAAA;EACA,4BAAA;EACA,aAAA;EACA,UAAA;EACA,SAAA;EACA,qBAAA;C5BmqFH;A4BhqFC;EACE,kBAAA;EACA,iBAAA;C5BkqFH;A4BhqFC;EACE,kBAAA;EACA,iBAAA;C5BkqFH;A4BhqFC;EACE,mBAAA;EACA,eAAA;C5BkqFH;A4BjqFG;;EAEE,YAAA;EACA,WAAA;EACA,gCAAA;EACA,2BAAA;C5BmqFL;A4B9pFD;EtBZE,mBAAA;EsBcA,YAAA;EACA,YAAA;EACA,aAAA;C5BgqFD;A4B/pFC;EACE,aAAA;C5BiqFH;A4B7pFD;EACE,eAAA;EACA,mBAAA;EACA,gBAAA;C5B+pFD;A4B5pFD;EACE,iBAAA;C5B8pFD;A4B3pFD;EACE,YAAA;C5B6pFD;A4BzpFD;EzBkDE,mCAAA;EAGA,2BAAA;CH0mFD;A4BzpFD;EzB4CE,sCAAA;EAGA,8BAAA;EyB7CA,mBAAA;EACA,OAAA;EACA,UAAA;EACA,cAAA;EACA,YAAA;EACA,oBAAA;EACA,YAAA;EACA,eAAA;C5B8pFD;A4B1pFD;EAII,4CAAA;EACA,cAAA;EACA,UAAA;C5BypFH;AElxFC;;EAEE,aAAA;EACA,eAAA;CFoxFH;AElxFC;EACE,YAAA;CFoxFH;A4BhqFG;EACE,oBAAA;C5BkqFL;A4B7pFD;EtBrEE,mBAAA;EsBuEA,YAAA;EACA,YAAA;C5B+pFD;A4B5pFD;EACE,kBAAA;EACA,YAAA;C5B8pFD;A4B3pFD;;EAEE,eAAA;C5B6pFD;A4B1pFD;EACE,iBAAA;C5B4pFD;A4BzpFD;EACE,gBAAA;C5B2pFD;A4BxpFD;EACE,YAAA;EACA,oBAAA;C5B0pFD;A4BvpFD;EACE,YAAA;C5BypFD;A4BrpFD;EtBnHI,oBAAA;EACA,sBAAA;EACA,YAAA;CN2wFH;AM1wFG;;EAEE,2BAAA;CN4wFL;A4B1pFD;EtBvHI,oBAAA;EACA,sBAAA;EACA,YAAA;CNoxFH;AMnxFG;;EAEE,2BAAA;CNqxFL;A4B/pFD;EtB3HI,oBAAA;EACA,sBAAA;EACA,YAAA;CN6xFH;AM5xFG;;EAEE,2BAAA;CN8xFL;A4BpqFD;EtB/HI,oBAAA;EACA,sBAAA;EACA,YAAA;CNsyFH;AMryFG;;EAEE,2BAAA;CNuyFL;A4BzqFD;EtBnII,oBAAA;EACA,sBAAA;EACA,YAAA;CN+yFH;AM9yFG;;EAEE,2BAAA;CNgzFL;AACD;;;GAGG;A6Bj3FH;EAGI,WAAA;EACA,YAAA;EACA,cAAA;EACA,mBAAA;C7Bi3FH;A6Bv3FD;EvBoEE,mBAAA;EuB3DI,gBAAA;EACA,aAAA;C7Bk3FL;A6B/2FK;;EAEE,YAAA;C7Bi3FP;A6B32FD;;EAEE,eAAA;C7B62FD;A6B12FD;EACE,iBAAA;EACA,YAAA;EACA,iBAAA;EACA,oBAAA;EACA,wBAAA;C7B42FD;A6Bz2FD;EACE,YAAA;EACA,gBAAA;C7B22FD;AACD;;;GAGG;A8Bl5FD;;EAEE,uBAAA;C9Bo5FH;A8Bv5FD;EAMI,gBAAA;EACA,mBAAA;EACA,SAAA;EACA,WAAA;EACA,sBAAA;EACA,kBAAA;C9Bo5FH;AACD;;;GAGG;A+Bn6FH;EACE,+BAAA;C/Bq6FD;A+Bl6FD;EzBgEE,iBAAA;EHGA,2CAAA;E4BhEA,UAAA;C/Bq6FD;A+Bp6FC;EAoEF;I5BLE,2CAAA;GH02FC;CACF;A+Br6FD;EACE,6BAAA;C/Bu6FD;A+Bp6FD;EACE,0BAAA;C/Bs6FD;A+Bl6FD;;EAOI,sBAAA;C/B+5FH;A+B35FD;;EAOI,sBAAA;C/Bw5FH;A+Bp5FD;;EAOI,sBAAA;C/Bi5FH;A+B74FD;;EAOI,sBAAA;C/B04FH;A+Bt4FD;;EAOI,sBAAA;C/Bm4FH;AACD;;;GAGG;AgC/8FH;EACE,aAAA;EACA,mBAAA;ChCi9FD;AgC78FD;EAGI,cAAA;EACA,cAAA;ErBZF,6BAAA;EACA,4BAAA;CX09FD;AgCn9FD;EASI,cAAA;EACA,mBAAA;EACA,gBAAA;EACA,iBAAA;EACA,0CAAA;ChC68FH;AgC19FD;EAiBI,cAAA;ChC48FH;AgC79FD;EAqBI,mBAAA;EACA,UAAA;EACA,UAAA;EACA,mBAAA;ChC28FH;AgCn+FD;EA0BM,YAAA;EACA,aAAA;EACA,uBAAA;ChC48FL;AgCx+FD;EAgCI,kBAAA;ChC28FH;AgCt8FD;EAGI,cAAA;ErBhDF,6BAAA;EACA,4BAAA;CXu/FD;AgC38FD;EAQI,gBAAA;EACA,mBAAA;EACA,gBAAA;EACA,iBAAA;ChCs8FH;AgCj9FD;EAeI,cAAA;ChCq8FH;AgCp9FD;;EAmBI,kBAAA;ChCq8FH;AgCx9FD;EAwBM,YAAA;EACA,aAAA;EACA,YAAA;ChCm8FL;AACD;;;GAGG;AiC7gGH;EAEI,UAAA;CjC8gGH;AiC1gGD;EACE,aAAA;CjC4gGD;AiC3gGC;EACE,iCAAA;CjC6gGH;AiCzgGD;EACE,iCAAA;EACA,cAAA;CjC2gGD;AiC7gGD;EAII,gBAAA;EACA,UAAA;CjC4gGH;AiCjhGD;EAQI,UAAA;EACA,mBAAA;CjC4gGH;AiCxgGD;EACE,YAAA;EACA,gBAAA;CjC0gGD;AiCvgGD;EACE,cAAA;CjCygGD;AiCtgGD;EAGI,YAAA;EACA,aAAA;EACA,uBAAA;EACA,oBAAA;EACA,mBAAA;CjCsgGH;AiClgGD;EACE,kBAAA;EACA,YAAA;CjCogGD;AiCjgGD;;;EAGE,eAAA;CjCmgGD;AiChgGD;EACE,cAAA;EACA,oBAAA;CjCkgGD;AiC//FD;EACE,YAAA;EACA,gBAAA;CjCigGD;AiC9/FD;EACE,mBAAA;EACA,gBAAA;EACA,YAAA;EACA,mBAAA;CjCggGD;AiC//FC;EACE,WAAA;CjCigGH;AiClgGC;EAGI,gBAAA;EACA,aAAA;CjCkgGL;AACD;;;GAGG;AACH,sCAAsC;AkCllGtC;EACE,oBAAA;ClColGD;AkCjlGD;EACE,gBAAA;EACA,mBAAA;EACA,oBAAA;EACA,iBAAA;ClCmlGD;AkCvlGD;EAMI,YAAA;ClColGH;AkChlGD;EACE,iBAAA;EACA,eAAA;EACA,gBAAA;ClCklGD;AACD,0BAA0B;AkC/kG1B;EACE,mBAAA;EACA,iBAAA;ClCilGD;AACD,iDAAiD;AkC9kGjD;E5BwCE,mBAAA;E4BtCA,WAAA;EACA,iBAAA;EACA,mBAAA;EACA,4BAAA;EACA,aAAA;ClCglGD;AACD,gBAAgB;AkC7kGhB;E5B8BE,mBAAA;E4B5BA,mBAAA;EACA,YAAA;EACA,WAAA;EACA,iBAAA;EACA,aAAA;EACA,YAAA;ClC+kGD;AkCtlGD;E5B8BE,mBAAA;E4BpBE,YAAA;EACA,aAAA;ClCglGH;AACD,sDAAsD;AkC5kGtD;EACE,kBAAA;ClC8kGD;AkC/kGD;EAGI,UAAA;ClC+kGH;AkCllGD;EAMI,uBAAA;EACA,UAAA;EACA,gBAAA;ClC+kGH;AkC3kGD;EACE,iBAAA;ClC6kGD;AACD;;;GAGG;AmCnpGH;;EAEE,gBAAA;EACA,mBAAA;EACA,oBAAA;EACA,iBAAA;CnCqpGD;AmC1pGD;;EAOI,YAAA;CnCupGH;AmCnpGD;;EAEE,oBAAA;CnCqpGD;AmClpGD;;EAEE,aAAA;EACA,gBAAA;CnCopGD;AmCnpGC;EA2BF;;IA1BI,WAAA;IACA,iBAAA;GnCupGD;CACF;AmCppGD;;EAEE,iBAAA;EACA,cAAA;EACA,cAAA;EACA,YAAA;CnCspGD;AmC3pGD;;EAOI,YAAA;CnCwpGH;AmCppGD;;EAEE,UAAA;EACA,mBAAA;EACA,0BAAA;CnCspGD;AmCnpGD;EACE,eAAA;CnCqpGD;AACD;;;GAGG;AoCvsGH;EACE,aAAA;EACA,yBAAA;CpCysGD;AoCxsGC;EA6BF;IA5BI,YAAA;GpC2sGD;CACF;AoChtGD;EAQI,YAAA;EACA,iBAAA;EACA,iBAAA;CpC2sGH;AoC1sGG;EAqBJ;IApBM,YAAA;IACA,mBAAA;GpC6sGH;CACF;AoC3tGD;EAkBI,mBAAA;EAWA,eAAA;CpCksGH;AoC5sGG;EAaJ;IAZM,eAAA;GpC+sGH;CACF;AoCpuGD;EAuBM,iBAAA;EACA,gBAAA;CpCgtGL;AoC/sGK;EAON;IANQ,mBAAA;GpCktGL;CACF;AACD;;;GAGG;AqChvGH;EACE,mBAAA;EACA,iBAAA;EACA,0BAAA;EACA,cAAA;EACA,kBAAA;CrCkvGD;AqC/uGD;EACE,cAAA;CrCivGD;AACD;;;GAGG;AsC9vGH;EACE,eAAA;EACA,aAAA;EACA,aAAA;EACA,0BAAA;CtCgwGD;AsC7vGD;EACE,gBAAA;EACA,gBAAA;CtC+vGD;AsC5vGD;EACE,iCAAA;EACA,oBAAA;EACA,qBAAA;EACA,YAAA;CtC8vGD;AsC7vGC;EACE,iBAAA;EACA,iBAAA;EACA,kBAAA;CtC+vGH;AsCvwGD;EAWI,oBAAA;CtC+vGH;AACD;;;;;;;GAOG;AuC/wGH;EACE,mBAAA;EACA,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,iBAAA;EACA,wBAAA;CvCixGD;AuCvxGD;EAQI,mBAAA;EACA,QAAA;EACA,OAAA;EACA,UAAA;EACA,YAAA;EACA,kBAAA;EACA,iBAAA;EACA,mBAAA;EACA,2CAAA;CvCkxGH;AuChxGC;EACE,mBAAA;CvCkxGH;AuCnxGC;EAGI,kBAAA;EACA,YAAA;EACA,iBAAA;CvCmxGL;AuChxGC;EACE,mBAAA;CvCkxGH;AuCnxGC;EAGI,kBAAA;EACA,YAAA;EACA,iBAAA;CvCmxGL;AuChxGC;EACE,mBAAA;CvCkxGH;AuCnxGC;EAGI,kBAAA;EACA,YAAA;EACA,iBAAA;CvCmxGL;AuC9wGD;EA3CE,mBAAA;EACA,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,iBAAA;EACA,wBAAA;EAwCA,aAAA;EACA,YAAA;EACA,WAAA;CvCqxGD;AuCzxGD;EApCI,mBAAA;EACA,QAAA;EACA,OAAA;EACA,UAAA;EACA,YAAA;EACA,kBAAA;EACA,iBAAA;EACA,mBAAA;EACA,2CAAA;CvCg0GH;AuC9zGC;EACE,mBAAA;CvCg0GH;AuCj0GC;EAGI,kBAAA;EACA,YAAA;EACA,iBAAA;CvCi0GL;AuC9zGC;EACE,mBAAA;CvCg0GH;AuCj0GC;EAGI,kBAAA;EACA,YAAA;EACA,iBAAA;CvCi0GL;AuC9zGC;EACE,mBAAA;CvCg0GH;AuCj0GC;EAGI,kBAAA;EACA,YAAA;EACA,iBAAA;CvCi0GL;AuC5zGD;EAMI,aAAA;EACA,mBAAA;EACA,YAAA;CvCyzGH;AuCvzGC;EACE,aAAA;EACA,YAAA;EACA,gBAAA;EACA,iBAAA;CvCyzGH;AuCvzGC;EACE,aAAA;EACA,YAAA;EACA,gBAAA;EACA,iBAAA;CvCyzGH;AuCvzGC;EACE,aAAA;EACA,YAAA;EACA,gBAAA;EACA,iBAAA;CvCyzGH;AuChzGD;EC7FE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg5GD;AwC94GC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg5GH;AwC94GC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg5GH;AwC94GC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg5GH;AwC94GG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCs5GL;AwCn5GC;;;EAGE,uBAAA;CxCq5GH;AwCh5GG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC85GL;AuC92GD;EC3CI,eAAA;EACA,uBAAA;CxC45GH;AuC92GD;ECjGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk9GD;AwCh9GC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk9GH;AwCh9GC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk9GH;AwCh9GC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk9GH;AwCh9GG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCw9GL;AwCr9GC;;;EAGE,uBAAA;CxCu9GH;AwCl9GG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCg+GL;AuC56GD;EC/CI,eAAA;EACA,uBAAA;CxC89GH;AuC56GD;ECrGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCohHD;AwClhHC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCohHH;AwClhHC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCohHH;AwClhHC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCohHH;AwClhHG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0hHL;AwCvhHC;;;EAGE,uBAAA;CxCyhHH;AwCphHG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCkiHL;AuC1+GD;ECnDI,eAAA;EACA,uBAAA;CxCgiHH;AuC1+GD;ECzGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCslHD;AwCplHC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCslHH;AwCplHC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCslHH;AwCplHC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCslHH;AwCplHG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4lHL;AwCzlHC;;;EAGE,uBAAA;CxC2lHH;AwCtlHG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxComHL;AuCxiHD;ECvDI,eAAA;EACA,uBAAA;CxCkmHH;AuCxiHD;EC7GE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwpHD;AwCtpHC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwpHH;AwCtpHC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwpHH;AwCtpHC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwpHH;AwCtpHG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC8pHL;AwC3pHC;;;EAGE,uBAAA;CxC6pHH;AwCxpHG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCsqHL;AuCtmHD;EC3DI,eAAA;EACA,uBAAA;CxCoqHH;AuCtmHD;ECjHE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0tHD;AwCxtHC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0tHH;AwCxtHC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0tHH;AwCxtHC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0tHH;AwCxtHG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCguHL;AwC7tHC;;;EAGE,uBAAA;CxC+tHH;AwC1tHG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCwuHL;AuCpqHD;EC/DI,eAAA;EACA,uBAAA;CxCsuHH;AuCpqHD;ECrHE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4xHD;AwC1xHC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4xHH;AwC1xHC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4xHH;AwC1xHC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4xHH;AwC1xHG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCkyHL;AwC/xHC;;;EAGE,uBAAA;CxCiyHH;AwC5xHG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC0yHL;AuCluHD;ECnEI,eAAA;EACA,uBAAA;CxCwyHH;AuCluHD;ECzHE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC81HD;AwC51HC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC81HH;AwC51HC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC81HH;AwC51HC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC81HH;AwC51HG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCo2HL;AwCj2HC;;;EAGE,uBAAA;CxCm2HH;AwC91HG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC42HL;AuChyHD;ECvEI,eAAA;EACA,uBAAA;CxC02HH;AuChyHD;EC7HE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg6HD;AwC95HC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg6HH;AwC95HC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg6HH;AwC95HC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg6HH;AwC95HG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCs6HL;AwCn6HC;;;EAGE,uBAAA;CxCq6HH;AwCh6HG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC86HL;AuC91HD;EC3EI,eAAA;EACA,uBAAA;CxC46HH;AuC91HD;ECjIE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk+HD;AwCh+HC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk+HH;AwCh+HC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk+HH;AwCh+HC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk+HH;AwCh+HG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCw+HL;AwCr+HC;;;EAGE,uBAAA;CxCu+HH;AwCl+HG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCg/HL;AuC55HD;EC/EI,eAAA;EACA,uBAAA;CxC8+HH;AuC55HD;ECrIE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCoiID;AwCliIC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCoiIH;AwCliIC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCoiIH;AwCliIC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCoiIH;AwCliIG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0iIL;AwCviIC;;;EAGE,uBAAA;CxCyiIH;AwCpiIG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCkjIL;AuC19HD;ECnFI,eAAA;EACA,uBAAA;CxCgjIH;AuC19HD;ECzIE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsmID;AwCpmIC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsmIH;AwCpmIC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsmIH;AwCpmIC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsmIH;AwCpmIG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4mIL;AwCzmIC;;;EAGE,uBAAA;CxC2mIH;AwCtmIG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxConIL;AuCxhID;ECvFI,eAAA;EACA,uBAAA;CxCknIH;AuCxhID;EC7IE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwqID;AwCtqIC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwqIH;AwCtqIC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwqIH;AwCtqIC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCwqIH;AwCtqIG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC8qIL;AwC3qIC;;;EAGE,uBAAA;CxC6qIH;AwCxqIG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCsrIL;AuCtlID;EC3FI,eAAA;EACA,uBAAA;CxCorIH;AuCtlID;ECjJE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0uID;AwCxuIC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0uIH;AwCxuIC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0uIH;AwCxuIC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0uIH;AwCxuIG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCgvIL;AwC7uIC;;;EAGE,uBAAA;CxC+uIH;AwC1uIG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCwvIL;AuCppID;EC/FI,eAAA;EACA,uBAAA;CxCsvIH;AuCppID;ECrJE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4yID;AwC1yIC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4yIH;AwC1yIC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4yIH;AwC1yIC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4yIH;AwC1yIG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCkzIL;AwC/yIC;;;EAGE,uBAAA;CxCizIH;AwC5yIG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC0zIL;AuCltID;ECnGI,eAAA;EACA,uBAAA;CxCwzIH;AuCltID;ECzJE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC82ID;AwC52IC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC82IH;AwC52IC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC82IH;AwC52IC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC82IH;AwC52IG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCo3IL;AwCj3IC;;;EAGE,uBAAA;CxCm3IH;AwC92IG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC43IL;AuChxID;ECvGI,eAAA;EACA,uBAAA;CxC03IH;AuChxID;EC7JE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg7ID;AwC96IC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg7IH;AwC96IC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg7IH;AwC96IC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCg7IH;AwC96IG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCs7IL;AwCn7IC;;;EAGE,uBAAA;CxCq7IH;AwCh7IG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxC87IL;AuC90ID;EC3GI,eAAA;EACA,uBAAA;CxC47IH;AuC90ID;ECjKE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk/ID;AwCh/IC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk/IH;AwCh/IC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk/IH;AwCh/IC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCk/IH;AwCh/IG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCw/IL;AwCr/IC;;;EAGE,uBAAA;CxCu/IH;AwCl/IG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCggJL;AuC54ID;EC/GI,eAAA;EACA,uBAAA;CxC8/IH;AuC54ID;ECrKE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCojJD;AwCljJC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCojJH;AwCljJC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCojJH;AwCljJC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCojJH;AwCljJG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC0jJL;AwCvjJC;;;EAGE,uBAAA;CxCyjJH;AwCpjJG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCkkJL;AuC18ID;ECnHI,eAAA;EACA,uBAAA;CxCgkJH;AuC18ID;ECzKE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsnJD;AwCpnJC;;EAEE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsnJH;AwCpnJC;EACE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsnJH;AwCpnJC;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxCsnJH;AwCpnJG;;;;;;;;;EAGE,YAAA;EACA,0BAAA;EACA,iCAAA;CxC4nJL;AwCznJC;;;EAGE,uBAAA;CxC2nJH;AwCtnJG;;;;;;;;;;;;;;;;;;EAME,0BAAA;EACA,iCAAA;CxCooJL;AuCxgJD;ECvHI,eAAA;EACA,uBAAA;CxCkoJH;AACD;;;GAGG;AyCnrJH;EACE,oBAAA;EACA,uBAAA;EACA,YAAA;EACA,mBAAA;EACA,0BAAA;CzCqrJD;AyCprJC;;;EAGE,0BAAA;CzCsrJH;AyCjrJD;EACE,gBAAA;EACA,mBAAA;EACA,YAAA;EACA,kBAAA;CzCmrJD;AyChrJD;EACE,oBAAA;CzCkrJD;AyC/qJD;EACE,mBAAA;CzCirJD;AyC7qJD;EACE,oBAAA;CzC+qJD;AyC5qJD;EACE,YAAA;EACA,UAAA;CzC8qJD;AyC3qJD;;EAEE,eAAA;EACA,gBAAA;CzC6qJD;AyC1qJD;;EAEE,gBAAA;CzC4qJD;AyCzqJD;EACE,cAAA;EACA,UAAA;CzC2qJD;AyCxqJD;EACE,gBAAA;EACA,iBAAA;EACA,oBAAA;CzC0qJD;AyCvqJD;EACE,iBAAA;EACA,UAAA;EACA,WAAA;CzCyqJD;AyC5qJD;EAKI,YAAA;EACA,gBAAA;EACA,kBAAA;EACA,kBAAA;CzC0qJH;AyClrJD;EtCsKE,kDAAA;EAGA,0CAAA;EAAA,kCAAA;EAAA,iEAAA;CH+gJD;AyC7qJK;EtCuFJ,iCAAA;EAGA,yBAAA;CHylJD;AyC5qJD;EtC0HE,oCAAA;EAEA,4BAAA;CHqjJD;AyC7qJD;EACE,kBAAA;EACA,kBAAA;EACA,mBAAA;EACA,yCAAA;EACA,0CAAA;EACA,mBAAA;EACA,aAAA;CzC+qJD;AyC9qJC;EACE,8CAAA;CzCgrJH;AACD;;;GAGG;A0C9wJD;;;;;;EAGE,cAAA;C1CmxJH;A0CxxJD;;EAQI,0BAAA;EACA,iBAAA;EACA,kBAAA;EACA,aAAA;C1CoxJH;A0ChxJD;EACE,sBAAA;C1CkxJD;A0C/wJD;EACE,0BAAA;EACA,iBAAA;C1CixJD;A0C9wJD;EACE,0BAAA;EACA,aAAA;C1CgxJD;A0C7wJD;EACE,kBAAA;EACA,uBAAA;EAAA,sBAAA;EAAA,kBAAA;EACA,0BAAA;C1C+wJD;A0C5wJD;EACE,gBAAA;EACA,iBAAA;EACA,aAAA;EACA,iBAAA;C1C8wJD;A0C3wJD;EACE,mBAAA;EACA,mBAAA;C1C6wJD;A0C1wJD;EACE,aAAA;EACA,WAAA;C1C4wJD;A0CzwJD;EACE,cAAA;C1C2wJD;A0CxwJD;;EAGI,0BAAA;C1CywJH;A0CxwJG;;EACE,cAAA;EACA,0BAAA;C1C2wJL;A0CtwJD;EACE,YAAA;C1CwwJD;A0CrwJD;EACE,uBAAA;C1CuwJD;A0CtwJC;;EAEE,YAAA;C1CwwJH;A0CnwJD;EAEI,0BAAA;EACA,iBAAA;C1CowJH;A0CnwJG;EACE,sBAAA;C1CqwJL;A0ClwJC;EACE,sBAAA;C1CowJH;A0ChwJD;EACE,0BAAA;EACA,sBAAA;EACA,kBAAA;EACA,YAAA;C1CkwJD;A0C/vJD;EACE,kBAAA;EACA,gCAAA;C1CiwJD;A0ChwJC;EACE,YAAA;C1CkwJH;A0C9vJD;EACE,oBAAA;C1CgwJD;AACD;;;GAGG;A2Cx3JH;EACE,cAAA;C3C03JD;A2Cv3JD;EACE,aAAA;C3Cy3JD;A2Ct3JD;EACE,oBAAA;C3Cw3JD;A2Cr3JD;EACE,iBAAA;C3Cu3JD;A2Cp3JD;EACE,kBAAA;C3Cs3JD;A2Cl3JD;EACE,gBAAA;C3Co3JD;A2Ch3JD;EACE,eAAA;EACA,eAAA;EACA,mBAAA;C3Ck3JD;A2Cj3JC;EACE,oBAAA;C3Cm3JH;A2Cx3JD;EAQI,UAAA;EACA,WAAA;EACA,iBAAA;EACA,gBAAA;C3Cm3JH;A2C93JD;EAcI,0BAAA;C3Cm3JH;A2C92JD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BE,uBAAA;C3C64JD;A2C14JD;EACE,YAAA;EACA,qCAAA;C3C44JD;A2Cz4JD;EACE,0BAAA;C3C24JD;A2Cx4JD;EACE,kCAAA;C3C04JD;A2Cv4JD;;;;;;EACE,qCAAA;C3C84JD;A2C34JD;;;;;EACE,qCAAA;C3Ci5JD;A2C94JD;;;;;EACE,qCAAA;C3Co5JD;A2Cj5JD;EACE,qCAAA;C3Cm5JD;A2Ch5JD;;;EACE,qCAAA;C3Co5JD;A2Cj5JD;;;;;EACE,qCAAA;C3Cu5JD;A2Cp5JD;EACE,qCAAA;C3Cs5JD;A2Cn5JD;EACE,qCAAA;C3Cq5JD;A2Cl5JD;EACE,qCAAA;C3Co5JD;A2Cj5JD;EACE,qCAAA;C3Cm5JD;A2Ch5JD;EACE,qCAAA;C3Ck5JD;A2C/4JD;EACE,qCAAA;C3Ci5JD;A2C94JD;EACE,qCAAA;C3Cg5JD;A2C74JD;EACE,qCAAA;C3C+4JD;A2C34JD;EACE,YAAA;EACA,qCAAA;C3C64JD;A2C14JD;EACE,qCAAA;C3C44JD;A2Cz4JD;;;EACE,qCAAA;C3C64JD;A2C14JD;;;EACE,qCAAA;C3C84JD;A2C34JD;;;EACE,qCAAA;C3C+4JD;A2C54JD;EACE,qCAAA;C3C84JD;A2C34JD;;;EACE,qCAAA;C3C+4JD;A2C54JD;;;EACE,qCAAA;C3Cg5JD;A2C74JD;EACE,qCAAA;C3C+4JD;A2C54JD;EACE,qCAAA;C3C84JD;A2C34JD;EACE,qCAAA;C3C64JD;A2C14JD;EACE,qCAAA;C3C44JD;A2Cz4JD;EACE,qCAAA;C3C24JD;A2Cx4JD;EACE,qCAAA;C3C04JD;A2Cv4JD;EACE,qCAAA;C3Cy4JD;A2Ct4JD;EACE,qCAAA;C3Cw4JD;A2Cp4JD;EtBxNE,cAAA;EAGA,0BAAA;CrB6lKD;A2Cn4JD;EACE,0BAAA;C3Cq4JD;A2Cl4JD;EACE,0BAAA;C3Co4JD;A2Cj4JD;EACE,0BAAA;C3Cm4JD;A2Ch4JD;EACE,0BAAA;C3Ck4JD;A2C/3JD;EACE,uBAAA;C3Ci4JD;A2C93JD;EACE,0BAAA;C3Cg4JD;A2C73JD;EACE,0BAAA;C3C+3JD;A2C53JD;EACE,0BAAA;C3C83JD;A2C33JD;EACE,0BAAA;C3C63JD;A2C13JD;EACE,0BAAA;C3C43JD;A2Cz3JD;EACE,0BAAA;C3C23JD;A2Cx3JD;EACE,0BAAA;C3C03JD;A2Cv3JD;EACE,0BAAA;C3Cy3JD;A2Ct3JD;EACE,0BAAA;C3Cw3JD;A2Cr3JD;EACE,0BAAA;C3Cu3JD;A2Cp3JD;EACE,0BAAA;C3Cs3JD;A2Cn3JD;EACE,eAAA;C3Cq3JD;A2Cp3JC;;EAEE,eAAA;C3Cs3JH;A2Cl3JD;EACE,YAAA;C3Co3JD;A2Cn3JC;;EAEE,YAAA;C3Cq3JH;A2Ch3JD;EACE,yBAAA;C3Ck3JD;A2C92JD;EACE,qBAAA;C3Cg3JD;A2C52JD;EACE,sBAAA;C3C82JD;A2C12JD;EACE,qBAAA;C3C42JD;A2Cx2JD;EACE,4BAAA;C3C02JD;A2Ct2JD;;;;;EACE,iBAAA;EACA,UAAA;EACA,WAAA;C3C42JD;A2Cz2JD;EAEI,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,gBAAA;EACA,iBAAA;C3C02JH;A2Cr2JD;ErCnRE,4BAAA;CN2nKD;A2Cn2JC;;;EACE,iBAAA;C3Cu2JH;A2Cn2JD;EACE,gBAAA;C3Cq2JD;A2Cj2JD;EACE,wBAAA;EACA,uBAAA;EACA,wBAAA;C3Cm2JD;A2C/1JD;ErCjRE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqCqQA,YAAA;C3Cs2JD;A2Cn2JD;ErCtRE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqC0QA,YAAA;C3C02JD;A2Cv2JD;ErC3RE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqC+QA,YAAA;C3C82JD;A2C32JD;ErChSE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqCoRA,YAAA;C3Ck3JD;A2C/2JD;ErCrSE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqCyRA,YAAA;C3Cs3JD;A2Cn3JD;ErC1SE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqC8RA,YAAA;C3C03JD;A2Cv3JD;ErC/SE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqCmSA,YAAA;C3C83JD;A2C33JD;ErCpTE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqCwSA,YAAA;C3Ck4JD;A2C/3JD;ErCzTE,4BAAA;EACA,oHAAA;EAWA,yDAAA;EAEA,2HAAA;EqC6SA,YAAA;C3Cs4JD;A2Cn4JD;ErC9TE,+BAAA;EACA,uHAAA;EAWA,4DAAA;EAEA,8HAAA;EqCkTA,YAAA;C3C04JD;A2Ct4JD;EAEI,gBAAA;C3Cu4JH;A2Cl4JD;EACE,eAAA;C3Co4JD;A2Ch4JD;EACE,4BAAA;C3Ck4JD;A2C93JD;EACE,gBAAA;EACA,kBAAA;EACA,kBAAA;EACA,YAAA;C3Cg4JD;A2C73JD;EACE,YAAA;EACA,oBAAA;EACA,qBAAA;C3C+3JD;A2C53JD;EAEI,aAAA;EACA,YAAA;C3C63JH;A2C53JG;EACE,YAAA;C3C83JL;A2Cx3JD;EACE,iBAAA;C3C03JD;AE3zKC;;EAEE,aAAA;EACA,eAAA;CF6zKH;AE3zKC;EACE,YAAA;CF6zKH;A2C93JD;EAGI,YAAA;EACA,aAAA;EACA,YAAA;C3C83JH;A2Cn4JD;;;EAUI,eAAA;EACA,kBAAA;C3C83JH;A2Cz4JD;EAcI,gBAAA;EACA,iBAAA;C3C83JH;A2C74JD;EAkBI,YAAA;EACA,gBAAA;C3C83JH;A2C53JC;;;EAOI,kBAAA;C3C03JL;A2Cj4JC;EAUI,gBAAA;C3C03JL;A2Cp3JD;;;;;EAGE,YAAA;C3Cw3JD;A2Cr3JD;;;EACE,uBAAA;EACA,wBAAA;C3Cy3JD;A2C33JD;EAII,kBAAA;C3C03JH;A2Ct3JD;EACE,YAAA;EACA,aAAA;C3Cw3JD;A2C13JD;EAII,kBAAA;C3Cy3JH;A2Cr3JD;EACE,aAAA;EACA,cAAA;C3Cu3JD;A2Cz3JD;EAII,mBAAA;C3Cw3JH;A2Cn3JD;EACE,0BAAA;EACA,aAAA;C3Cq3JD;A2Cl3JD;EACE,0BAAA;EACA,aAAA;C3Co3JD;A2Ch3JD;EACE,0BAAA;EACA,aAAA;EACA,oBAAA;EACA,oBAAA;C3Ck3JD;A2Ct3JD;EAOI,iBAAA;EACA,kBAAA;EACA,aAAA;EACA,YAAA;C3Ck3JH;A2C53JD;EAaI,mBAAA;C3Ck3JH;A2C/3JD;EAgBI,UAAA;C3Ck3JH;A2Cl4JD;EAmBI,YAAA;C3Ck3JH;A2C92JD;EACE,kBAAA;C3Cg3JD;A2C72JD;EACE,UAAA;EACA,oBAAA;EACA,YAAA;EACA,aAAA;EACA,iBAAA;EACA,WAAA;EACA,mBAAA;EACA,WAAA;C3C+2JD;A2C52JD;EACE,oBAAA;EACA,wBAAA;EACA,oBAAA;C3C82JD;A2C32JD;EtB1kBE,cAAA;EAGA,0BAAA;CrBs7KD;A2C72JC;EtB5kBA,WAAA;EAGA,2BAAA;CrB07KD;A2C32JD;EACE,mBAAA;EACA,iBAAA;EACA,YAAA;C3C62JD;A2Ch3JD;;EAMI,uBAAA;C3C82JH;AACD;;;GAGG;A4Cz8KH;EAEE;;;;;IACE,yBAAA;G5C88KD;E4Cl8KD;;;IAGE,0BAAA;IACA,yBAAA;IzCwIF,8CAAA;IAGA,sCAAA;GH6zKC;E4Cp8KD;;IAEE,0BAAA;G5Cs8KD;E4Cl8KD;IACE,YAAA;IACA,UAAA;IACA,UAAA;IACA,WAAA;G5Co8KD;E4Cj8KD;IACE,YAAA;IACA,mBAAA;G5Cm8KD;E4C/7KD;IACE,eAAA;G5Ci8KD;E4Cl8KD;;IAII,+BAAA;G5Ck8KH;CACF", "file": "adminlte-less.css", "sourcesContent": ["/*!\n *   AdminLTE v2.3.8\n *   Author: Almsaeed Studio\n *\t Website: Almsaeed Studio <http://almsaeedstudio.com>\n *   License: Open source - MIT\n *           Please visit http://opensource.org/licenses/MIT for more information\n!*/\n@import url(https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic);\n/*\n * Core: General Layout Style\n * -------------------------\n */\nhtml,\nbody {\n  height: 100%;\n}\n.layout-boxed html,\n.layout-boxed body {\n  height: 100%;\n}\nbody {\n  font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n  font-weight: 400;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n/* Layout */\n.wrapper {\n  height: 100%;\n  position: relative;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n.wrapper:before,\n.wrapper:after {\n  content: \" \";\n  display: table;\n}\n.wrapper:after {\n  clear: both;\n}\n.layout-boxed .wrapper {\n  max-width: 1250px;\n  margin: 0 auto;\n  min-height: 100%;\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\n  position: relative;\n}\n.layout-boxed {\n  background: url('../img/boxed-bg.jpg') repeat fixed;\n}\n/*\n * Content Wrapper - contains the main content\n * ```.right-side has been deprecated as of v2.0.0 in favor of .content-wrapper  ```\n */\n.content-wrapper,\n.right-side,\n.main-footer {\n  -webkit-transition: -webkit-transform 0.3s ease-in-out, margin 0.3s ease-in-out;\n  -moz-transition: -moz-transform 0.3s ease-in-out, margin 0.3s ease-in-out;\n  -o-transition: -o-transform 0.3s ease-in-out, margin 0.3s ease-in-out;\n  transition: transform 0.3s ease-in-out, margin 0.3s ease-in-out;\n  margin-left: 230px;\n  z-index: 820;\n}\n.layout-top-nav .content-wrapper,\n.layout-top-nav .right-side,\n.layout-top-nav .main-footer {\n  margin-left: 0;\n}\n@media (max-width: 767px) {\n  .content-wrapper,\n  .right-side,\n  .main-footer {\n    margin-left: 0;\n  }\n}\n@media (min-width: 768px) {\n  .sidebar-collapse .content-wrapper,\n  .sidebar-collapse .right-side,\n  .sidebar-collapse .main-footer {\n    margin-left: 0;\n  }\n}\n@media (max-width: 767px) {\n  .sidebar-open .content-wrapper,\n  .sidebar-open .right-side,\n  .sidebar-open .main-footer {\n    -webkit-transform: translate(230px, 0);\n    -ms-transform: translate(230px, 0);\n    -o-transform: translate(230px, 0);\n    transform: translate(230px, 0);\n  }\n}\n.content-wrapper,\n.right-side {\n  min-height: 100%;\n  background-color: #ecf0f5;\n  z-index: 800;\n}\n.main-footer {\n  background: #fff;\n  padding: 15px;\n  color: #444;\n  border-top: 1px solid #d2d6de;\n}\n/* Fixed layout */\n.fixed .main-header,\n.fixed .main-sidebar,\n.fixed .left-side {\n  position: fixed;\n}\n.fixed .main-header {\n  top: 0;\n  right: 0;\n  left: 0;\n}\n.fixed .content-wrapper,\n.fixed .right-side {\n  padding-top: 50px;\n}\n@media (max-width: 767px) {\n  .fixed .content-wrapper,\n  .fixed .right-side {\n    padding-top: 100px;\n  }\n}\n.fixed.layout-boxed .wrapper {\n  max-width: 100%;\n}\nbody.hold-transition .content-wrapper,\nbody.hold-transition .right-side,\nbody.hold-transition .main-footer,\nbody.hold-transition .main-sidebar,\nbody.hold-transition .left-side,\nbody.hold-transition .main-header .navbar,\nbody.hold-transition .main-header .logo {\n  /* Fix for IE */\n  -webkit-transition: none;\n  -o-transition: none;\n  transition: none;\n}\n/* Content */\n.content {\n  min-height: 250px;\n  padding: 15px;\n  margin-right: auto;\n  margin-left: auto;\n  padding-left: 15px;\n  padding-right: 15px;\n}\n/* H1 - H6 font */\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\n.h1,\n.h2,\n.h3,\n.h4,\n.h5,\n.h6 {\n  font-family: 'Source Sans Pro', sans-serif;\n}\n/* General Links */\na {\n  color: #3c8dbc;\n}\na:hover,\na:active,\na:focus {\n  outline: none;\n  text-decoration: none;\n  color: #72afd2;\n}\n/* Page Header */\n.page-header {\n  margin: 10px 0 20px 0;\n  font-size: 22px;\n}\n.page-header > small {\n  color: #666;\n  display: block;\n  margin-top: 5px;\n}\n/*\n * Component: Main Header\n * ----------------------\n */\n.main-header {\n  position: relative;\n  max-height: 100px;\n  z-index: 1030;\n}\n.main-header .navbar {\n  -webkit-transition: margin-left 0.3s ease-in-out;\n  -o-transition: margin-left 0.3s ease-in-out;\n  transition: margin-left 0.3s ease-in-out;\n  margin-bottom: 0;\n  margin-left: 230px;\n  border: none;\n  min-height: 50px;\n  border-radius: 0;\n}\n.layout-top-nav .main-header .navbar {\n  margin-left: 0;\n}\n.main-header #navbar-search-input.form-control {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: transparent;\n}\n.main-header #navbar-search-input.form-control:focus,\n.main-header #navbar-search-input.form-control:active {\n  border-color: rgba(0, 0, 0, 0.1);\n  background: rgba(255, 255, 255, 0.9);\n}\n.main-header #navbar-search-input.form-control::-moz-placeholder {\n  color: #ccc;\n  opacity: 1;\n}\n.main-header #navbar-search-input.form-control:-ms-input-placeholder {\n  color: #ccc;\n}\n.main-header #navbar-search-input.form-control::-webkit-input-placeholder {\n  color: #ccc;\n}\n.main-header .navbar-custom-menu,\n.main-header .navbar-right {\n  float: right;\n}\n@media (max-width: 991px) {\n  .main-header .navbar-custom-menu a,\n  .main-header .navbar-right a {\n    color: inherit;\n    background: transparent;\n  }\n}\n@media (max-width: 767px) {\n  .main-header .navbar-right {\n    float: none;\n  }\n  .navbar-collapse .main-header .navbar-right {\n    margin: 7.5px -15px;\n  }\n  .main-header .navbar-right > li {\n    color: inherit;\n    border: 0;\n  }\n}\n.main-header .sidebar-toggle {\n  float: left;\n  background-color: transparent;\n  background-image: none;\n  padding: 15px 15px;\n  font-family: fontAwesome;\n}\n.main-header .sidebar-toggle:before {\n  content: \"\\f0c9\";\n}\n.main-header .sidebar-toggle:hover {\n  color: #fff;\n}\n.main-header .sidebar-toggle:focus,\n.main-header .sidebar-toggle:active {\n  background: transparent;\n}\n.main-header .sidebar-toggle .icon-bar {\n  display: none;\n}\n.main-header .navbar .nav > li.user > a > .fa,\n.main-header .navbar .nav > li.user > a > .glyphicon,\n.main-header .navbar .nav > li.user > a > .ion {\n  margin-right: 5px;\n}\n.main-header .navbar .nav > li > a > .label {\n  position: absolute;\n  top: 9px;\n  right: 7px;\n  text-align: center;\n  font-size: 9px;\n  padding: 2px 3px;\n  line-height: .9;\n}\n.main-header .logo {\n  -webkit-transition: width 0.3s ease-in-out;\n  -o-transition: width 0.3s ease-in-out;\n  transition: width 0.3s ease-in-out;\n  display: block;\n  float: left;\n  height: 50px;\n  font-size: 20px;\n  line-height: 50px;\n  text-align: center;\n  width: 230px;\n  font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n  padding: 0 15px;\n  font-weight: 300;\n  overflow: hidden;\n}\n.main-header .logo .logo-lg {\n  display: block;\n}\n.main-header .logo .logo-mini {\n  display: none;\n}\n.main-header .navbar-brand {\n  color: #fff;\n}\n.content-header {\n  position: relative;\n  padding: 15px 15px 0 15px;\n}\n.content-header > h1 {\n  margin: 0;\n  font-size: 24px;\n}\n.content-header > h1 > small {\n  font-size: 15px;\n  display: inline-block;\n  padding-left: 4px;\n  font-weight: 300;\n}\n.content-header > .breadcrumb {\n  float: right;\n  background: transparent;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: 12px;\n  padding: 7px 5px;\n  position: absolute;\n  top: 15px;\n  right: 10px;\n  border-radius: 2px;\n}\n.content-header > .breadcrumb > li > a {\n  color: #444;\n  text-decoration: none;\n  display: inline-block;\n}\n.content-header > .breadcrumb > li > a > .fa,\n.content-header > .breadcrumb > li > a > .glyphicon,\n.content-header > .breadcrumb > li > a > .ion {\n  margin-right: 5px;\n}\n.content-header > .breadcrumb > li + li:before {\n  content: '>\\00a0';\n}\n@media (max-width: 991px) {\n  .content-header > .breadcrumb {\n    position: relative;\n    margin-top: 5px;\n    top: 0;\n    right: 0;\n    float: none;\n    background: #d2d6de;\n    padding-left: 10px;\n  }\n  .content-header > .breadcrumb li:before {\n    color: #97a0b3;\n  }\n}\n.navbar-toggle {\n  color: #fff;\n  border: 0;\n  margin: 0;\n  padding: 15px 15px;\n}\n@media (max-width: 991px) {\n  .navbar-custom-menu .navbar-nav > li {\n    float: left;\n  }\n  .navbar-custom-menu .navbar-nav {\n    margin: 0;\n    float: left;\n  }\n  .navbar-custom-menu .navbar-nav > li > a {\n    padding-top: 15px;\n    padding-bottom: 15px;\n    line-height: 20px;\n  }\n}\n@media (max-width: 767px) {\n  .main-header {\n    position: relative;\n  }\n  .main-header .logo,\n  .main-header .navbar {\n    width: 100%;\n    float: none;\n  }\n  .main-header .navbar {\n    margin: 0;\n  }\n  .main-header .navbar-custom-menu {\n    float: right;\n  }\n}\n@media (max-width: 991px) {\n  .navbar-collapse.pull-left {\n    float: none !important;\n  }\n  .navbar-collapse.pull-left + .navbar-custom-menu {\n    display: block;\n    position: absolute;\n    top: 0;\n    right: 40px;\n  }\n}\n/*\n * Component: Sidebar\n * ------------------\n */\n.main-sidebar,\n.left-side {\n  position: absolute;\n  top: 0;\n  left: 0;\n  padding-top: 50px;\n  min-height: 100%;\n  width: 230px;\n  z-index: 810;\n  -webkit-transition: -webkit-transform 0.3s ease-in-out, width 0.3s ease-in-out;\n  -moz-transition: -moz-transform 0.3s ease-in-out, width 0.3s ease-in-out;\n  -o-transition: -o-transform 0.3s ease-in-out, width 0.3s ease-in-out;\n  transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;\n}\n@media (max-width: 767px) {\n  .main-sidebar,\n  .left-side {\n    padding-top: 100px;\n  }\n}\n@media (max-width: 767px) {\n  .main-sidebar,\n  .left-side {\n    -webkit-transform: translate(-230px, 0);\n    -ms-transform: translate(-230px, 0);\n    -o-transform: translate(-230px, 0);\n    transform: translate(-230px, 0);\n  }\n}\n@media (min-width: 768px) {\n  .sidebar-collapse .main-sidebar,\n  .sidebar-collapse .left-side {\n    -webkit-transform: translate(-230px, 0);\n    -ms-transform: translate(-230px, 0);\n    -o-transform: translate(-230px, 0);\n    transform: translate(-230px, 0);\n  }\n}\n@media (max-width: 767px) {\n  .sidebar-open .main-sidebar,\n  .sidebar-open .left-side {\n    -webkit-transform: translate(0, 0);\n    -ms-transform: translate(0, 0);\n    -o-transform: translate(0, 0);\n    transform: translate(0, 0);\n  }\n}\n.sidebar {\n  padding-bottom: 10px;\n}\n.sidebar-form input:focus {\n  border-color: transparent;\n}\n.user-panel {\n  position: relative;\n  width: 100%;\n  padding: 10px;\n  overflow: hidden;\n}\n.user-panel:before,\n.user-panel:after {\n  content: \" \";\n  display: table;\n}\n.user-panel:after {\n  clear: both;\n}\n.user-panel > .image > img {\n  width: 100%;\n  max-width: 45px;\n  height: auto;\n}\n.user-panel > .info {\n  padding: 5px 5px 5px 15px;\n  line-height: 1;\n  position: absolute;\n  left: 55px;\n}\n.user-panel > .info > p {\n  font-weight: 600;\n  margin-bottom: 9px;\n}\n.user-panel > .info > a {\n  text-decoration: none;\n  padding-right: 5px;\n  margin-top: 3px;\n  font-size: 11px;\n}\n.user-panel > .info > a > .fa,\n.user-panel > .info > a > .ion,\n.user-panel > .info > a > .glyphicon {\n  margin-right: 3px;\n}\n.sidebar-menu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.sidebar-menu > li {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n.sidebar-menu > li > a {\n  padding: 12px 5px 12px 15px;\n  display: block;\n}\n.sidebar-menu > li > a > .fa,\n.sidebar-menu > li > a > .glyphicon,\n.sidebar-menu > li > a > .ion {\n  width: 20px;\n}\n.sidebar-menu > li .label,\n.sidebar-menu > li .badge {\n  margin-right: 5px;\n}\n.sidebar-menu > li .badge {\n  margin-top: 3px;\n}\n.sidebar-menu li.header {\n  padding: 10px 25px 10px 15px;\n  font-size: 12px;\n}\n.sidebar-menu li > a > .fa-angle-left,\n.sidebar-menu li > a > .pull-right-container > .fa-angle-left {\n  width: auto;\n  height: auto;\n  padding: 0;\n  margin-right: 10px;\n}\n.sidebar-menu li > a > .fa-angle-left {\n  position: absolute;\n  top: 50%;\n  right: 10px;\n  margin-top: -8px;\n}\n.sidebar-menu li.active > a > .fa-angle-left,\n.sidebar-menu li.active > a > .pull-right-container > .fa-angle-left {\n  -webkit-transform: rotate(-90deg);\n  -ms-transform: rotate(-90deg);\n  -o-transform: rotate(-90deg);\n  transform: rotate(-90deg);\n}\n.sidebar-menu li.active > .treeview-menu {\n  display: block;\n}\n.sidebar-menu .treeview-menu {\n  display: none;\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  padding-left: 5px;\n}\n.sidebar-menu .treeview-menu .treeview-menu {\n  padding-left: 20px;\n}\n.sidebar-menu .treeview-menu > li {\n  margin: 0;\n}\n.sidebar-menu .treeview-menu > li > a {\n  padding: 5px 5px 5px 15px;\n  display: block;\n  font-size: 14px;\n}\n.sidebar-menu .treeview-menu > li > a > .fa,\n.sidebar-menu .treeview-menu > li > a > .glyphicon,\n.sidebar-menu .treeview-menu > li > a > .ion {\n  width: 20px;\n}\n.sidebar-menu .treeview-menu > li > a > .pull-right-container > .fa-angle-left,\n.sidebar-menu .treeview-menu > li > a > .pull-right-container > .fa-angle-down,\n.sidebar-menu .treeview-menu > li > a > .fa-angle-left,\n.sidebar-menu .treeview-menu > li > a > .fa-angle-down {\n  width: auto;\n}\n/*\n * Component: Sidebar Mini\n */\n@media (min-width: 768px) {\n  .sidebar-mini.sidebar-collapse .content-wrapper,\n  .sidebar-mini.sidebar-collapse .right-side,\n  .sidebar-mini.sidebar-collapse .main-footer {\n    margin-left: 50px !important;\n    z-index: 840;\n  }\n  .sidebar-mini.sidebar-collapse .main-sidebar {\n    -webkit-transform: translate(0, 0);\n    -ms-transform: translate(0, 0);\n    -o-transform: translate(0, 0);\n    transform: translate(0, 0);\n    width: 50px !important;\n    z-index: 850;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li {\n    position: relative;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li > a {\n    margin-right: 0;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li > a > span {\n    border-top-right-radius: 4px;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:not(.treeview) > a > span {\n    border-bottom-right-radius: 4px;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {\n    padding-top: 5px;\n    padding-bottom: 5px;\n    border-bottom-right-radius: 4px;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right),\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > .treeview-menu {\n    display: block !important;\n    position: absolute;\n    width: 180px;\n    left: 50px;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span {\n    top: 0;\n    margin-left: -3px;\n    padding: 12px 5px 12px 20px;\n    background-color: inherit;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > .pull-right-container {\n    position: relative!important;\n    float: right;\n    width: auto!important;\n    left: 180px !important;\n    top: -22px !important;\n    z-index: 900;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > .pull-right-container > .label:not(:first-of-type) {\n    display: none;\n  }\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > .treeview-menu {\n    top: 44px;\n    margin-left: 0;\n  }\n  .sidebar-mini.sidebar-collapse .main-sidebar .user-panel > .info,\n  .sidebar-mini.sidebar-collapse .sidebar-form,\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li > a > span,\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu,\n  .sidebar-mini.sidebar-collapse .sidebar-menu > li > a > .pull-right,\n  .sidebar-mini.sidebar-collapse .sidebar-menu li.header {\n    display: none !important;\n    -webkit-transform: translateZ(0);\n  }\n  .sidebar-mini.sidebar-collapse .main-header .logo {\n    width: 50px;\n  }\n  .sidebar-mini.sidebar-collapse .main-header .logo > .logo-mini {\n    display: block;\n    margin-left: -15px;\n    margin-right: -15px;\n    font-size: 18px;\n  }\n  .sidebar-mini.sidebar-collapse .main-header .logo > .logo-lg {\n    display: none;\n  }\n  .sidebar-mini.sidebar-collapse .main-header .navbar {\n    margin-left: 50px;\n  }\n}\n.sidebar-menu,\n.main-sidebar .user-panel,\n.sidebar-menu > li.header {\n  white-space: nowrap;\n  overflow: hidden;\n}\n.sidebar-menu:hover {\n  overflow: visible;\n}\n.sidebar-form,\n.sidebar-menu > li.header {\n  overflow: hidden;\n  text-overflow: clip;\n}\n.sidebar-menu li > a {\n  position: relative;\n}\n.sidebar-menu li > a > .pull-right-container {\n  position: absolute;\n  right: 10px;\n  top: 50%;\n  margin-top: -7px;\n}\n/*\n * Component: Control sidebar. By default, this is the right sidebar.\n */\n.control-sidebar-bg {\n  position: fixed;\n  z-index: 1000;\n  bottom: 0;\n}\n.control-sidebar-bg,\n.control-sidebar {\n  top: 0;\n  right: -230px;\n  width: 230px;\n  -webkit-transition: right 0.3s ease-in-out;\n  -o-transition: right 0.3s ease-in-out;\n  transition: right 0.3s ease-in-out;\n}\n.control-sidebar {\n  position: absolute;\n  padding-top: 50px;\n  z-index: 1010;\n}\n@media (max-width: 768px) {\n  .control-sidebar {\n    padding-top: 100px;\n  }\n}\n.control-sidebar > .tab-content {\n  padding: 10px 15px;\n}\n.control-sidebar.control-sidebar-open,\n.control-sidebar.control-sidebar-open + .control-sidebar-bg {\n  right: 0;\n}\n.control-sidebar-open .control-sidebar-bg,\n.control-sidebar-open .control-sidebar {\n  right: 0;\n}\n@media (min-width: 768px) {\n  .control-sidebar-open .content-wrapper,\n  .control-sidebar-open .right-side,\n  .control-sidebar-open .main-footer {\n    margin-right: 230px;\n  }\n}\n.nav-tabs.control-sidebar-tabs > li:first-of-type > a,\n.nav-tabs.control-sidebar-tabs > li:first-of-type > a:hover,\n.nav-tabs.control-sidebar-tabs > li:first-of-type > a:focus {\n  border-left-width: 0;\n}\n.nav-tabs.control-sidebar-tabs > li > a {\n  border-radius: 0;\n}\n.nav-tabs.control-sidebar-tabs > li > a,\n.nav-tabs.control-sidebar-tabs > li > a:hover {\n  border-top: none;\n  border-right: none;\n  border-left: 1px solid transparent;\n  border-bottom: 1px solid transparent;\n}\n.nav-tabs.control-sidebar-tabs > li > a .icon {\n  font-size: 16px;\n}\n.nav-tabs.control-sidebar-tabs > li.active > a,\n.nav-tabs.control-sidebar-tabs > li.active > a:hover,\n.nav-tabs.control-sidebar-tabs > li.active > a:focus,\n.nav-tabs.control-sidebar-tabs > li.active > a:active {\n  border-top: none;\n  border-right: none;\n  border-bottom: none;\n}\n@media (max-width: 768px) {\n  .nav-tabs.control-sidebar-tabs {\n    display: table;\n  }\n  .nav-tabs.control-sidebar-tabs > li {\n    display: table-cell;\n  }\n}\n.control-sidebar-heading {\n  font-weight: 400;\n  font-size: 16px;\n  padding: 10px 0;\n  margin-bottom: 10px;\n}\n.control-sidebar-subheading {\n  display: block;\n  font-weight: 400;\n  font-size: 14px;\n}\n.control-sidebar-menu {\n  list-style: none;\n  padding: 0;\n  margin: 0 -15px;\n}\n.control-sidebar-menu > li > a {\n  display: block;\n  padding: 10px 15px;\n}\n.control-sidebar-menu > li > a:before,\n.control-sidebar-menu > li > a:after {\n  content: \" \";\n  display: table;\n}\n.control-sidebar-menu > li > a:after {\n  clear: both;\n}\n.control-sidebar-menu > li > a > .control-sidebar-subheading {\n  margin-top: 0;\n}\n.control-sidebar-menu .menu-icon {\n  float: left;\n  width: 35px;\n  height: 35px;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 35px;\n}\n.control-sidebar-menu .menu-info {\n  margin-left: 45px;\n  margin-top: 3px;\n}\n.control-sidebar-menu .menu-info > .control-sidebar-subheading {\n  margin: 0;\n}\n.control-sidebar-menu .menu-info > p {\n  margin: 0;\n  font-size: 11px;\n}\n.control-sidebar-menu .progress {\n  margin: 0;\n}\n.control-sidebar-dark {\n  color: #b8c7ce;\n}\n.control-sidebar-dark,\n.control-sidebar-dark + .control-sidebar-bg {\n  background: #222d32;\n}\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs {\n  border-bottom: #1c2529;\n}\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a {\n  background: #181f23;\n  color: #b8c7ce;\n}\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a:hover,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a:focus {\n  border-left-color: #141a1d;\n  border-bottom-color: #141a1d;\n}\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a:hover,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a:focus,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a:active {\n  background: #1c2529;\n}\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li > a:hover {\n  color: #fff;\n}\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li.active > a,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li.active > a:hover,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li.active > a:focus,\n.control-sidebar-dark .nav-tabs.control-sidebar-tabs > li.active > a:active {\n  background: #222d32;\n  color: #fff;\n}\n.control-sidebar-dark .control-sidebar-heading,\n.control-sidebar-dark .control-sidebar-subheading {\n  color: #fff;\n}\n.control-sidebar-dark .control-sidebar-menu > li > a:hover {\n  background: #1e282c;\n}\n.control-sidebar-dark .control-sidebar-menu > li > a .menu-info > p {\n  color: #b8c7ce;\n}\n.control-sidebar-light {\n  color: #5e5e5e;\n}\n.control-sidebar-light,\n.control-sidebar-light + .control-sidebar-bg {\n  background: #f9fafc;\n  border-left: 1px solid #d2d6de;\n}\n.control-sidebar-light .nav-tabs.control-sidebar-tabs {\n  border-bottom: #d2d6de;\n}\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a {\n  background: #e8ecf4;\n  color: #444;\n}\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a:hover,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a:focus {\n  border-left-color: #d2d6de;\n  border-bottom-color: #d2d6de;\n}\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a:hover,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a:focus,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li > a:active {\n  background: #eff1f7;\n}\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li.active > a,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li.active > a:hover,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li.active > a:focus,\n.control-sidebar-light .nav-tabs.control-sidebar-tabs > li.active > a:active {\n  background: #f9fafc;\n  color: #111;\n}\n.control-sidebar-light .control-sidebar-heading,\n.control-sidebar-light .control-sidebar-subheading {\n  color: #111;\n}\n.control-sidebar-light .control-sidebar-menu {\n  margin-left: -14px;\n}\n.control-sidebar-light .control-sidebar-menu > li > a:hover {\n  background: #f4f4f5;\n}\n.control-sidebar-light .control-sidebar-menu > li > a .menu-info > p {\n  color: #5e5e5e;\n}\n/*\n * Component: Dropdown menus\n * -------------------------\n */\n/*Dropdowns in general*/\n.dropdown-menu {\n  box-shadow: none;\n  border-color: #eee;\n}\n.dropdown-menu > li > a {\n  color: #777;\n}\n.dropdown-menu > li > a > .glyphicon,\n.dropdown-menu > li > a > .fa,\n.dropdown-menu > li > a > .ion {\n  margin-right: 10px;\n}\n.dropdown-menu > li > a:hover {\n  background-color: #e1e3e9;\n  color: #333;\n}\n.dropdown-menu > .divider {\n  background-color: #eee;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu,\n.navbar-nav > .messages-menu > .dropdown-menu,\n.navbar-nav > .tasks-menu > .dropdown-menu {\n  width: 280px;\n  padding: 0 0 0 0;\n  margin: 0;\n  top: 100%;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li,\n.navbar-nav > .messages-menu > .dropdown-menu > li,\n.navbar-nav > .tasks-menu > .dropdown-menu > li {\n  position: relative;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li.header,\n.navbar-nav > .messages-menu > .dropdown-menu > li.header,\n.navbar-nav > .tasks-menu > .dropdown-menu > li.header {\n  border-top-left-radius: 4px;\n  border-top-right-radius: 4px;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background-color: #ffffff;\n  padding: 7px 10px;\n  border-bottom: 1px solid #f4f4f4;\n  color: #444444;\n  font-size: 14px;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li.footer > a,\n.navbar-nav > .messages-menu > .dropdown-menu > li.footer > a,\n.navbar-nav > .tasks-menu > .dropdown-menu > li.footer > a {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  font-size: 12px;\n  background-color: #fff;\n  padding: 7px 10px;\n  border-bottom: 1px solid #eeeeee;\n  color: #444 !important;\n  text-align: center;\n}\n@media (max-width: 991px) {\n  .navbar-nav > .notifications-menu > .dropdown-menu > li.footer > a,\n  .navbar-nav > .messages-menu > .dropdown-menu > li.footer > a,\n  .navbar-nav > .tasks-menu > .dropdown-menu > li.footer > a {\n    background: #fff !important;\n    color: #444 !important;\n  }\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li.footer > a:hover,\n.navbar-nav > .messages-menu > .dropdown-menu > li.footer > a:hover,\n.navbar-nav > .tasks-menu > .dropdown-menu > li.footer > a:hover {\n  text-decoration: none;\n  font-weight: normal;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu,\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu,\n.navbar-nav > .tasks-menu > .dropdown-menu > li .menu {\n  max-height: 200px;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  overflow-x: hidden;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a,\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a,\n.navbar-nav > .tasks-menu > .dropdown-menu > li .menu > li > a {\n  display: block;\n  white-space: nowrap;\n  /* Prevent text from breaking */\n  border-bottom: 1px solid #f4f4f4;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a:hover,\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a:hover,\n.navbar-nav > .tasks-menu > .dropdown-menu > li .menu > li > a:hover {\n  background: #f4f4f4;\n  text-decoration: none;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a {\n  color: #444444;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding: 10px;\n}\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > .glyphicon,\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > .fa,\n.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > .ion {\n  width: 20px;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a {\n  margin: 0;\n  padding: 10px 10px;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a > div > img {\n  margin: auto 10px auto auto;\n  width: 40px;\n  height: 40px;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a > h4 {\n  padding: 0;\n  margin: 0 0 0 45px;\n  color: #444444;\n  font-size: 15px;\n  position: relative;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a > h4 > small {\n  color: #999999;\n  font-size: 10px;\n  position: absolute;\n  top: 0;\n  right: 0;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a > p {\n  margin: 0 0 0 45px;\n  font-size: 12px;\n  color: #888888;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a:before,\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a:after {\n  content: \" \";\n  display: table;\n}\n.navbar-nav > .messages-menu > .dropdown-menu > li .menu > li > a:after {\n  clear: both;\n}\n.navbar-nav > .tasks-menu > .dropdown-menu > li .menu > li > a {\n  padding: 10px;\n}\n.navbar-nav > .tasks-menu > .dropdown-menu > li .menu > li > a > h3 {\n  font-size: 14px;\n  padding: 0;\n  margin: 0 0 10px 0;\n  color: #666666;\n}\n.navbar-nav > .tasks-menu > .dropdown-menu > li .menu > li > a > .progress {\n  padding: 0;\n  margin: 0;\n}\n.navbar-nav > .user-menu > .dropdown-menu {\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n  padding: 1px 0 0 0;\n  border-top-width: 0;\n  width: 280px;\n}\n.navbar-nav > .user-menu > .dropdown-menu,\n.navbar-nav > .user-menu > .dropdown-menu > .user-body {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.navbar-nav > .user-menu > .dropdown-menu > li.user-header {\n  height: 175px;\n  padding: 10px;\n  text-align: center;\n}\n.navbar-nav > .user-menu > .dropdown-menu > li.user-header > img {\n  z-index: 5;\n  height: 90px;\n  width: 90px;\n  border: 3px solid;\n  border-color: transparent;\n  border-color: rgba(255, 255, 255, 0.2);\n}\n.navbar-nav > .user-menu > .dropdown-menu > li.user-header > p {\n  z-index: 5;\n  color: #fff;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 17px;\n  margin-top: 10px;\n}\n.navbar-nav > .user-menu > .dropdown-menu > li.user-header > p > small {\n  display: block;\n  font-size: 12px;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-body {\n  padding: 15px;\n  border-bottom: 1px solid #f4f4f4;\n  border-top: 1px solid #dddddd;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-body:before,\n.navbar-nav > .user-menu > .dropdown-menu > .user-body:after {\n  content: \" \";\n  display: table;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-body:after {\n  clear: both;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-body a {\n  color: #444 !important;\n}\n@media (max-width: 991px) {\n  .navbar-nav > .user-menu > .dropdown-menu > .user-body a {\n    background: #fff !important;\n    color: #444 !important;\n  }\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-footer {\n  background-color: #f9f9f9;\n  padding: 10px;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-footer:before,\n.navbar-nav > .user-menu > .dropdown-menu > .user-footer:after {\n  content: \" \";\n  display: table;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-footer:after {\n  clear: both;\n}\n.navbar-nav > .user-menu > .dropdown-menu > .user-footer .btn-default {\n  color: #666666;\n}\n@media (max-width: 991px) {\n  .navbar-nav > .user-menu > .dropdown-menu > .user-footer .btn-default:hover {\n    background-color: #f9f9f9;\n  }\n}\n.navbar-nav > .user-menu .user-image {\n  float: left;\n  width: 25px;\n  height: 25px;\n  border-radius: 50%;\n  margin-right: 10px;\n  margin-top: -2px;\n}\n@media (max-width: 767px) {\n  .navbar-nav > .user-menu .user-image {\n    float: none;\n    margin-right: 0;\n    margin-top: -8px;\n    line-height: 10px;\n  }\n}\n/* Add fade animation to dropdown menus by appending\n the class .animated-dropdown-menu to the .dropdown-menu ul (or ol)*/\n.open:not(.dropup) > .animated-dropdown-menu {\n  backface-visibility: visible !important;\n  -webkit-animation: flipInX 0.7s both;\n  -o-animation: flipInX 0.7s both;\n  animation: flipInX 0.7s both;\n}\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    transform: perspective(400px);\n  }\n}\n@-webkit-keyframes flipInX {\n  0% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    -webkit-transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    -webkit-transition-timing-function: ease-in;\n  }\n  60% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    -webkit-transform: perspective(400px);\n  }\n}\n/* Fix dropdown menu in navbars */\n.navbar-custom-menu > .navbar-nav > li {\n  position: relative;\n}\n.navbar-custom-menu > .navbar-nav > li > .dropdown-menu {\n  position: absolute;\n  right: 0;\n  left: auto;\n}\n@media (max-width: 991px) {\n  .navbar-custom-menu > .navbar-nav {\n    float: right;\n  }\n  .navbar-custom-menu > .navbar-nav > li {\n    position: static;\n  }\n  .navbar-custom-menu > .navbar-nav > li > .dropdown-menu {\n    position: absolute;\n    right: 5%;\n    left: auto;\n    border: 1px solid #ddd;\n    background: #fff;\n  }\n}\n/*\n * Component: Form\n * ---------------\n */\n.form-control {\n  border-radius: 0;\n  box-shadow: none;\n  border-color: #d2d6de;\n}\n.form-control:focus {\n  border-color: #3c8dbc;\n  box-shadow: none;\n}\n.form-control::-moz-placeholder,\n.form-control:-ms-input-placeholder,\n.form-control::-webkit-input-placeholder {\n  color: #bbb;\n  opacity: 1;\n}\n.form-control:not(select) {\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n}\n.form-group.has-success label {\n  color: #00a65a;\n}\n.form-group.has-success .form-control,\n.form-group.has-success .input-group-addon {\n  border-color: #00a65a;\n  box-shadow: none;\n}\n.form-group.has-success .help-block {\n  color: #00a65a;\n}\n.form-group.has-warning label {\n  color: #f39c12;\n}\n.form-group.has-warning .form-control,\n.form-group.has-warning .input-group-addon {\n  border-color: #f39c12;\n  box-shadow: none;\n}\n.form-group.has-warning .help-block {\n  color: #f39c12;\n}\n.form-group.has-error label {\n  color: #dd4b39;\n}\n.form-group.has-error .form-control,\n.form-group.has-error .input-group-addon {\n  border-color: #dd4b39;\n  box-shadow: none;\n}\n.form-group.has-error .help-block {\n  color: #dd4b39;\n}\n/* Input group */\n.input-group .input-group-addon {\n  border-radius: 0;\n  border-color: #d2d6de;\n  background-color: #fff;\n}\n/* button groups */\n.btn-group-vertical .btn.btn-flat:first-of-type,\n.btn-group-vertical .btn.btn-flat:last-of-type {\n  border-radius: 0;\n}\n.icheck > label {\n  padding-left: 0;\n}\n/* support Font Awesome icons in form-control */\n.form-control-feedback.fa {\n  line-height: 34px;\n}\n.input-lg + .form-control-feedback.fa,\n.input-group-lg + .form-control-feedback.fa,\n.form-group-lg .form-control + .form-control-feedback.fa {\n  line-height: 46px;\n}\n.input-sm + .form-control-feedback.fa,\n.input-group-sm + .form-control-feedback.fa,\n.form-group-sm .form-control + .form-control-feedback.fa {\n  line-height: 30px;\n}\n/*\n * Component: Progress Bar\n * -----------------------\n */\n.progress,\n.progress > .progress-bar {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.progress,\n.progress > .progress-bar,\n.progress .progress-bar,\n.progress > .progress-bar .progress-bar {\n  border-radius: 1px;\n}\n/* size variation */\n.progress.sm,\n.progress-sm {\n  height: 10px;\n}\n.progress.sm,\n.progress-sm,\n.progress.sm .progress-bar,\n.progress-sm .progress-bar {\n  border-radius: 1px;\n}\n.progress.xs,\n.progress-xs {\n  height: 7px;\n}\n.progress.xs,\n.progress-xs,\n.progress.xs .progress-bar,\n.progress-xs .progress-bar {\n  border-radius: 1px;\n}\n.progress.xxs,\n.progress-xxs {\n  height: 3px;\n}\n.progress.xxs,\n.progress-xxs,\n.progress.xxs .progress-bar,\n.progress-xxs .progress-bar {\n  border-radius: 1px;\n}\n/* Vertical bars */\n.progress.vertical {\n  position: relative;\n  width: 30px;\n  height: 200px;\n  display: inline-block;\n  margin-right: 10px;\n}\n.progress.vertical > .progress-bar {\n  width: 100%;\n  position: absolute;\n  bottom: 0;\n}\n.progress.vertical.sm,\n.progress.vertical.progress-sm {\n  width: 20px;\n}\n.progress.vertical.xs,\n.progress.vertical.progress-xs {\n  width: 10px;\n}\n.progress.vertical.xxs,\n.progress.vertical.progress-xxs {\n  width: 3px;\n}\n.progress-group .progress-text {\n  font-weight: 600;\n}\n.progress-group .progress-number {\n  float: right;\n}\n/* Remove margins from progress bars when put in a table */\n.table tr > td .progress {\n  margin: 0;\n}\n.progress-bar-light-blue,\n.progress-bar-primary {\n  background-color: #3c8dbc;\n}\n.progress-striped .progress-bar-light-blue,\n.progress-striped .progress-bar-primary {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n.progress-bar-green,\n.progress-bar-success {\n  background-color: #00a65a;\n}\n.progress-striped .progress-bar-green,\n.progress-striped .progress-bar-success {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n.progress-bar-aqua,\n.progress-bar-info {\n  background-color: #00c0ef;\n}\n.progress-striped .progress-bar-aqua,\n.progress-striped .progress-bar-info {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n.progress-bar-yellow,\n.progress-bar-warning {\n  background-color: #f39c12;\n}\n.progress-striped .progress-bar-yellow,\n.progress-striped .progress-bar-warning {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n.progress-bar-red,\n.progress-bar-danger {\n  background-color: #dd4b39;\n}\n.progress-striped .progress-bar-red,\n.progress-striped .progress-bar-danger {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n/*\n * Component: Small Box\n * --------------------\n */\n.small-box {\n  border-radius: 2px;\n  position: relative;\n  display: block;\n  margin-bottom: 20px;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n}\n.small-box > .inner {\n  padding: 10px;\n}\n.small-box > .small-box-footer {\n  position: relative;\n  text-align: center;\n  padding: 3px 0;\n  color: #fff;\n  color: rgba(255, 255, 255, 0.8);\n  display: block;\n  z-index: 10;\n  background: rgba(0, 0, 0, 0.1);\n  text-decoration: none;\n}\n.small-box > .small-box-footer:hover {\n  color: #fff;\n  background: rgba(0, 0, 0, 0.15);\n}\n.small-box h3 {\n  font-size: 38px;\n  font-weight: bold;\n  margin: 0 0 10px 0;\n  white-space: nowrap;\n  padding: 0;\n}\n.small-box p {\n  font-size: 15px;\n}\n.small-box p > small {\n  display: block;\n  color: #f9f9f9;\n  font-size: 13px;\n  margin-top: 5px;\n}\n.small-box h3,\n.small-box p {\n  z-index: 5;\n}\n.small-box .icon {\n  -webkit-transition: all 0.3s linear;\n  -o-transition: all 0.3s linear;\n  transition: all 0.3s linear;\n  position: absolute;\n  top: -10px;\n  right: 10px;\n  z-index: 0;\n  font-size: 90px;\n  color: rgba(0, 0, 0, 0.15);\n}\n.small-box:hover {\n  text-decoration: none;\n  color: #f9f9f9;\n}\n.small-box:hover .icon {\n  font-size: 95px;\n}\n@media (max-width: 767px) {\n  .small-box {\n    text-align: center;\n  }\n  .small-box .icon {\n    display: none;\n  }\n  .small-box p {\n    font-size: 12px;\n  }\n}\n/*\n * Component: Box\n * --------------\n */\n.box {\n  position: relative;\n  border-radius: 3px;\n  background: #ffffff;\n  border-top: 3px solid #d2d6de;\n  margin-bottom: 20px;\n  width: 100%;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n}\n.box.box-primary {\n  border-top-color: #3c8dbc;\n}\n.box.box-info {\n  border-top-color: #00c0ef;\n}\n.box.box-danger {\n  border-top-color: #dd4b39;\n}\n.box.box-warning {\n  border-top-color: #f39c12;\n}\n.box.box-success {\n  border-top-color: #00a65a;\n}\n.box.box-default {\n  border-top-color: #d2d6de;\n}\n.box.collapsed-box .box-body,\n.box.collapsed-box .box-footer {\n  display: none;\n}\n.box .nav-stacked > li {\n  border-bottom: 1px solid #f4f4f4;\n  margin: 0;\n}\n.box .nav-stacked > li:last-of-type {\n  border-bottom: none;\n}\n.box.height-control .box-body {\n  max-height: 300px;\n  overflow: auto;\n}\n.box .border-right {\n  border-right: 1px solid #f4f4f4;\n}\n.box .border-left {\n  border-left: 1px solid #f4f4f4;\n}\n.box.box-solid {\n  border-top: 0;\n}\n.box.box-solid > .box-header .btn.btn-default {\n  background: transparent;\n}\n.box.box-solid > .box-header .btn:hover,\n.box.box-solid > .box-header a:hover {\n  background: rgba(0, 0, 0, 0.1);\n}\n.box.box-solid.box-default {\n  border: 1px solid #d2d6de;\n}\n.box.box-solid.box-default > .box-header {\n  color: #444;\n  background: #d2d6de;\n  background-color: #d2d6de;\n}\n.box.box-solid.box-default > .box-header a,\n.box.box-solid.box-default > .box-header .btn {\n  color: #444;\n}\n.box.box-solid.box-primary {\n  border: 1px solid #3c8dbc;\n}\n.box.box-solid.box-primary > .box-header {\n  color: #fff;\n  background: #3c8dbc;\n  background-color: #3c8dbc;\n}\n.box.box-solid.box-primary > .box-header a,\n.box.box-solid.box-primary > .box-header .btn {\n  color: #fff;\n}\n.box.box-solid.box-info {\n  border: 1px solid #00c0ef;\n}\n.box.box-solid.box-info > .box-header {\n  color: #fff;\n  background: #00c0ef;\n  background-color: #00c0ef;\n}\n.box.box-solid.box-info > .box-header a,\n.box.box-solid.box-info > .box-header .btn {\n  color: #fff;\n}\n.box.box-solid.box-danger {\n  border: 1px solid #dd4b39;\n}\n.box.box-solid.box-danger > .box-header {\n  color: #fff;\n  background: #dd4b39;\n  background-color: #dd4b39;\n}\n.box.box-solid.box-danger > .box-header a,\n.box.box-solid.box-danger > .box-header .btn {\n  color: #fff;\n}\n.box.box-solid.box-warning {\n  border: 1px solid #f39c12;\n}\n.box.box-solid.box-warning > .box-header {\n  color: #fff;\n  background: #f39c12;\n  background-color: #f39c12;\n}\n.box.box-solid.box-warning > .box-header a,\n.box.box-solid.box-warning > .box-header .btn {\n  color: #fff;\n}\n.box.box-solid.box-success {\n  border: 1px solid #00a65a;\n}\n.box.box-solid.box-success > .box-header {\n  color: #fff;\n  background: #00a65a;\n  background-color: #00a65a;\n}\n.box.box-solid.box-success > .box-header a,\n.box.box-solid.box-success > .box-header .btn {\n  color: #fff;\n}\n.box.box-solid > .box-header > .box-tools .btn {\n  border: 0;\n  box-shadow: none;\n}\n.box.box-solid[class*='bg'] > .box-header {\n  color: #fff;\n}\n.box .box-group > .box {\n  margin-bottom: 5px;\n}\n.box .knob-label {\n  text-align: center;\n  color: #333;\n  font-weight: 100;\n  font-size: 12px;\n  margin-bottom: 0.3em;\n}\n.box > .overlay,\n.overlay-wrapper > .overlay,\n.box > .loading-img,\n.overlay-wrapper > .loading-img {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.box .overlay,\n.overlay-wrapper .overlay {\n  z-index: 50;\n  background: rgba(255, 255, 255, 0.7);\n  border-radius: 3px;\n}\n.box .overlay > .fa,\n.overlay-wrapper .overlay > .fa {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin-left: -15px;\n  margin-top: -15px;\n  color: #000;\n  font-size: 30px;\n}\n.box .overlay.dark,\n.overlay-wrapper .overlay.dark {\n  background: rgba(0, 0, 0, 0.5);\n}\n.box-header:before,\n.box-body:before,\n.box-footer:before,\n.box-header:after,\n.box-body:after,\n.box-footer:after {\n  content: \" \";\n  display: table;\n}\n.box-header:after,\n.box-body:after,\n.box-footer:after {\n  clear: both;\n}\n.box-header {\n  color: #444;\n  display: block;\n  padding: 10px;\n  position: relative;\n}\n.box-header.with-border {\n  border-bottom: 1px solid #f4f4f4;\n}\n.collapsed-box .box-header.with-border {\n  border-bottom: none;\n}\n.box-header > .fa,\n.box-header > .glyphicon,\n.box-header > .ion,\n.box-header .box-title {\n  display: inline-block;\n  font-size: 18px;\n  margin: 0;\n  line-height: 1;\n}\n.box-header > .fa,\n.box-header > .glyphicon,\n.box-header > .ion {\n  margin-right: 5px;\n}\n.box-header > .box-tools {\n  position: absolute;\n  right: 10px;\n  top: 5px;\n}\n.box-header > .box-tools [data-toggle=\"tooltip\"] {\n  position: relative;\n}\n.box-header > .box-tools.pull-right .dropdown-menu {\n  right: 0;\n  left: auto;\n}\n.box-header > .box-tools .dropdown-menu > li > a {\n  color: #444!important;\n}\n.btn-box-tool {\n  padding: 5px;\n  font-size: 12px;\n  background: transparent;\n  color: #97a0b3;\n}\n.open .btn-box-tool,\n.btn-box-tool:hover {\n  color: #606c84;\n}\n.btn-box-tool.btn:active {\n  box-shadow: none;\n}\n.box-body {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 3px;\n  padding: 10px;\n}\n.no-header .box-body {\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n.box-body > .table {\n  margin-bottom: 0;\n}\n.box-body .fc {\n  margin-top: 5px;\n}\n.box-body .full-width-chart {\n  margin: -19px;\n}\n.box-body.no-padding .full-width-chart {\n  margin: -9px;\n}\n.box-body .box-pane {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 3px;\n}\n.box-body .box-pane-right {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 0;\n}\n.box-footer {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 3px;\n  border-top: 1px solid #f4f4f4;\n  padding: 10px;\n  background-color: #fff;\n}\n.chart-legend {\n  margin: 10px 0;\n}\n@media (max-width: 991px) {\n  .chart-legend > li {\n    float: left;\n    margin-right: 10px;\n  }\n}\n.box-comments {\n  background: #f7f7f7;\n}\n.box-comments .box-comment {\n  padding: 8px 0;\n  border-bottom: 1px solid #eee;\n}\n.box-comments .box-comment:before,\n.box-comments .box-comment:after {\n  content: \" \";\n  display: table;\n}\n.box-comments .box-comment:after {\n  clear: both;\n}\n.box-comments .box-comment:last-of-type {\n  border-bottom: 0;\n}\n.box-comments .box-comment:first-of-type {\n  padding-top: 0;\n}\n.box-comments .box-comment img {\n  float: left;\n}\n.box-comments .comment-text {\n  margin-left: 40px;\n  color: #555;\n}\n.box-comments .username {\n  color: #444;\n  display: block;\n  font-weight: 600;\n}\n.box-comments .text-muted {\n  font-weight: 400;\n  font-size: 12px;\n}\n/* Widget: TODO LIST */\n.todo-list {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  overflow: auto;\n}\n.todo-list > li {\n  border-radius: 2px;\n  padding: 10px;\n  background: #f4f4f4;\n  margin-bottom: 2px;\n  border-left: 2px solid #e6e7e8;\n  color: #444;\n}\n.todo-list > li:last-of-type {\n  margin-bottom: 0;\n}\n.todo-list > li > input[type='checkbox'] {\n  margin: 0 10px 0 5px;\n}\n.todo-list > li .text {\n  display: inline-block;\n  margin-left: 5px;\n  font-weight: 600;\n}\n.todo-list > li .label {\n  margin-left: 10px;\n  font-size: 9px;\n}\n.todo-list > li .tools {\n  display: none;\n  float: right;\n  color: #dd4b39;\n}\n.todo-list > li .tools > .fa,\n.todo-list > li .tools > .glyphicon,\n.todo-list > li .tools > .ion {\n  margin-right: 5px;\n  cursor: pointer;\n}\n.todo-list > li:hover .tools {\n  display: inline-block;\n}\n.todo-list > li.done {\n  color: #999;\n}\n.todo-list > li.done .text {\n  text-decoration: line-through;\n  font-weight: 500;\n}\n.todo-list > li.done .label {\n  background: #d2d6de !important;\n}\n.todo-list .danger {\n  border-left-color: #dd4b39;\n}\n.todo-list .warning {\n  border-left-color: #f39c12;\n}\n.todo-list .info {\n  border-left-color: #00c0ef;\n}\n.todo-list .success {\n  border-left-color: #00a65a;\n}\n.todo-list .primary {\n  border-left-color: #3c8dbc;\n}\n.todo-list .handle {\n  display: inline-block;\n  cursor: move;\n  margin: 0 5px;\n}\n/* Chat widget (DEPRECATED - this will be removed in the next major release. Use Direct Chat instead)*/\n.chat {\n  padding: 5px 20px 5px 10px;\n}\n.chat .item {\n  margin-bottom: 10px;\n}\n.chat .item:before,\n.chat .item:after {\n  content: \" \";\n  display: table;\n}\n.chat .item:after {\n  clear: both;\n}\n.chat .item > img {\n  width: 40px;\n  height: 40px;\n  border: 2px solid transparent;\n  border-radius: 50%;\n}\n.chat .item > .online {\n  border: 2px solid #00a65a;\n}\n.chat .item > .offline {\n  border: 2px solid #dd4b39;\n}\n.chat .item > .message {\n  margin-left: 55px;\n  margin-top: -40px;\n}\n.chat .item > .message > .name {\n  display: block;\n  font-weight: 600;\n}\n.chat .item > .attachment {\n  border-radius: 3px;\n  background: #f4f4f4;\n  margin-left: 65px;\n  margin-right: 15px;\n  padding: 10px;\n}\n.chat .item > .attachment > h4 {\n  margin: 0 0 5px 0;\n  font-weight: 600;\n  font-size: 14px;\n}\n.chat .item > .attachment > p,\n.chat .item > .attachment > .filename {\n  font-weight: 600;\n  font-size: 13px;\n  font-style: italic;\n  margin: 0;\n}\n.chat .item > .attachment:before,\n.chat .item > .attachment:after {\n  content: \" \";\n  display: table;\n}\n.chat .item > .attachment:after {\n  clear: both;\n}\n.box-input {\n  max-width: 200px;\n}\n.modal .panel-body {\n  color: #444;\n}\n/*\n * Component: Info Box\n * -------------------\n */\n.info-box {\n  display: block;\n  min-height: 90px;\n  background: #fff;\n  width: 100%;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  border-radius: 2px;\n  margin-bottom: 15px;\n}\n.info-box small {\n  font-size: 14px;\n}\n.info-box .progress {\n  background: rgba(0, 0, 0, 0.2);\n  margin: 5px -10px 5px -10px;\n  height: 2px;\n}\n.info-box .progress,\n.info-box .progress .progress-bar {\n  border-radius: 0;\n}\n.info-box .progress .progress-bar {\n  background: #fff;\n}\n.info-box-icon {\n  border-top-left-radius: 2px;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 2px;\n  display: block;\n  float: left;\n  height: 90px;\n  width: 90px;\n  text-align: center;\n  font-size: 45px;\n  line-height: 90px;\n  background: rgba(0, 0, 0, 0.2);\n}\n.info-box-icon > img {\n  max-width: 100%;\n}\n.info-box-content {\n  padding: 5px 10px;\n  margin-left: 90px;\n}\n.info-box-number {\n  display: block;\n  font-weight: bold;\n  font-size: 18px;\n}\n.progress-description,\n.info-box-text {\n  display: block;\n  font-size: 14px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.info-box-text {\n  text-transform: uppercase;\n}\n.info-box-more {\n  display: block;\n}\n.progress-description {\n  margin: 0;\n}\n/*\n * Component: Timeline\n * -------------------\n */\n.timeline {\n  position: relative;\n  margin: 0 0 30px 0;\n  padding: 0;\n  list-style: none;\n}\n.timeline:before {\n  content: '';\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: #ddd;\n  left: 31px;\n  margin: 0;\n  border-radius: 2px;\n}\n.timeline > li {\n  position: relative;\n  margin-right: 10px;\n  margin-bottom: 15px;\n}\n.timeline > li:before,\n.timeline > li:after {\n  content: \" \";\n  display: table;\n}\n.timeline > li:after {\n  clear: both;\n}\n.timeline > li > .timeline-item {\n  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  border-radius: 3px;\n  margin-top: 0;\n  background: #fff;\n  color: #444;\n  margin-left: 60px;\n  margin-right: 15px;\n  padding: 0;\n  position: relative;\n}\n.timeline > li > .timeline-item > .time {\n  color: #999;\n  float: right;\n  padding: 10px;\n  font-size: 12px;\n}\n.timeline > li > .timeline-item > .timeline-header {\n  margin: 0;\n  color: #555;\n  border-bottom: 1px solid #f4f4f4;\n  padding: 10px;\n  font-size: 16px;\n  line-height: 1.1;\n}\n.timeline > li > .timeline-item > .timeline-header > a {\n  font-weight: 600;\n}\n.timeline > li > .timeline-item > .timeline-body,\n.timeline > li > .timeline-item > .timeline-footer {\n  padding: 10px;\n}\n.timeline > li > .fa,\n.timeline > li > .glyphicon,\n.timeline > li > .ion {\n  width: 30px;\n  height: 30px;\n  font-size: 15px;\n  line-height: 30px;\n  position: absolute;\n  color: #666;\n  background: #d2d6de;\n  border-radius: 50%;\n  text-align: center;\n  left: 18px;\n  top: 0;\n}\n.timeline > .time-label > span {\n  font-weight: 600;\n  padding: 5px;\n  display: inline-block;\n  background-color: #fff;\n  border-radius: 4px;\n}\n.timeline-inverse > li > .timeline-item {\n  background: #f0f0f0;\n  border: 1px solid #ddd;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.timeline-inverse > li > .timeline-item > .timeline-header {\n  border-bottom-color: #ddd;\n}\n/*\n * Component: Button\n * -----------------\n */\n.btn {\n  border-radius: 3px;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n  border: 1px solid transparent;\n}\n.btn.uppercase {\n  text-transform: uppercase;\n}\n.btn.btn-flat {\n  border-radius: 0;\n  -webkit-box-shadow: none;\n  -moz-box-shadow: none;\n  box-shadow: none;\n  border-width: 1px;\n}\n.btn:active {\n  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n.btn:focus {\n  outline: none;\n}\n.btn.btn-file {\n  position: relative;\n  overflow: hidden;\n}\n.btn.btn-file > input[type='file'] {\n  position: absolute;\n  top: 0;\n  right: 0;\n  min-width: 100%;\n  min-height: 100%;\n  font-size: 100px;\n  text-align: right;\n  opacity: 0;\n  filter: alpha(opacity=0);\n  outline: none;\n  background: white;\n  cursor: inherit;\n  display: block;\n}\n.btn-default {\n  background-color: #f4f4f4;\n  color: #444;\n  border-color: #ddd;\n}\n.btn-default:hover,\n.btn-default:active,\n.btn-default.hover {\n  background-color: #e7e7e7;\n}\n.btn-primary {\n  background-color: #3c8dbc;\n  border-color: #367fa9;\n}\n.btn-primary:hover,\n.btn-primary:active,\n.btn-primary.hover {\n  background-color: #367fa9;\n}\n.btn-success {\n  background-color: #00a65a;\n  border-color: #008d4c;\n}\n.btn-success:hover,\n.btn-success:active,\n.btn-success.hover {\n  background-color: #008d4c;\n}\n.btn-info {\n  background-color: #00c0ef;\n  border-color: #00acd6;\n}\n.btn-info:hover,\n.btn-info:active,\n.btn-info.hover {\n  background-color: #00acd6;\n}\n.btn-danger {\n  background-color: #dd4b39;\n  border-color: #d73925;\n}\n.btn-danger:hover,\n.btn-danger:active,\n.btn-danger.hover {\n  background-color: #d73925;\n}\n.btn-warning {\n  background-color: #f39c12;\n  border-color: #e08e0b;\n}\n.btn-warning:hover,\n.btn-warning:active,\n.btn-warning.hover {\n  background-color: #e08e0b;\n}\n.btn-outline {\n  border: 1px solid #fff;\n  background: transparent;\n  color: #fff;\n}\n.btn-outline:hover,\n.btn-outline:focus,\n.btn-outline:active {\n  color: rgba(255, 255, 255, 0.7);\n  border-color: rgba(255, 255, 255, 0.7);\n}\n.btn-link {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.btn[class*='bg-']:hover {\n  -webkit-box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.2);\n  box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.2);\n}\n.btn-app {\n  border-radius: 3px;\n  position: relative;\n  padding: 15px 5px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  height: 60px;\n  text-align: center;\n  color: #666;\n  border: 1px solid #ddd;\n  background-color: #f4f4f4;\n  font-size: 12px;\n}\n.btn-app > .fa,\n.btn-app > .glyphicon,\n.btn-app > .ion {\n  font-size: 20px;\n  display: block;\n}\n.btn-app:hover {\n  background: #f4f4f4;\n  color: #444;\n  border-color: #aaa;\n}\n.btn-app:active,\n.btn-app:focus {\n  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n.btn-app > .badge {\n  position: absolute;\n  top: -3px;\n  right: -10px;\n  font-size: 10px;\n  font-weight: 400;\n}\n/*\n * Component: Callout\n * ------------------\n */\n.callout {\n  border-radius: 3px;\n  margin: 0 0 20px 0;\n  padding: 15px 30px 15px 15px;\n  border-left: 5px solid #eee;\n}\n.callout a {\n  color: #fff;\n  text-decoration: underline;\n}\n.callout a:hover {\n  color: #eee;\n}\n.callout h4 {\n  margin-top: 0;\n  font-weight: 600;\n}\n.callout p:last-child {\n  margin-bottom: 0;\n}\n.callout code,\n.callout .highlight {\n  background-color: #fff;\n}\n.callout.callout-danger {\n  border-color: #c23321;\n}\n.callout.callout-warning {\n  border-color: #c87f0a;\n}\n.callout.callout-info {\n  border-color: #0097bc;\n}\n.callout.callout-success {\n  border-color: #00733e;\n}\n/*\n * Component: alert\n * ----------------\n */\n.alert {\n  border-radius: 3px;\n}\n.alert h4 {\n  font-weight: 600;\n}\n.alert .icon {\n  margin-right: 10px;\n}\n.alert .close {\n  color: #000;\n  opacity: 0.2;\n  filter: alpha(opacity=20);\n}\n.alert .close:hover {\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n}\n.alert a {\n  color: #fff;\n  text-decoration: underline;\n}\n.alert-success {\n  border-color: #008d4c;\n}\n.alert-danger,\n.alert-error {\n  border-color: #d73925;\n}\n.alert-warning {\n  border-color: #e08e0b;\n}\n.alert-info {\n  border-color: #00acd6;\n}\n/*\n * Component: Nav\n * --------------\n */\n.nav > li > a:hover,\n.nav > li > a:active,\n.nav > li > a:focus {\n  color: #444;\n  background: #f7f7f7;\n}\n/* NAV PILLS */\n.nav-pills > li > a {\n  border-radius: 0;\n  border-top: 3px solid transparent;\n  color: #444;\n}\n.nav-pills > li > a > .fa,\n.nav-pills > li > a > .glyphicon,\n.nav-pills > li > a > .ion {\n  margin-right: 5px;\n}\n.nav-pills > li.active > a,\n.nav-pills > li.active > a:hover,\n.nav-pills > li.active > a:focus {\n  border-top-color: #3c8dbc;\n}\n.nav-pills > li.active > a {\n  font-weight: 600;\n}\n/* NAV STACKED */\n.nav-stacked > li > a {\n  border-radius: 0;\n  border-top: 0;\n  border-left: 3px solid transparent;\n  color: #444;\n}\n.nav-stacked > li.active > a,\n.nav-stacked > li.active > a:hover {\n  background: transparent;\n  color: #444;\n  border-top: 0;\n  border-left-color: #3c8dbc;\n}\n.nav-stacked > li.header {\n  border-bottom: 1px solid #ddd;\n  color: #777;\n  margin-bottom: 10px;\n  padding: 5px 10px;\n  text-transform: uppercase;\n}\n/* NAV TABS */\n.nav-tabs-custom {\n  margin-bottom: 20px;\n  background: #fff;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  border-radius: 3px;\n}\n.nav-tabs-custom > .nav-tabs {\n  margin: 0;\n  border-bottom-color: #f4f4f4;\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n.nav-tabs-custom > .nav-tabs > li {\n  border-top: 3px solid transparent;\n  margin-bottom: -2px;\n  margin-right: 5px;\n}\n.nav-tabs-custom > .nav-tabs > li > a {\n  color: #444;\n  border-radius: 0;\n}\n.nav-tabs-custom > .nav-tabs > li > a.text-muted {\n  color: #999;\n}\n.nav-tabs-custom > .nav-tabs > li > a,\n.nav-tabs-custom > .nav-tabs > li > a:hover {\n  background: transparent;\n  margin: 0;\n}\n.nav-tabs-custom > .nav-tabs > li > a:hover {\n  color: #999;\n}\n.nav-tabs-custom > .nav-tabs > li:not(.active) > a:hover,\n.nav-tabs-custom > .nav-tabs > li:not(.active) > a:focus,\n.nav-tabs-custom > .nav-tabs > li:not(.active) > a:active {\n  border-color: transparent;\n}\n.nav-tabs-custom > .nav-tabs > li.active {\n  border-top-color: #3c8dbc;\n}\n.nav-tabs-custom > .nav-tabs > li.active > a,\n.nav-tabs-custom > .nav-tabs > li.active:hover > a {\n  background-color: #fff;\n  color: #444;\n}\n.nav-tabs-custom > .nav-tabs > li.active > a {\n  border-top-color: transparent;\n  border-left-color: #f4f4f4;\n  border-right-color: #f4f4f4;\n}\n.nav-tabs-custom > .nav-tabs > li:first-of-type {\n  margin-left: 0;\n}\n.nav-tabs-custom > .nav-tabs > li:first-of-type.active > a {\n  border-left-color: transparent;\n}\n.nav-tabs-custom > .nav-tabs.pull-right {\n  float: none !important;\n}\n.nav-tabs-custom > .nav-tabs.pull-right > li {\n  float: right;\n}\n.nav-tabs-custom > .nav-tabs.pull-right > li:first-of-type {\n  margin-right: 0;\n}\n.nav-tabs-custom > .nav-tabs.pull-right > li:first-of-type > a {\n  border-left-width: 1px;\n}\n.nav-tabs-custom > .nav-tabs.pull-right > li:first-of-type.active > a {\n  border-left-color: #f4f4f4;\n  border-right-color: transparent;\n}\n.nav-tabs-custom > .nav-tabs > li.header {\n  line-height: 35px;\n  padding: 0 10px;\n  font-size: 20px;\n  color: #444;\n}\n.nav-tabs-custom > .nav-tabs > li.header > .fa,\n.nav-tabs-custom > .nav-tabs > li.header > .glyphicon,\n.nav-tabs-custom > .nav-tabs > li.header > .ion {\n  margin-right: 5px;\n}\n.nav-tabs-custom > .tab-content {\n  background: #fff;\n  padding: 10px;\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 3px;\n}\n.nav-tabs-custom .dropdown.open > a:active,\n.nav-tabs-custom .dropdown.open > a:focus {\n  background: transparent;\n  color: #999;\n}\n.nav-tabs-custom.tab-primary > .nav-tabs > li.active {\n  border-top-color: #3c8dbc;\n}\n.nav-tabs-custom.tab-info > .nav-tabs > li.active {\n  border-top-color: #00c0ef;\n}\n.nav-tabs-custom.tab-danger > .nav-tabs > li.active {\n  border-top-color: #dd4b39;\n}\n.nav-tabs-custom.tab-warning > .nav-tabs > li.active {\n  border-top-color: #f39c12;\n}\n.nav-tabs-custom.tab-success > .nav-tabs > li.active {\n  border-top-color: #00a65a;\n}\n.nav-tabs-custom.tab-default > .nav-tabs > li.active {\n  border-top-color: #d2d6de;\n}\n/* PAGINATION */\n.pagination > li > a {\n  background: #fafafa;\n  color: #666;\n}\n.pagination.pagination-flat > li > a {\n  border-radius: 0 !important;\n}\n/*\n * Component: Products List\n * ------------------------\n */\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.products-list > .item {\n  border-radius: 3px;\n  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  padding: 10px 0;\n  background: #fff;\n}\n.products-list > .item:before,\n.products-list > .item:after {\n  content: \" \";\n  display: table;\n}\n.products-list > .item:after {\n  clear: both;\n}\n.products-list .product-img {\n  float: left;\n}\n.products-list .product-img img {\n  width: 50px;\n  height: 50px;\n}\n.products-list .product-info {\n  margin-left: 60px;\n}\n.products-list .product-title {\n  font-weight: 600;\n}\n.products-list .product-description {\n  display: block;\n  color: #999;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n.product-list-in-box > .item {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n  border-radius: 0;\n  border-bottom: 1px solid #f4f4f4;\n}\n.product-list-in-box > .item:last-of-type {\n  border-bottom-width: 0;\n}\n/*\n * Component: Table\n * ----------------\n */\n.table > thead > tr > th,\n.table > tbody > tr > th,\n.table > tfoot > tr > th,\n.table > thead > tr > td,\n.table > tbody > tr > td,\n.table > tfoot > tr > td {\n  border-top: 1px solid #f4f4f4;\n}\n.table > thead > tr > th {\n  border-bottom: 2px solid #f4f4f4;\n}\n.table tr td .progress {\n  margin-top: 5px;\n}\n.table-bordered {\n  border: 1px solid #f4f4f4;\n}\n.table-bordered > thead > tr > th,\n.table-bordered > tbody > tr > th,\n.table-bordered > tfoot > tr > th,\n.table-bordered > thead > tr > td,\n.table-bordered > tbody > tr > td,\n.table-bordered > tfoot > tr > td {\n  border: 1px solid #f4f4f4;\n}\n.table-bordered > thead > tr > th,\n.table-bordered > thead > tr > td {\n  border-bottom-width: 2px;\n}\n.table.no-border,\n.table.no-border td,\n.table.no-border th {\n  border: 0;\n}\n/* .text-center in tables */\ntable.text-center,\ntable.text-center td,\ntable.text-center th {\n  text-align: center;\n}\n.table.align th {\n  text-align: left;\n}\n.table.align td {\n  text-align: right;\n}\n/*\n * Component: Label\n * ----------------\n */\n.label-default {\n  background-color: #d2d6de;\n  color: #444;\n}\n/*\n * Component: Direct Chat\n * ----------------------\n */\n.direct-chat .box-body {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  position: relative;\n  overflow-x: hidden;\n  padding: 0;\n}\n.direct-chat.chat-pane-open .direct-chat-contacts {\n  -webkit-transform: translate(0, 0);\n  -ms-transform: translate(0, 0);\n  -o-transform: translate(0, 0);\n  transform: translate(0, 0);\n}\n.direct-chat-messages {\n  -webkit-transform: translate(0, 0);\n  -ms-transform: translate(0, 0);\n  -o-transform: translate(0, 0);\n  transform: translate(0, 0);\n  padding: 10px;\n  height: 250px;\n  overflow: auto;\n}\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n.direct-chat-msg {\n  margin-bottom: 10px;\n}\n.direct-chat-msg:before,\n.direct-chat-msg:after {\n  content: \" \";\n  display: table;\n}\n.direct-chat-msg:after {\n  clear: both;\n}\n.direct-chat-messages,\n.direct-chat-contacts {\n  -webkit-transition: -webkit-transform 0.5s ease-in-out;\n  -moz-transition: -moz-transform 0.5s ease-in-out;\n  -o-transition: -o-transform 0.5s ease-in-out;\n  transition: transform 0.5s ease-in-out;\n}\n.direct-chat-text {\n  border-radius: 5px;\n  position: relative;\n  padding: 5px 10px;\n  background: #d2d6de;\n  border: 1px solid #d2d6de;\n  margin: 5px 0 0 50px;\n  color: #444;\n}\n.direct-chat-text:after,\n.direct-chat-text:before {\n  position: absolute;\n  right: 100%;\n  top: 15px;\n  border: solid transparent;\n  border-right-color: #d2d6de;\n  content: ' ';\n  height: 0;\n  width: 0;\n  pointer-events: none;\n}\n.direct-chat-text:after {\n  border-width: 5px;\n  margin-top: -5px;\n}\n.direct-chat-text:before {\n  border-width: 6px;\n  margin-top: -6px;\n}\n.right .direct-chat-text {\n  margin-right: 50px;\n  margin-left: 0;\n}\n.right .direct-chat-text:after,\n.right .direct-chat-text:before {\n  right: auto;\n  left: 100%;\n  border-right-color: transparent;\n  border-left-color: #d2d6de;\n}\n.direct-chat-img {\n  border-radius: 50%;\n  float: left;\n  width: 40px;\n  height: 40px;\n}\n.right .direct-chat-img {\n  float: right;\n}\n.direct-chat-info {\n  display: block;\n  margin-bottom: 2px;\n  font-size: 12px;\n}\n.direct-chat-name {\n  font-weight: 600;\n}\n.direct-chat-timestamp {\n  color: #999;\n}\n.direct-chat-contacts-open .direct-chat-contacts {\n  -webkit-transform: translate(0, 0);\n  -ms-transform: translate(0, 0);\n  -o-transform: translate(0, 0);\n  transform: translate(0, 0);\n}\n.direct-chat-contacts {\n  -webkit-transform: translate(101%, 0);\n  -ms-transform: translate(101%, 0);\n  -o-transform: translate(101%, 0);\n  transform: translate(101%, 0);\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  height: 250px;\n  width: 100%;\n  background: #222d32;\n  color: #fff;\n  overflow: auto;\n}\n.contacts-list > li {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.2);\n  padding: 10px;\n  margin: 0;\n}\n.contacts-list > li:before,\n.contacts-list > li:after {\n  content: \" \";\n  display: table;\n}\n.contacts-list > li:after {\n  clear: both;\n}\n.contacts-list > li:last-of-type {\n  border-bottom: none;\n}\n.contacts-list-img {\n  border-radius: 50%;\n  width: 40px;\n  float: left;\n}\n.contacts-list-info {\n  margin-left: 45px;\n  color: #fff;\n}\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n.contacts-list-name {\n  font-weight: 600;\n}\n.contacts-list-status {\n  font-size: 12px;\n}\n.contacts-list-date {\n  color: #aaa;\n  font-weight: normal;\n}\n.contacts-list-msg {\n  color: #999;\n}\n.direct-chat-danger .right > .direct-chat-text {\n  background: #dd4b39;\n  border-color: #dd4b39;\n  color: #fff;\n}\n.direct-chat-danger .right > .direct-chat-text:after,\n.direct-chat-danger .right > .direct-chat-text:before {\n  border-left-color: #dd4b39;\n}\n.direct-chat-primary .right > .direct-chat-text {\n  background: #3c8dbc;\n  border-color: #3c8dbc;\n  color: #fff;\n}\n.direct-chat-primary .right > .direct-chat-text:after,\n.direct-chat-primary .right > .direct-chat-text:before {\n  border-left-color: #3c8dbc;\n}\n.direct-chat-warning .right > .direct-chat-text {\n  background: #f39c12;\n  border-color: #f39c12;\n  color: #fff;\n}\n.direct-chat-warning .right > .direct-chat-text:after,\n.direct-chat-warning .right > .direct-chat-text:before {\n  border-left-color: #f39c12;\n}\n.direct-chat-info .right > .direct-chat-text {\n  background: #00c0ef;\n  border-color: #00c0ef;\n  color: #fff;\n}\n.direct-chat-info .right > .direct-chat-text:after,\n.direct-chat-info .right > .direct-chat-text:before {\n  border-left-color: #00c0ef;\n}\n.direct-chat-success .right > .direct-chat-text {\n  background: #00a65a;\n  border-color: #00a65a;\n  color: #fff;\n}\n.direct-chat-success .right > .direct-chat-text:after,\n.direct-chat-success .right > .direct-chat-text:before {\n  border-left-color: #00a65a;\n}\n/*\n * Component: Users List\n * ---------------------\n */\n.users-list > li {\n  width: 25%;\n  float: left;\n  padding: 10px;\n  text-align: center;\n}\n.users-list > li img {\n  border-radius: 50%;\n  max-width: 100%;\n  height: auto;\n}\n.users-list > li > a:hover,\n.users-list > li > a:hover .users-list-name {\n  color: #999;\n}\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n.users-list-name {\n  font-weight: 600;\n  color: #444;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n.users-list-date {\n  color: #999;\n  font-size: 12px;\n}\n/*\n * Component: Carousel\n * -------------------\n */\n.carousel-control.left,\n.carousel-control.right {\n  background-image: none;\n}\n.carousel-control > .fa {\n  font-size: 40px;\n  position: absolute;\n  top: 50%;\n  z-index: 5;\n  display: inline-block;\n  margin-top: -20px;\n}\n/*\n * Component: modal\n * ----------------\n */\n.modal {\n  background: rgba(0, 0, 0, 0.3);\n}\n.modal-content {\n  border-radius: 0;\n  -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.125);\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.125);\n  border: 0;\n}\n@media (min-width: 768px) {\n  .modal-content {\n    -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.125);\n    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.125);\n  }\n}\n.modal-header {\n  border-bottom-color: #f4f4f4;\n}\n.modal-footer {\n  border-top-color: #f4f4f4;\n}\n.modal-primary .modal-header,\n.modal-primary .modal-footer {\n  border-color: #307095;\n}\n.modal-warning .modal-header,\n.modal-warning .modal-footer {\n  border-color: #c87f0a;\n}\n.modal-info .modal-header,\n.modal-info .modal-footer {\n  border-color: #0097bc;\n}\n.modal-success .modal-header,\n.modal-success .modal-footer {\n  border-color: #00733e;\n}\n.modal-danger .modal-header,\n.modal-danger .modal-footer {\n  border-color: #c23321;\n}\n/*\n * Component: Social Widgets\n * -------------------------\n */\n.box-widget {\n  border: none;\n  position: relative;\n}\n.widget-user .widget-user-header {\n  padding: 20px;\n  height: 120px;\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n.widget-user .widget-user-username {\n  margin-top: 0;\n  margin-bottom: 5px;\n  font-size: 25px;\n  font-weight: 300;\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);\n}\n.widget-user .widget-user-desc {\n  margin-top: 0;\n}\n.widget-user .widget-user-image {\n  position: absolute;\n  top: 65px;\n  left: 50%;\n  margin-left: -45px;\n}\n.widget-user .widget-user-image > img {\n  width: 90px;\n  height: auto;\n  border: 3px solid #fff;\n}\n.widget-user .box-footer {\n  padding-top: 30px;\n}\n.widget-user-2 .widget-user-header {\n  padding: 20px;\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n.widget-user-2 .widget-user-username {\n  margin-top: 5px;\n  margin-bottom: 5px;\n  font-size: 25px;\n  font-weight: 300;\n}\n.widget-user-2 .widget-user-desc {\n  margin-top: 0;\n}\n.widget-user-2 .widget-user-username,\n.widget-user-2 .widget-user-desc {\n  margin-left: 75px;\n}\n.widget-user-2 .widget-user-image > img {\n  width: 65px;\n  height: auto;\n  float: left;\n}\n/*\n * Page: Mailbox\n * -------------\n */\n.mailbox-messages > .table {\n  margin: 0;\n}\n.mailbox-controls {\n  padding: 5px;\n}\n.mailbox-controls.with-border {\n  border-bottom: 1px solid #f4f4f4;\n}\n.mailbox-read-info {\n  border-bottom: 1px solid #f4f4f4;\n  padding: 10px;\n}\n.mailbox-read-info h3 {\n  font-size: 20px;\n  margin: 0;\n}\n.mailbox-read-info h5 {\n  margin: 0;\n  padding: 5px 0 0 0;\n}\n.mailbox-read-time {\n  color: #999;\n  font-size: 13px;\n}\n.mailbox-read-message {\n  padding: 10px;\n}\n.mailbox-attachments li {\n  float: left;\n  width: 200px;\n  border: 1px solid #eee;\n  margin-bottom: 10px;\n  margin-right: 10px;\n}\n.mailbox-attachment-name {\n  font-weight: bold;\n  color: #666;\n}\n.mailbox-attachment-icon,\n.mailbox-attachment-info,\n.mailbox-attachment-size {\n  display: block;\n}\n.mailbox-attachment-info {\n  padding: 10px;\n  background: #f4f4f4;\n}\n.mailbox-attachment-size {\n  color: #999;\n  font-size: 12px;\n}\n.mailbox-attachment-icon {\n  text-align: center;\n  font-size: 65px;\n  color: #666;\n  padding: 20px 10px;\n}\n.mailbox-attachment-icon.has-img {\n  padding: 0;\n}\n.mailbox-attachment-icon.has-img > img {\n  max-width: 100%;\n  height: auto;\n}\n/*\n * Page: Lock Screen\n * -----------------\n */\n/* ADD THIS CLASS TO THE <BODY> TAG */\n.lockscreen {\n  background: #d2d6de;\n}\n.lockscreen-logo {\n  font-size: 35px;\n  text-align: center;\n  margin-bottom: 25px;\n  font-weight: 300;\n}\n.lockscreen-logo a {\n  color: #444;\n}\n.lockscreen-wrapper {\n  max-width: 400px;\n  margin: 0 auto;\n  margin-top: 10%;\n}\n/* User name [optional] */\n.lockscreen .lockscreen-name {\n  text-align: center;\n  font-weight: 600;\n}\n/* Will contain the image and the sign in form */\n.lockscreen-item {\n  border-radius: 4px;\n  padding: 0;\n  background: #fff;\n  position: relative;\n  margin: 10px auto 30px auto;\n  width: 290px;\n}\n/* User image */\n.lockscreen-image {\n  border-radius: 50%;\n  position: absolute;\n  left: -10px;\n  top: -25px;\n  background: #fff;\n  padding: 5px;\n  z-index: 10;\n}\n.lockscreen-image > img {\n  border-radius: 50%;\n  width: 70px;\n  height: 70px;\n}\n/* Contains the password input and the login button */\n.lockscreen-credentials {\n  margin-left: 70px;\n}\n.lockscreen-credentials .form-control {\n  border: 0;\n}\n.lockscreen-credentials .btn {\n  background-color: #fff;\n  border: 0;\n  padding: 0 10px;\n}\n.lockscreen-footer {\n  margin-top: 10px;\n}\n/*\n * Page: Login & Register\n * ----------------------\n */\n.login-logo,\n.register-logo {\n  font-size: 35px;\n  text-align: center;\n  margin-bottom: 25px;\n  font-weight: 300;\n}\n.login-logo a,\n.register-logo a {\n  color: #444;\n}\n.login-page,\n.register-page {\n  background: #d2d6de;\n}\n.login-box,\n.register-box {\n  width: 360px;\n  margin: 7% auto;\n}\n@media (max-width: 768px) {\n  .login-box,\n  .register-box {\n    width: 90%;\n    margin-top: 20px;\n  }\n}\n.login-box-body,\n.register-box-body {\n  background: #fff;\n  padding: 20px;\n  border-top: 0;\n  color: #666;\n}\n.login-box-body .form-control-feedback,\n.register-box-body .form-control-feedback {\n  color: #777;\n}\n.login-box-msg,\n.register-box-msg {\n  margin: 0;\n  text-align: center;\n  padding: 0 20px 20px 20px;\n}\n.social-auth-links {\n  margin: 10px 0;\n}\n/*\n * Page: 400 and 500 error pages\n * ------------------------------\n */\n.error-page {\n  width: 600px;\n  margin: 20px auto 0 auto;\n}\n@media (max-width: 991px) {\n  .error-page {\n    width: 100%;\n  }\n}\n.error-page > .headline {\n  float: left;\n  font-size: 100px;\n  font-weight: 300;\n}\n@media (max-width: 991px) {\n  .error-page > .headline {\n    float: none;\n    text-align: center;\n  }\n}\n.error-page > .error-content {\n  margin-left: 190px;\n  display: block;\n}\n@media (max-width: 991px) {\n  .error-page > .error-content {\n    margin-left: 0;\n  }\n}\n.error-page > .error-content > h3 {\n  font-weight: 300;\n  font-size: 25px;\n}\n@media (max-width: 991px) {\n  .error-page > .error-content > h3 {\n    text-align: center;\n  }\n}\n/*\n * Page: Invoice\n * -------------\n */\n.invoice {\n  position: relative;\n  background: #fff;\n  border: 1px solid #f4f4f4;\n  padding: 20px;\n  margin: 10px 25px;\n}\n.invoice-title {\n  margin-top: 0;\n}\n/*\n * Page: Profile\n * -------------\n */\n.profile-user-img {\n  margin: 0 auto;\n  width: 100px;\n  padding: 3px;\n  border: 3px solid #d2d6de;\n}\n.profile-username {\n  font-size: 21px;\n  margin-top: 5px;\n}\n.post {\n  border-bottom: 1px solid #d2d6de;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  color: #666;\n}\n.post:last-of-type {\n  border-bottom: 0;\n  margin-bottom: 0;\n  padding-bottom: 0;\n}\n.post .user-block {\n  margin-bottom: 15px;\n}\n/*\n * Social Buttons for Bootstrap\n *\n * Copyright 2013-2015 Panayiotis Lipiridis\n * Licensed under the MIT License\n *\n * https://github.com/lipis/bootstrap-social\n */\n.btn-social {\n  position: relative;\n  padding-left: 44px;\n  text-align: left;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.btn-social > :first-child {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 32px;\n  line-height: 34px;\n  font-size: 1.6em;\n  text-align: center;\n  border-right: 1px solid rgba(0, 0, 0, 0.2);\n}\n.btn-social.btn-lg {\n  padding-left: 61px;\n}\n.btn-social.btn-lg > :first-child {\n  line-height: 45px;\n  width: 45px;\n  font-size: 1.8em;\n}\n.btn-social.btn-sm {\n  padding-left: 38px;\n}\n.btn-social.btn-sm > :first-child {\n  line-height: 28px;\n  width: 28px;\n  font-size: 1.4em;\n}\n.btn-social.btn-xs {\n  padding-left: 30px;\n}\n.btn-social.btn-xs > :first-child {\n  line-height: 20px;\n  width: 20px;\n  font-size: 1.2em;\n}\n.btn-social-icon {\n  position: relative;\n  padding-left: 44px;\n  text-align: left;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  height: 34px;\n  width: 34px;\n  padding: 0;\n}\n.btn-social-icon > :first-child {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 32px;\n  line-height: 34px;\n  font-size: 1.6em;\n  text-align: center;\n  border-right: 1px solid rgba(0, 0, 0, 0.2);\n}\n.btn-social-icon.btn-lg {\n  padding-left: 61px;\n}\n.btn-social-icon.btn-lg > :first-child {\n  line-height: 45px;\n  width: 45px;\n  font-size: 1.8em;\n}\n.btn-social-icon.btn-sm {\n  padding-left: 38px;\n}\n.btn-social-icon.btn-sm > :first-child {\n  line-height: 28px;\n  width: 28px;\n  font-size: 1.4em;\n}\n.btn-social-icon.btn-xs {\n  padding-left: 30px;\n}\n.btn-social-icon.btn-xs > :first-child {\n  line-height: 20px;\n  width: 20px;\n  font-size: 1.2em;\n}\n.btn-social-icon > :first-child {\n  border: none;\n  text-align: center;\n  width: 100%;\n}\n.btn-social-icon.btn-lg {\n  height: 45px;\n  width: 45px;\n  padding-left: 0;\n  padding-right: 0;\n}\n.btn-social-icon.btn-sm {\n  height: 30px;\n  width: 30px;\n  padding-left: 0;\n  padding-right: 0;\n}\n.btn-social-icon.btn-xs {\n  height: 22px;\n  width: 22px;\n  padding-left: 0;\n  padding-right: 0;\n}\n.btn-adn {\n  color: #fff;\n  background-color: #d87a68;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-adn:focus,\n.btn-adn.focus {\n  color: #fff;\n  background-color: #ce563f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-adn:hover {\n  color: #fff;\n  background-color: #ce563f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-adn:active,\n.btn-adn.active,\n.open > .dropdown-toggle.btn-adn {\n  color: #fff;\n  background-color: #ce563f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-adn:active:hover,\n.btn-adn.active:hover,\n.open > .dropdown-toggle.btn-adn:hover,\n.btn-adn:active:focus,\n.btn-adn.active:focus,\n.open > .dropdown-toggle.btn-adn:focus,\n.btn-adn:active.focus,\n.btn-adn.active.focus,\n.open > .dropdown-toggle.btn-adn.focus {\n  color: #fff;\n  background-color: #b94630;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-adn:active,\n.btn-adn.active,\n.open > .dropdown-toggle.btn-adn {\n  background-image: none;\n}\n.btn-adn.disabled,\n.btn-adn[disabled],\nfieldset[disabled] .btn-adn,\n.btn-adn.disabled:hover,\n.btn-adn[disabled]:hover,\nfieldset[disabled] .btn-adn:hover,\n.btn-adn.disabled:focus,\n.btn-adn[disabled]:focus,\nfieldset[disabled] .btn-adn:focus,\n.btn-adn.disabled.focus,\n.btn-adn[disabled].focus,\nfieldset[disabled] .btn-adn.focus,\n.btn-adn.disabled:active,\n.btn-adn[disabled]:active,\nfieldset[disabled] .btn-adn:active,\n.btn-adn.disabled.active,\n.btn-adn[disabled].active,\nfieldset[disabled] .btn-adn.active {\n  background-color: #d87a68;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-adn .badge {\n  color: #d87a68;\n  background-color: #fff;\n}\n.btn-bitbucket {\n  color: #fff;\n  background-color: #205081;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-bitbucket:focus,\n.btn-bitbucket.focus {\n  color: #fff;\n  background-color: #163758;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-bitbucket:hover {\n  color: #fff;\n  background-color: #163758;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-bitbucket:active,\n.btn-bitbucket.active,\n.open > .dropdown-toggle.btn-bitbucket {\n  color: #fff;\n  background-color: #163758;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-bitbucket:active:hover,\n.btn-bitbucket.active:hover,\n.open > .dropdown-toggle.btn-bitbucket:hover,\n.btn-bitbucket:active:focus,\n.btn-bitbucket.active:focus,\n.open > .dropdown-toggle.btn-bitbucket:focus,\n.btn-bitbucket:active.focus,\n.btn-bitbucket.active.focus,\n.open > .dropdown-toggle.btn-bitbucket.focus {\n  color: #fff;\n  background-color: #0f253c;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-bitbucket:active,\n.btn-bitbucket.active,\n.open > .dropdown-toggle.btn-bitbucket {\n  background-image: none;\n}\n.btn-bitbucket.disabled,\n.btn-bitbucket[disabled],\nfieldset[disabled] .btn-bitbucket,\n.btn-bitbucket.disabled:hover,\n.btn-bitbucket[disabled]:hover,\nfieldset[disabled] .btn-bitbucket:hover,\n.btn-bitbucket.disabled:focus,\n.btn-bitbucket[disabled]:focus,\nfieldset[disabled] .btn-bitbucket:focus,\n.btn-bitbucket.disabled.focus,\n.btn-bitbucket[disabled].focus,\nfieldset[disabled] .btn-bitbucket.focus,\n.btn-bitbucket.disabled:active,\n.btn-bitbucket[disabled]:active,\nfieldset[disabled] .btn-bitbucket:active,\n.btn-bitbucket.disabled.active,\n.btn-bitbucket[disabled].active,\nfieldset[disabled] .btn-bitbucket.active {\n  background-color: #205081;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-bitbucket .badge {\n  color: #205081;\n  background-color: #fff;\n}\n.btn-dropbox {\n  color: #fff;\n  background-color: #1087dd;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-dropbox:focus,\n.btn-dropbox.focus {\n  color: #fff;\n  background-color: #0d6aad;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-dropbox:hover {\n  color: #fff;\n  background-color: #0d6aad;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-dropbox:active,\n.btn-dropbox.active,\n.open > .dropdown-toggle.btn-dropbox {\n  color: #fff;\n  background-color: #0d6aad;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-dropbox:active:hover,\n.btn-dropbox.active:hover,\n.open > .dropdown-toggle.btn-dropbox:hover,\n.btn-dropbox:active:focus,\n.btn-dropbox.active:focus,\n.open > .dropdown-toggle.btn-dropbox:focus,\n.btn-dropbox:active.focus,\n.btn-dropbox.active.focus,\n.open > .dropdown-toggle.btn-dropbox.focus {\n  color: #fff;\n  background-color: #0a568c;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-dropbox:active,\n.btn-dropbox.active,\n.open > .dropdown-toggle.btn-dropbox {\n  background-image: none;\n}\n.btn-dropbox.disabled,\n.btn-dropbox[disabled],\nfieldset[disabled] .btn-dropbox,\n.btn-dropbox.disabled:hover,\n.btn-dropbox[disabled]:hover,\nfieldset[disabled] .btn-dropbox:hover,\n.btn-dropbox.disabled:focus,\n.btn-dropbox[disabled]:focus,\nfieldset[disabled] .btn-dropbox:focus,\n.btn-dropbox.disabled.focus,\n.btn-dropbox[disabled].focus,\nfieldset[disabled] .btn-dropbox.focus,\n.btn-dropbox.disabled:active,\n.btn-dropbox[disabled]:active,\nfieldset[disabled] .btn-dropbox:active,\n.btn-dropbox.disabled.active,\n.btn-dropbox[disabled].active,\nfieldset[disabled] .btn-dropbox.active {\n  background-color: #1087dd;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-dropbox .badge {\n  color: #1087dd;\n  background-color: #fff;\n}\n.btn-facebook {\n  color: #fff;\n  background-color: #3b5998;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-facebook:focus,\n.btn-facebook.focus {\n  color: #fff;\n  background-color: #2d4373;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-facebook:hover {\n  color: #fff;\n  background-color: #2d4373;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-facebook:active,\n.btn-facebook.active,\n.open > .dropdown-toggle.btn-facebook {\n  color: #fff;\n  background-color: #2d4373;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-facebook:active:hover,\n.btn-facebook.active:hover,\n.open > .dropdown-toggle.btn-facebook:hover,\n.btn-facebook:active:focus,\n.btn-facebook.active:focus,\n.open > .dropdown-toggle.btn-facebook:focus,\n.btn-facebook:active.focus,\n.btn-facebook.active.focus,\n.open > .dropdown-toggle.btn-facebook.focus {\n  color: #fff;\n  background-color: #23345a;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-facebook:active,\n.btn-facebook.active,\n.open > .dropdown-toggle.btn-facebook {\n  background-image: none;\n}\n.btn-facebook.disabled,\n.btn-facebook[disabled],\nfieldset[disabled] .btn-facebook,\n.btn-facebook.disabled:hover,\n.btn-facebook[disabled]:hover,\nfieldset[disabled] .btn-facebook:hover,\n.btn-facebook.disabled:focus,\n.btn-facebook[disabled]:focus,\nfieldset[disabled] .btn-facebook:focus,\n.btn-facebook.disabled.focus,\n.btn-facebook[disabled].focus,\nfieldset[disabled] .btn-facebook.focus,\n.btn-facebook.disabled:active,\n.btn-facebook[disabled]:active,\nfieldset[disabled] .btn-facebook:active,\n.btn-facebook.disabled.active,\n.btn-facebook[disabled].active,\nfieldset[disabled] .btn-facebook.active {\n  background-color: #3b5998;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-facebook .badge {\n  color: #3b5998;\n  background-color: #fff;\n}\n.btn-flickr {\n  color: #fff;\n  background-color: #ff0084;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-flickr:focus,\n.btn-flickr.focus {\n  color: #fff;\n  background-color: #cc006a;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-flickr:hover {\n  color: #fff;\n  background-color: #cc006a;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-flickr:active,\n.btn-flickr.active,\n.open > .dropdown-toggle.btn-flickr {\n  color: #fff;\n  background-color: #cc006a;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-flickr:active:hover,\n.btn-flickr.active:hover,\n.open > .dropdown-toggle.btn-flickr:hover,\n.btn-flickr:active:focus,\n.btn-flickr.active:focus,\n.open > .dropdown-toggle.btn-flickr:focus,\n.btn-flickr:active.focus,\n.btn-flickr.active.focus,\n.open > .dropdown-toggle.btn-flickr.focus {\n  color: #fff;\n  background-color: #a80057;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-flickr:active,\n.btn-flickr.active,\n.open > .dropdown-toggle.btn-flickr {\n  background-image: none;\n}\n.btn-flickr.disabled,\n.btn-flickr[disabled],\nfieldset[disabled] .btn-flickr,\n.btn-flickr.disabled:hover,\n.btn-flickr[disabled]:hover,\nfieldset[disabled] .btn-flickr:hover,\n.btn-flickr.disabled:focus,\n.btn-flickr[disabled]:focus,\nfieldset[disabled] .btn-flickr:focus,\n.btn-flickr.disabled.focus,\n.btn-flickr[disabled].focus,\nfieldset[disabled] .btn-flickr.focus,\n.btn-flickr.disabled:active,\n.btn-flickr[disabled]:active,\nfieldset[disabled] .btn-flickr:active,\n.btn-flickr.disabled.active,\n.btn-flickr[disabled].active,\nfieldset[disabled] .btn-flickr.active {\n  background-color: #ff0084;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-flickr .badge {\n  color: #ff0084;\n  background-color: #fff;\n}\n.btn-foursquare {\n  color: #fff;\n  background-color: #f94877;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-foursquare:focus,\n.btn-foursquare.focus {\n  color: #fff;\n  background-color: #f71752;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-foursquare:hover {\n  color: #fff;\n  background-color: #f71752;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-foursquare:active,\n.btn-foursquare.active,\n.open > .dropdown-toggle.btn-foursquare {\n  color: #fff;\n  background-color: #f71752;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-foursquare:active:hover,\n.btn-foursquare.active:hover,\n.open > .dropdown-toggle.btn-foursquare:hover,\n.btn-foursquare:active:focus,\n.btn-foursquare.active:focus,\n.open > .dropdown-toggle.btn-foursquare:focus,\n.btn-foursquare:active.focus,\n.btn-foursquare.active.focus,\n.open > .dropdown-toggle.btn-foursquare.focus {\n  color: #fff;\n  background-color: #e30742;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-foursquare:active,\n.btn-foursquare.active,\n.open > .dropdown-toggle.btn-foursquare {\n  background-image: none;\n}\n.btn-foursquare.disabled,\n.btn-foursquare[disabled],\nfieldset[disabled] .btn-foursquare,\n.btn-foursquare.disabled:hover,\n.btn-foursquare[disabled]:hover,\nfieldset[disabled] .btn-foursquare:hover,\n.btn-foursquare.disabled:focus,\n.btn-foursquare[disabled]:focus,\nfieldset[disabled] .btn-foursquare:focus,\n.btn-foursquare.disabled.focus,\n.btn-foursquare[disabled].focus,\nfieldset[disabled] .btn-foursquare.focus,\n.btn-foursquare.disabled:active,\n.btn-foursquare[disabled]:active,\nfieldset[disabled] .btn-foursquare:active,\n.btn-foursquare.disabled.active,\n.btn-foursquare[disabled].active,\nfieldset[disabled] .btn-foursquare.active {\n  background-color: #f94877;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-foursquare .badge {\n  color: #f94877;\n  background-color: #fff;\n}\n.btn-github {\n  color: #fff;\n  background-color: #444444;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-github:focus,\n.btn-github.focus {\n  color: #fff;\n  background-color: #2b2b2b;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-github:hover {\n  color: #fff;\n  background-color: #2b2b2b;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-github:active,\n.btn-github.active,\n.open > .dropdown-toggle.btn-github {\n  color: #fff;\n  background-color: #2b2b2b;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-github:active:hover,\n.btn-github.active:hover,\n.open > .dropdown-toggle.btn-github:hover,\n.btn-github:active:focus,\n.btn-github.active:focus,\n.open > .dropdown-toggle.btn-github:focus,\n.btn-github:active.focus,\n.btn-github.active.focus,\n.open > .dropdown-toggle.btn-github.focus {\n  color: #fff;\n  background-color: #191919;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-github:active,\n.btn-github.active,\n.open > .dropdown-toggle.btn-github {\n  background-image: none;\n}\n.btn-github.disabled,\n.btn-github[disabled],\nfieldset[disabled] .btn-github,\n.btn-github.disabled:hover,\n.btn-github[disabled]:hover,\nfieldset[disabled] .btn-github:hover,\n.btn-github.disabled:focus,\n.btn-github[disabled]:focus,\nfieldset[disabled] .btn-github:focus,\n.btn-github.disabled.focus,\n.btn-github[disabled].focus,\nfieldset[disabled] .btn-github.focus,\n.btn-github.disabled:active,\n.btn-github[disabled]:active,\nfieldset[disabled] .btn-github:active,\n.btn-github.disabled.active,\n.btn-github[disabled].active,\nfieldset[disabled] .btn-github.active {\n  background-color: #444444;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-github .badge {\n  color: #444444;\n  background-color: #fff;\n}\n.btn-google {\n  color: #fff;\n  background-color: #dd4b39;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-google:focus,\n.btn-google.focus {\n  color: #fff;\n  background-color: #c23321;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-google:hover {\n  color: #fff;\n  background-color: #c23321;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-google:active,\n.btn-google.active,\n.open > .dropdown-toggle.btn-google {\n  color: #fff;\n  background-color: #c23321;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-google:active:hover,\n.btn-google.active:hover,\n.open > .dropdown-toggle.btn-google:hover,\n.btn-google:active:focus,\n.btn-google.active:focus,\n.open > .dropdown-toggle.btn-google:focus,\n.btn-google:active.focus,\n.btn-google.active.focus,\n.open > .dropdown-toggle.btn-google.focus {\n  color: #fff;\n  background-color: #a32b1c;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-google:active,\n.btn-google.active,\n.open > .dropdown-toggle.btn-google {\n  background-image: none;\n}\n.btn-google.disabled,\n.btn-google[disabled],\nfieldset[disabled] .btn-google,\n.btn-google.disabled:hover,\n.btn-google[disabled]:hover,\nfieldset[disabled] .btn-google:hover,\n.btn-google.disabled:focus,\n.btn-google[disabled]:focus,\nfieldset[disabled] .btn-google:focus,\n.btn-google.disabled.focus,\n.btn-google[disabled].focus,\nfieldset[disabled] .btn-google.focus,\n.btn-google.disabled:active,\n.btn-google[disabled]:active,\nfieldset[disabled] .btn-google:active,\n.btn-google.disabled.active,\n.btn-google[disabled].active,\nfieldset[disabled] .btn-google.active {\n  background-color: #dd4b39;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-google .badge {\n  color: #dd4b39;\n  background-color: #fff;\n}\n.btn-instagram {\n  color: #fff;\n  background-color: #3f729b;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-instagram:focus,\n.btn-instagram.focus {\n  color: #fff;\n  background-color: #305777;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-instagram:hover {\n  color: #fff;\n  background-color: #305777;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-instagram:active,\n.btn-instagram.active,\n.open > .dropdown-toggle.btn-instagram {\n  color: #fff;\n  background-color: #305777;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-instagram:active:hover,\n.btn-instagram.active:hover,\n.open > .dropdown-toggle.btn-instagram:hover,\n.btn-instagram:active:focus,\n.btn-instagram.active:focus,\n.open > .dropdown-toggle.btn-instagram:focus,\n.btn-instagram:active.focus,\n.btn-instagram.active.focus,\n.open > .dropdown-toggle.btn-instagram.focus {\n  color: #fff;\n  background-color: #26455d;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-instagram:active,\n.btn-instagram.active,\n.open > .dropdown-toggle.btn-instagram {\n  background-image: none;\n}\n.btn-instagram.disabled,\n.btn-instagram[disabled],\nfieldset[disabled] .btn-instagram,\n.btn-instagram.disabled:hover,\n.btn-instagram[disabled]:hover,\nfieldset[disabled] .btn-instagram:hover,\n.btn-instagram.disabled:focus,\n.btn-instagram[disabled]:focus,\nfieldset[disabled] .btn-instagram:focus,\n.btn-instagram.disabled.focus,\n.btn-instagram[disabled].focus,\nfieldset[disabled] .btn-instagram.focus,\n.btn-instagram.disabled:active,\n.btn-instagram[disabled]:active,\nfieldset[disabled] .btn-instagram:active,\n.btn-instagram.disabled.active,\n.btn-instagram[disabled].active,\nfieldset[disabled] .btn-instagram.active {\n  background-color: #3f729b;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-instagram .badge {\n  color: #3f729b;\n  background-color: #fff;\n}\n.btn-linkedin {\n  color: #fff;\n  background-color: #007bb6;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-linkedin:focus,\n.btn-linkedin.focus {\n  color: #fff;\n  background-color: #005983;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-linkedin:hover {\n  color: #fff;\n  background-color: #005983;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-linkedin:active,\n.btn-linkedin.active,\n.open > .dropdown-toggle.btn-linkedin {\n  color: #fff;\n  background-color: #005983;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-linkedin:active:hover,\n.btn-linkedin.active:hover,\n.open > .dropdown-toggle.btn-linkedin:hover,\n.btn-linkedin:active:focus,\n.btn-linkedin.active:focus,\n.open > .dropdown-toggle.btn-linkedin:focus,\n.btn-linkedin:active.focus,\n.btn-linkedin.active.focus,\n.open > .dropdown-toggle.btn-linkedin.focus {\n  color: #fff;\n  background-color: #00405f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-linkedin:active,\n.btn-linkedin.active,\n.open > .dropdown-toggle.btn-linkedin {\n  background-image: none;\n}\n.btn-linkedin.disabled,\n.btn-linkedin[disabled],\nfieldset[disabled] .btn-linkedin,\n.btn-linkedin.disabled:hover,\n.btn-linkedin[disabled]:hover,\nfieldset[disabled] .btn-linkedin:hover,\n.btn-linkedin.disabled:focus,\n.btn-linkedin[disabled]:focus,\nfieldset[disabled] .btn-linkedin:focus,\n.btn-linkedin.disabled.focus,\n.btn-linkedin[disabled].focus,\nfieldset[disabled] .btn-linkedin.focus,\n.btn-linkedin.disabled:active,\n.btn-linkedin[disabled]:active,\nfieldset[disabled] .btn-linkedin:active,\n.btn-linkedin.disabled.active,\n.btn-linkedin[disabled].active,\nfieldset[disabled] .btn-linkedin.active {\n  background-color: #007bb6;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-linkedin .badge {\n  color: #007bb6;\n  background-color: #fff;\n}\n.btn-microsoft {\n  color: #fff;\n  background-color: #2672ec;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-microsoft:focus,\n.btn-microsoft.focus {\n  color: #fff;\n  background-color: #125acd;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-microsoft:hover {\n  color: #fff;\n  background-color: #125acd;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-microsoft:active,\n.btn-microsoft.active,\n.open > .dropdown-toggle.btn-microsoft {\n  color: #fff;\n  background-color: #125acd;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-microsoft:active:hover,\n.btn-microsoft.active:hover,\n.open > .dropdown-toggle.btn-microsoft:hover,\n.btn-microsoft:active:focus,\n.btn-microsoft.active:focus,\n.open > .dropdown-toggle.btn-microsoft:focus,\n.btn-microsoft:active.focus,\n.btn-microsoft.active.focus,\n.open > .dropdown-toggle.btn-microsoft.focus {\n  color: #fff;\n  background-color: #0f4bac;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-microsoft:active,\n.btn-microsoft.active,\n.open > .dropdown-toggle.btn-microsoft {\n  background-image: none;\n}\n.btn-microsoft.disabled,\n.btn-microsoft[disabled],\nfieldset[disabled] .btn-microsoft,\n.btn-microsoft.disabled:hover,\n.btn-microsoft[disabled]:hover,\nfieldset[disabled] .btn-microsoft:hover,\n.btn-microsoft.disabled:focus,\n.btn-microsoft[disabled]:focus,\nfieldset[disabled] .btn-microsoft:focus,\n.btn-microsoft.disabled.focus,\n.btn-microsoft[disabled].focus,\nfieldset[disabled] .btn-microsoft.focus,\n.btn-microsoft.disabled:active,\n.btn-microsoft[disabled]:active,\nfieldset[disabled] .btn-microsoft:active,\n.btn-microsoft.disabled.active,\n.btn-microsoft[disabled].active,\nfieldset[disabled] .btn-microsoft.active {\n  background-color: #2672ec;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-microsoft .badge {\n  color: #2672ec;\n  background-color: #fff;\n}\n.btn-openid {\n  color: #fff;\n  background-color: #f7931e;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-openid:focus,\n.btn-openid.focus {\n  color: #fff;\n  background-color: #da7908;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-openid:hover {\n  color: #fff;\n  background-color: #da7908;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-openid:active,\n.btn-openid.active,\n.open > .dropdown-toggle.btn-openid {\n  color: #fff;\n  background-color: #da7908;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-openid:active:hover,\n.btn-openid.active:hover,\n.open > .dropdown-toggle.btn-openid:hover,\n.btn-openid:active:focus,\n.btn-openid.active:focus,\n.open > .dropdown-toggle.btn-openid:focus,\n.btn-openid:active.focus,\n.btn-openid.active.focus,\n.open > .dropdown-toggle.btn-openid.focus {\n  color: #fff;\n  background-color: #b86607;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-openid:active,\n.btn-openid.active,\n.open > .dropdown-toggle.btn-openid {\n  background-image: none;\n}\n.btn-openid.disabled,\n.btn-openid[disabled],\nfieldset[disabled] .btn-openid,\n.btn-openid.disabled:hover,\n.btn-openid[disabled]:hover,\nfieldset[disabled] .btn-openid:hover,\n.btn-openid.disabled:focus,\n.btn-openid[disabled]:focus,\nfieldset[disabled] .btn-openid:focus,\n.btn-openid.disabled.focus,\n.btn-openid[disabled].focus,\nfieldset[disabled] .btn-openid.focus,\n.btn-openid.disabled:active,\n.btn-openid[disabled]:active,\nfieldset[disabled] .btn-openid:active,\n.btn-openid.disabled.active,\n.btn-openid[disabled].active,\nfieldset[disabled] .btn-openid.active {\n  background-color: #f7931e;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-openid .badge {\n  color: #f7931e;\n  background-color: #fff;\n}\n.btn-pinterest {\n  color: #fff;\n  background-color: #cb2027;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-pinterest:focus,\n.btn-pinterest.focus {\n  color: #fff;\n  background-color: #9f191f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-pinterest:hover {\n  color: #fff;\n  background-color: #9f191f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-pinterest:active,\n.btn-pinterest.active,\n.open > .dropdown-toggle.btn-pinterest {\n  color: #fff;\n  background-color: #9f191f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-pinterest:active:hover,\n.btn-pinterest.active:hover,\n.open > .dropdown-toggle.btn-pinterest:hover,\n.btn-pinterest:active:focus,\n.btn-pinterest.active:focus,\n.open > .dropdown-toggle.btn-pinterest:focus,\n.btn-pinterest:active.focus,\n.btn-pinterest.active.focus,\n.open > .dropdown-toggle.btn-pinterest.focus {\n  color: #fff;\n  background-color: #801419;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-pinterest:active,\n.btn-pinterest.active,\n.open > .dropdown-toggle.btn-pinterest {\n  background-image: none;\n}\n.btn-pinterest.disabled,\n.btn-pinterest[disabled],\nfieldset[disabled] .btn-pinterest,\n.btn-pinterest.disabled:hover,\n.btn-pinterest[disabled]:hover,\nfieldset[disabled] .btn-pinterest:hover,\n.btn-pinterest.disabled:focus,\n.btn-pinterest[disabled]:focus,\nfieldset[disabled] .btn-pinterest:focus,\n.btn-pinterest.disabled.focus,\n.btn-pinterest[disabled].focus,\nfieldset[disabled] .btn-pinterest.focus,\n.btn-pinterest.disabled:active,\n.btn-pinterest[disabled]:active,\nfieldset[disabled] .btn-pinterest:active,\n.btn-pinterest.disabled.active,\n.btn-pinterest[disabled].active,\nfieldset[disabled] .btn-pinterest.active {\n  background-color: #cb2027;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-pinterest .badge {\n  color: #cb2027;\n  background-color: #fff;\n}\n.btn-reddit {\n  color: #000;\n  background-color: #eff7ff;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-reddit:focus,\n.btn-reddit.focus {\n  color: #000;\n  background-color: #bcddff;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-reddit:hover {\n  color: #000;\n  background-color: #bcddff;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-reddit:active,\n.btn-reddit.active,\n.open > .dropdown-toggle.btn-reddit {\n  color: #000;\n  background-color: #bcddff;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-reddit:active:hover,\n.btn-reddit.active:hover,\n.open > .dropdown-toggle.btn-reddit:hover,\n.btn-reddit:active:focus,\n.btn-reddit.active:focus,\n.open > .dropdown-toggle.btn-reddit:focus,\n.btn-reddit:active.focus,\n.btn-reddit.active.focus,\n.open > .dropdown-toggle.btn-reddit.focus {\n  color: #000;\n  background-color: #98ccff;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-reddit:active,\n.btn-reddit.active,\n.open > .dropdown-toggle.btn-reddit {\n  background-image: none;\n}\n.btn-reddit.disabled,\n.btn-reddit[disabled],\nfieldset[disabled] .btn-reddit,\n.btn-reddit.disabled:hover,\n.btn-reddit[disabled]:hover,\nfieldset[disabled] .btn-reddit:hover,\n.btn-reddit.disabled:focus,\n.btn-reddit[disabled]:focus,\nfieldset[disabled] .btn-reddit:focus,\n.btn-reddit.disabled.focus,\n.btn-reddit[disabled].focus,\nfieldset[disabled] .btn-reddit.focus,\n.btn-reddit.disabled:active,\n.btn-reddit[disabled]:active,\nfieldset[disabled] .btn-reddit:active,\n.btn-reddit.disabled.active,\n.btn-reddit[disabled].active,\nfieldset[disabled] .btn-reddit.active {\n  background-color: #eff7ff;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-reddit .badge {\n  color: #eff7ff;\n  background-color: #000;\n}\n.btn-soundcloud {\n  color: #fff;\n  background-color: #ff5500;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-soundcloud:focus,\n.btn-soundcloud.focus {\n  color: #fff;\n  background-color: #cc4400;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-soundcloud:hover {\n  color: #fff;\n  background-color: #cc4400;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-soundcloud:active,\n.btn-soundcloud.active,\n.open > .dropdown-toggle.btn-soundcloud {\n  color: #fff;\n  background-color: #cc4400;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-soundcloud:active:hover,\n.btn-soundcloud.active:hover,\n.open > .dropdown-toggle.btn-soundcloud:hover,\n.btn-soundcloud:active:focus,\n.btn-soundcloud.active:focus,\n.open > .dropdown-toggle.btn-soundcloud:focus,\n.btn-soundcloud:active.focus,\n.btn-soundcloud.active.focus,\n.open > .dropdown-toggle.btn-soundcloud.focus {\n  color: #fff;\n  background-color: #a83800;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-soundcloud:active,\n.btn-soundcloud.active,\n.open > .dropdown-toggle.btn-soundcloud {\n  background-image: none;\n}\n.btn-soundcloud.disabled,\n.btn-soundcloud[disabled],\nfieldset[disabled] .btn-soundcloud,\n.btn-soundcloud.disabled:hover,\n.btn-soundcloud[disabled]:hover,\nfieldset[disabled] .btn-soundcloud:hover,\n.btn-soundcloud.disabled:focus,\n.btn-soundcloud[disabled]:focus,\nfieldset[disabled] .btn-soundcloud:focus,\n.btn-soundcloud.disabled.focus,\n.btn-soundcloud[disabled].focus,\nfieldset[disabled] .btn-soundcloud.focus,\n.btn-soundcloud.disabled:active,\n.btn-soundcloud[disabled]:active,\nfieldset[disabled] .btn-soundcloud:active,\n.btn-soundcloud.disabled.active,\n.btn-soundcloud[disabled].active,\nfieldset[disabled] .btn-soundcloud.active {\n  background-color: #ff5500;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-soundcloud .badge {\n  color: #ff5500;\n  background-color: #fff;\n}\n.btn-tumblr {\n  color: #fff;\n  background-color: #2c4762;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-tumblr:focus,\n.btn-tumblr.focus {\n  color: #fff;\n  background-color: #1c2d3f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-tumblr:hover {\n  color: #fff;\n  background-color: #1c2d3f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-tumblr:active,\n.btn-tumblr.active,\n.open > .dropdown-toggle.btn-tumblr {\n  color: #fff;\n  background-color: #1c2d3f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-tumblr:active:hover,\n.btn-tumblr.active:hover,\n.open > .dropdown-toggle.btn-tumblr:hover,\n.btn-tumblr:active:focus,\n.btn-tumblr.active:focus,\n.open > .dropdown-toggle.btn-tumblr:focus,\n.btn-tumblr:active.focus,\n.btn-tumblr.active.focus,\n.open > .dropdown-toggle.btn-tumblr.focus {\n  color: #fff;\n  background-color: #111c26;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-tumblr:active,\n.btn-tumblr.active,\n.open > .dropdown-toggle.btn-tumblr {\n  background-image: none;\n}\n.btn-tumblr.disabled,\n.btn-tumblr[disabled],\nfieldset[disabled] .btn-tumblr,\n.btn-tumblr.disabled:hover,\n.btn-tumblr[disabled]:hover,\nfieldset[disabled] .btn-tumblr:hover,\n.btn-tumblr.disabled:focus,\n.btn-tumblr[disabled]:focus,\nfieldset[disabled] .btn-tumblr:focus,\n.btn-tumblr.disabled.focus,\n.btn-tumblr[disabled].focus,\nfieldset[disabled] .btn-tumblr.focus,\n.btn-tumblr.disabled:active,\n.btn-tumblr[disabled]:active,\nfieldset[disabled] .btn-tumblr:active,\n.btn-tumblr.disabled.active,\n.btn-tumblr[disabled].active,\nfieldset[disabled] .btn-tumblr.active {\n  background-color: #2c4762;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-tumblr .badge {\n  color: #2c4762;\n  background-color: #fff;\n}\n.btn-twitter {\n  color: #fff;\n  background-color: #55acee;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-twitter:focus,\n.btn-twitter.focus {\n  color: #fff;\n  background-color: #2795e9;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-twitter:hover {\n  color: #fff;\n  background-color: #2795e9;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-twitter:active,\n.btn-twitter.active,\n.open > .dropdown-toggle.btn-twitter {\n  color: #fff;\n  background-color: #2795e9;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-twitter:active:hover,\n.btn-twitter.active:hover,\n.open > .dropdown-toggle.btn-twitter:hover,\n.btn-twitter:active:focus,\n.btn-twitter.active:focus,\n.open > .dropdown-toggle.btn-twitter:focus,\n.btn-twitter:active.focus,\n.btn-twitter.active.focus,\n.open > .dropdown-toggle.btn-twitter.focus {\n  color: #fff;\n  background-color: #1583d7;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-twitter:active,\n.btn-twitter.active,\n.open > .dropdown-toggle.btn-twitter {\n  background-image: none;\n}\n.btn-twitter.disabled,\n.btn-twitter[disabled],\nfieldset[disabled] .btn-twitter,\n.btn-twitter.disabled:hover,\n.btn-twitter[disabled]:hover,\nfieldset[disabled] .btn-twitter:hover,\n.btn-twitter.disabled:focus,\n.btn-twitter[disabled]:focus,\nfieldset[disabled] .btn-twitter:focus,\n.btn-twitter.disabled.focus,\n.btn-twitter[disabled].focus,\nfieldset[disabled] .btn-twitter.focus,\n.btn-twitter.disabled:active,\n.btn-twitter[disabled]:active,\nfieldset[disabled] .btn-twitter:active,\n.btn-twitter.disabled.active,\n.btn-twitter[disabled].active,\nfieldset[disabled] .btn-twitter.active {\n  background-color: #55acee;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-twitter .badge {\n  color: #55acee;\n  background-color: #fff;\n}\n.btn-vimeo {\n  color: #fff;\n  background-color: #1ab7ea;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vimeo:focus,\n.btn-vimeo.focus {\n  color: #fff;\n  background-color: #1295bf;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vimeo:hover {\n  color: #fff;\n  background-color: #1295bf;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vimeo:active,\n.btn-vimeo.active,\n.open > .dropdown-toggle.btn-vimeo {\n  color: #fff;\n  background-color: #1295bf;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vimeo:active:hover,\n.btn-vimeo.active:hover,\n.open > .dropdown-toggle.btn-vimeo:hover,\n.btn-vimeo:active:focus,\n.btn-vimeo.active:focus,\n.open > .dropdown-toggle.btn-vimeo:focus,\n.btn-vimeo:active.focus,\n.btn-vimeo.active.focus,\n.open > .dropdown-toggle.btn-vimeo.focus {\n  color: #fff;\n  background-color: #0f7b9f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vimeo:active,\n.btn-vimeo.active,\n.open > .dropdown-toggle.btn-vimeo {\n  background-image: none;\n}\n.btn-vimeo.disabled,\n.btn-vimeo[disabled],\nfieldset[disabled] .btn-vimeo,\n.btn-vimeo.disabled:hover,\n.btn-vimeo[disabled]:hover,\nfieldset[disabled] .btn-vimeo:hover,\n.btn-vimeo.disabled:focus,\n.btn-vimeo[disabled]:focus,\nfieldset[disabled] .btn-vimeo:focus,\n.btn-vimeo.disabled.focus,\n.btn-vimeo[disabled].focus,\nfieldset[disabled] .btn-vimeo.focus,\n.btn-vimeo.disabled:active,\n.btn-vimeo[disabled]:active,\nfieldset[disabled] .btn-vimeo:active,\n.btn-vimeo.disabled.active,\n.btn-vimeo[disabled].active,\nfieldset[disabled] .btn-vimeo.active {\n  background-color: #1ab7ea;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vimeo .badge {\n  color: #1ab7ea;\n  background-color: #fff;\n}\n.btn-vk {\n  color: #fff;\n  background-color: #587ea3;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vk:focus,\n.btn-vk.focus {\n  color: #fff;\n  background-color: #466482;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vk:hover {\n  color: #fff;\n  background-color: #466482;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vk:active,\n.btn-vk.active,\n.open > .dropdown-toggle.btn-vk {\n  color: #fff;\n  background-color: #466482;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vk:active:hover,\n.btn-vk.active:hover,\n.open > .dropdown-toggle.btn-vk:hover,\n.btn-vk:active:focus,\n.btn-vk.active:focus,\n.open > .dropdown-toggle.btn-vk:focus,\n.btn-vk:active.focus,\n.btn-vk.active.focus,\n.open > .dropdown-toggle.btn-vk.focus {\n  color: #fff;\n  background-color: #3a526b;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vk:active,\n.btn-vk.active,\n.open > .dropdown-toggle.btn-vk {\n  background-image: none;\n}\n.btn-vk.disabled,\n.btn-vk[disabled],\nfieldset[disabled] .btn-vk,\n.btn-vk.disabled:hover,\n.btn-vk[disabled]:hover,\nfieldset[disabled] .btn-vk:hover,\n.btn-vk.disabled:focus,\n.btn-vk[disabled]:focus,\nfieldset[disabled] .btn-vk:focus,\n.btn-vk.disabled.focus,\n.btn-vk[disabled].focus,\nfieldset[disabled] .btn-vk.focus,\n.btn-vk.disabled:active,\n.btn-vk[disabled]:active,\nfieldset[disabled] .btn-vk:active,\n.btn-vk.disabled.active,\n.btn-vk[disabled].active,\nfieldset[disabled] .btn-vk.active {\n  background-color: #587ea3;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-vk .badge {\n  color: #587ea3;\n  background-color: #fff;\n}\n.btn-yahoo {\n  color: #fff;\n  background-color: #720e9e;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-yahoo:focus,\n.btn-yahoo.focus {\n  color: #fff;\n  background-color: #500a6f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-yahoo:hover {\n  color: #fff;\n  background-color: #500a6f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-yahoo:active,\n.btn-yahoo.active,\n.open > .dropdown-toggle.btn-yahoo {\n  color: #fff;\n  background-color: #500a6f;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-yahoo:active:hover,\n.btn-yahoo.active:hover,\n.open > .dropdown-toggle.btn-yahoo:hover,\n.btn-yahoo:active:focus,\n.btn-yahoo.active:focus,\n.open > .dropdown-toggle.btn-yahoo:focus,\n.btn-yahoo:active.focus,\n.btn-yahoo.active.focus,\n.open > .dropdown-toggle.btn-yahoo.focus {\n  color: #fff;\n  background-color: #39074e;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-yahoo:active,\n.btn-yahoo.active,\n.open > .dropdown-toggle.btn-yahoo {\n  background-image: none;\n}\n.btn-yahoo.disabled,\n.btn-yahoo[disabled],\nfieldset[disabled] .btn-yahoo,\n.btn-yahoo.disabled:hover,\n.btn-yahoo[disabled]:hover,\nfieldset[disabled] .btn-yahoo:hover,\n.btn-yahoo.disabled:focus,\n.btn-yahoo[disabled]:focus,\nfieldset[disabled] .btn-yahoo:focus,\n.btn-yahoo.disabled.focus,\n.btn-yahoo[disabled].focus,\nfieldset[disabled] .btn-yahoo.focus,\n.btn-yahoo.disabled:active,\n.btn-yahoo[disabled]:active,\nfieldset[disabled] .btn-yahoo:active,\n.btn-yahoo.disabled.active,\n.btn-yahoo[disabled].active,\nfieldset[disabled] .btn-yahoo.active {\n  background-color: #720e9e;\n  border-color: rgba(0, 0, 0, 0.2);\n}\n.btn-yahoo .badge {\n  color: #720e9e;\n  background-color: #fff;\n}\n/*\n * Plugin: Full Calendar\n * ---------------------\n */\n.fc-button {\n  background: #f4f4f4;\n  background-image: none;\n  color: #444;\n  border-color: #ddd;\n  border-bottom-color: #ddd;\n}\n.fc-button:hover,\n.fc-button:active,\n.fc-button.hover {\n  background-color: #e9e9e9;\n}\n.fc-header-title h2 {\n  font-size: 15px;\n  line-height: 1.6em;\n  color: #666;\n  margin-left: 10px;\n}\n.fc-header-right {\n  padding-right: 10px;\n}\n.fc-header-left {\n  padding-left: 10px;\n}\n.fc-widget-header {\n  background: #fafafa;\n}\n.fc-grid {\n  width: 100%;\n  border: 0;\n}\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n.fc-toolbar {\n  padding: 10px;\n  margin: 0;\n}\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.fc-color-picker > li {\n  float: left;\n  font-size: 30px;\n  margin-right: 5px;\n  line-height: 30px;\n}\n.fc-color-picker > li .fa {\n  -webkit-transition: -webkit-transform linear 0.3s;\n  -moz-transition: -moz-transform linear 0.3s;\n  -o-transition: -o-transform linear 0.3s;\n  transition: transform linear 0.3s;\n}\n.fc-color-picker > li .fa:hover {\n  -webkit-transform: rotate(30deg);\n  -ms-transform: rotate(30deg);\n  -o-transform: rotate(30deg);\n  transform: rotate(30deg);\n}\n#add-new-event {\n  -webkit-transition: all linear 0.3s;\n  -o-transition: all linear 0.3s;\n  transition: all linear 0.3s;\n}\n.external-event {\n  padding: 5px 10px;\n  font-weight: bold;\n  margin-bottom: 4px;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n  border-radius: 3px;\n  cursor: move;\n}\n.external-event:hover {\n  box-shadow: inset 0 0 90px rgba(0, 0, 0, 0.2);\n}\n/*\n * Plugin: Select2\n * ---------------\n */\n.select2-container--default.select2-container--focus,\n.select2-selection.select2-container--focus,\n.select2-container--default:focus,\n.select2-selection:focus,\n.select2-container--default:active,\n.select2-selection:active {\n  outline: none;\n}\n.select2-container--default .select2-selection--single,\n.select2-selection .select2-selection--single {\n  border: 1px solid #d2d6de;\n  border-radius: 0;\n  padding: 6px 12px;\n  height: 34px;\n}\n.select2-container--default.select2-container--open {\n  border-color: #3c8dbc;\n}\n.select2-dropdown {\n  border: 1px solid #d2d6de;\n  border-radius: 0;\n}\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #3c8dbc;\n  color: white;\n}\n.select2-results__option {\n  padding: 6px 12px;\n  user-select: none;\n  -webkit-user-select: none;\n}\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-left: 0;\n  padding-right: 0;\n  height: auto;\n  margin-top: -4px;\n}\n.select2-container[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n  padding-right: 6px;\n  padding-left: 20px;\n}\n.select2-container--default .select2-selection--single .select2-selection__arrow {\n  height: 28px;\n  right: 3px;\n}\n.select2-container--default .select2-selection--single .select2-selection__arrow b {\n  margin-top: 0;\n}\n.select2-dropdown .select2-search__field,\n.select2-search--inline .select2-search__field {\n  border: 1px solid #d2d6de;\n}\n.select2-dropdown .select2-search__field:focus,\n.select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: 1px solid #3c8dbc;\n}\n.select2-container--default .select2-results__option[aria-disabled=true] {\n  color: #999;\n}\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #ddd;\n}\n.select2-container--default .select2-results__option[aria-selected=true],\n.select2-container--default .select2-results__option[aria-selected=true]:hover {\n  color: #444;\n}\n.select2-container--default .select2-selection--multiple {\n  border: 1px solid #d2d6de;\n  border-radius: 0;\n}\n.select2-container--default .select2-selection--multiple:focus {\n  border-color: #3c8dbc;\n}\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #d2d6de;\n}\n.select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3c8dbc;\n  border-color: #367fa9;\n  padding: 1px 10px;\n  color: #fff;\n}\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  margin-right: 5px;\n  color: rgba(255, 255, 255, 0.7);\n}\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-right: 10px;\n}\n/*\n * General: Miscellaneous\n * ----------------------\n */\n.pad {\n  padding: 10px;\n}\n.margin {\n  margin: 10px;\n}\n.margin-bottom {\n  margin-bottom: 20px;\n}\n.margin-bottom-none {\n  margin-bottom: 0;\n}\n.margin-r-5 {\n  margin-right: 5px;\n}\n.inline {\n  display: inline;\n}\n.description-block {\n  display: block;\n  margin: 10px 0;\n  text-align: center;\n}\n.description-block.margin-bottom {\n  margin-bottom: 25px;\n}\n.description-block > .description-header {\n  margin: 0;\n  padding: 0;\n  font-weight: 600;\n  font-size: 16px;\n}\n.description-block > .description-text {\n  text-transform: uppercase;\n}\n.bg-red,\n.bg-yellow,\n.bg-aqua,\n.bg-blue,\n.bg-light-blue,\n.bg-green,\n.bg-navy,\n.bg-teal,\n.bg-olive,\n.bg-lime,\n.bg-orange,\n.bg-fuchsia,\n.bg-purple,\n.bg-maroon,\n.bg-black,\n.bg-red-active,\n.bg-yellow-active,\n.bg-aqua-active,\n.bg-blue-active,\n.bg-light-blue-active,\n.bg-green-active,\n.bg-navy-active,\n.bg-teal-active,\n.bg-olive-active,\n.bg-lime-active,\n.bg-orange-active,\n.bg-fuchsia-active,\n.bg-purple-active,\n.bg-maroon-active,\n.bg-black-active,\n.callout.callout-danger,\n.callout.callout-warning,\n.callout.callout-info,\n.callout.callout-success,\n.alert-success,\n.alert-danger,\n.alert-error,\n.alert-warning,\n.alert-info,\n.label-danger,\n.label-info,\n.label-warning,\n.label-primary,\n.label-success,\n.modal-primary .modal-body,\n.modal-primary .modal-header,\n.modal-primary .modal-footer,\n.modal-warning .modal-body,\n.modal-warning .modal-header,\n.modal-warning .modal-footer,\n.modal-info .modal-body,\n.modal-info .modal-header,\n.modal-info .modal-footer,\n.modal-success .modal-body,\n.modal-success .modal-header,\n.modal-success .modal-footer,\n.modal-danger .modal-body,\n.modal-danger .modal-header,\n.modal-danger .modal-footer {\n  color: #fff !important;\n}\n.bg-gray {\n  color: #000;\n  background-color: #d2d6de !important;\n}\n.bg-gray-light {\n  background-color: #f7f7f7;\n}\n.bg-black {\n  background-color: #111 !important;\n}\n.bg-red,\n.callout.callout-danger,\n.alert-danger,\n.alert-error,\n.label-danger,\n.modal-danger .modal-body {\n  background-color: #dd4b39 !important;\n}\n.bg-yellow,\n.callout.callout-warning,\n.alert-warning,\n.label-warning,\n.modal-warning .modal-body {\n  background-color: #f39c12 !important;\n}\n.bg-aqua,\n.callout.callout-info,\n.alert-info,\n.label-info,\n.modal-info .modal-body {\n  background-color: #00c0ef !important;\n}\n.bg-blue {\n  background-color: #0073b7 !important;\n}\n.bg-light-blue,\n.label-primary,\n.modal-primary .modal-body {\n  background-color: #3c8dbc !important;\n}\n.bg-green,\n.callout.callout-success,\n.alert-success,\n.label-success,\n.modal-success .modal-body {\n  background-color: #00a65a !important;\n}\n.bg-navy {\n  background-color: #001F3F !important;\n}\n.bg-teal {\n  background-color: #39CCCC !important;\n}\n.bg-olive {\n  background-color: #3D9970 !important;\n}\n.bg-lime {\n  background-color: #01FF70 !important;\n}\n.bg-orange {\n  background-color: #FF851B !important;\n}\n.bg-fuchsia {\n  background-color: #F012BE !important;\n}\n.bg-purple {\n  background-color: #605ca8 !important;\n}\n.bg-maroon {\n  background-color: #D81B60 !important;\n}\n.bg-gray-active {\n  color: #000;\n  background-color: #b5bbc8 !important;\n}\n.bg-black-active {\n  background-color: #000000 !important;\n}\n.bg-red-active,\n.modal-danger .modal-header,\n.modal-danger .modal-footer {\n  background-color: #d33724 !important;\n}\n.bg-yellow-active,\n.modal-warning .modal-header,\n.modal-warning .modal-footer {\n  background-color: #db8b0b !important;\n}\n.bg-aqua-active,\n.modal-info .modal-header,\n.modal-info .modal-footer {\n  background-color: #00a7d0 !important;\n}\n.bg-blue-active {\n  background-color: #005384 !important;\n}\n.bg-light-blue-active,\n.modal-primary .modal-header,\n.modal-primary .modal-footer {\n  background-color: #357ca5 !important;\n}\n.bg-green-active,\n.modal-success .modal-header,\n.modal-success .modal-footer {\n  background-color: #008d4c !important;\n}\n.bg-navy-active {\n  background-color: #001a35 !important;\n}\n.bg-teal-active {\n  background-color: #30bbbb !important;\n}\n.bg-olive-active {\n  background-color: #368763 !important;\n}\n.bg-lime-active {\n  background-color: #00e765 !important;\n}\n.bg-orange-active {\n  background-color: #ff7701 !important;\n}\n.bg-fuchsia-active {\n  background-color: #db0ead !important;\n}\n.bg-purple-active {\n  background-color: #555299 !important;\n}\n.bg-maroon-active {\n  background-color: #ca195a !important;\n}\n[class^=\"bg-\"].disabled {\n  opacity: 0.65;\n  filter: alpha(opacity=65);\n}\n.text-red {\n  color: #dd4b39 !important;\n}\n.text-yellow {\n  color: #f39c12 !important;\n}\n.text-aqua {\n  color: #00c0ef !important;\n}\n.text-blue {\n  color: #0073b7 !important;\n}\n.text-black {\n  color: #111 !important;\n}\n.text-light-blue {\n  color: #3c8dbc !important;\n}\n.text-green {\n  color: #00a65a !important;\n}\n.text-gray {\n  color: #d2d6de !important;\n}\n.text-navy {\n  color: #001F3F !important;\n}\n.text-teal {\n  color: #39CCCC !important;\n}\n.text-olive {\n  color: #3D9970 !important;\n}\n.text-lime {\n  color: #01FF70 !important;\n}\n.text-orange {\n  color: #FF851B !important;\n}\n.text-fuchsia {\n  color: #F012BE !important;\n}\n.text-purple {\n  color: #605ca8 !important;\n}\n.text-maroon {\n  color: #D81B60 !important;\n}\n.link-muted {\n  color: #7a869d;\n}\n.link-muted:hover,\n.link-muted:focus {\n  color: #606c84;\n}\n.link-black {\n  color: #666;\n}\n.link-black:hover,\n.link-black:focus {\n  color: #999;\n}\n.hide {\n  display: none !important;\n}\n.no-border {\n  border: 0 !important;\n}\n.no-padding {\n  padding: 0 !important;\n}\n.no-margin {\n  margin: 0 !important;\n}\n.no-shadow {\n  box-shadow: none !important;\n}\n.list-unstyled,\n.chart-legend,\n.contacts-list,\n.users-list,\n.mailbox-attachments {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.list-group-unbordered > .list-group-item {\n  border-left: 0;\n  border-right: 0;\n  border-radius: 0;\n  padding-left: 0;\n  padding-right: 0;\n}\n.flat {\n  border-radius: 0 !important;\n}\n.text-bold,\n.text-bold.table td,\n.text-bold.table th {\n  font-weight: 700;\n}\n.text-sm {\n  font-size: 12px;\n}\n.jqstooltip {\n  padding: 5px !important;\n  width: auto !important;\n  height: auto !important;\n}\n.bg-teal-gradient {\n  background: #39CCCC !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #39CCCC), color-stop(1, #7adddd)) !important;\n  background: -ms-linear-gradient(bottom, #39CCCC, #7adddd) !important;\n  background: -moz-linear-gradient(center bottom, #39CCCC 0%, #7adddd 100%) !important;\n  background: -o-linear-gradient(#7adddd, #39CCCC) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#7adddd', endColorstr='#39CCCC', GradientType=0) !important;\n  color: #fff;\n}\n.bg-light-blue-gradient {\n  background: #3c8dbc !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #3c8dbc), color-stop(1, #67a8ce)) !important;\n  background: -ms-linear-gradient(bottom, #3c8dbc, #67a8ce) !important;\n  background: -moz-linear-gradient(center bottom, #3c8dbc 0%, #67a8ce 100%) !important;\n  background: -o-linear-gradient(#67a8ce, #3c8dbc) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#67a8ce', endColorstr='#3c8dbc', GradientType=0) !important;\n  color: #fff;\n}\n.bg-blue-gradient {\n  background: #0073b7 !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #0073b7), color-stop(1, #0089db)) !important;\n  background: -ms-linear-gradient(bottom, #0073b7, #0089db) !important;\n  background: -moz-linear-gradient(center bottom, #0073b7 0%, #0089db 100%) !important;\n  background: -o-linear-gradient(#0089db, #0073b7) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0089db', endColorstr='#0073b7', GradientType=0) !important;\n  color: #fff;\n}\n.bg-aqua-gradient {\n  background: #00c0ef !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #00c0ef), color-stop(1, #14d1ff)) !important;\n  background: -ms-linear-gradient(bottom, #00c0ef, #14d1ff) !important;\n  background: -moz-linear-gradient(center bottom, #00c0ef 0%, #14d1ff 100%) !important;\n  background: -o-linear-gradient(#14d1ff, #00c0ef) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#14d1ff', endColorstr='#00c0ef', GradientType=0) !important;\n  color: #fff;\n}\n.bg-yellow-gradient {\n  background: #f39c12 !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #f39c12), color-stop(1, #f7bc60)) !important;\n  background: -ms-linear-gradient(bottom, #f39c12, #f7bc60) !important;\n  background: -moz-linear-gradient(center bottom, #f39c12 0%, #f7bc60 100%) !important;\n  background: -o-linear-gradient(#f7bc60, #f39c12) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7bc60', endColorstr='#f39c12', GradientType=0) !important;\n  color: #fff;\n}\n.bg-purple-gradient {\n  background: #605ca8 !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #605ca8), color-stop(1, #9491c4)) !important;\n  background: -ms-linear-gradient(bottom, #605ca8, #9491c4) !important;\n  background: -moz-linear-gradient(center bottom, #605ca8 0%, #9491c4 100%) !important;\n  background: -o-linear-gradient(#9491c4, #605ca8) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#9491c4', endColorstr='#605ca8', GradientType=0) !important;\n  color: #fff;\n}\n.bg-green-gradient {\n  background: #00a65a !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #00a65a), color-stop(1, #00ca6d)) !important;\n  background: -ms-linear-gradient(bottom, #00a65a, #00ca6d) !important;\n  background: -moz-linear-gradient(center bottom, #00a65a 0%, #00ca6d 100%) !important;\n  background: -o-linear-gradient(#00ca6d, #00a65a) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ca6d', endColorstr='#00a65a', GradientType=0) !important;\n  color: #fff;\n}\n.bg-red-gradient {\n  background: #dd4b39 !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #dd4b39), color-stop(1, #e47365)) !important;\n  background: -ms-linear-gradient(bottom, #dd4b39, #e47365) !important;\n  background: -moz-linear-gradient(center bottom, #dd4b39 0%, #e47365 100%) !important;\n  background: -o-linear-gradient(#e47365, #dd4b39) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e47365', endColorstr='#dd4b39', GradientType=0) !important;\n  color: #fff;\n}\n.bg-black-gradient {\n  background: #111 !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #111), color-stop(1, #2b2b2b)) !important;\n  background: -ms-linear-gradient(bottom, #111, #2b2b2b) !important;\n  background: -moz-linear-gradient(center bottom, #111 0%, #2b2b2b 100%) !important;\n  background: -o-linear-gradient(#2b2b2b, #111) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2b2b2b', endColorstr='#111', GradientType=0) !important;\n  color: #fff;\n}\n.bg-maroon-gradient {\n  background: #D81B60 !important;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #D81B60), color-stop(1, #e73f7c)) !important;\n  background: -ms-linear-gradient(bottom, #D81B60, #e73f7c) !important;\n  background: -moz-linear-gradient(center bottom, #D81B60 0%, #e73f7c 100%) !important;\n  background: -o-linear-gradient(#e73f7c, #D81B60) !important;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e73f7c', endColorstr='#D81B60', GradientType=0) !important;\n  color: #fff;\n}\n.description-block .description-icon {\n  font-size: 16px;\n}\n.no-pad-top {\n  padding-top: 0;\n}\n.position-static {\n  position: static !important;\n}\n.list-header {\n  font-size: 15px;\n  padding: 10px 4px;\n  font-weight: bold;\n  color: #666;\n}\n.list-seperator {\n  height: 1px;\n  background: #f4f4f4;\n  margin: 15px 0 9px 0;\n}\n.list-link > a {\n  padding: 4px;\n  color: #777;\n}\n.list-link > a:hover {\n  color: #222;\n}\n.font-light {\n  font-weight: 300;\n}\n.user-block:before,\n.user-block:after {\n  content: \" \";\n  display: table;\n}\n.user-block:after {\n  clear: both;\n}\n.user-block img {\n  width: 40px;\n  height: 40px;\n  float: left;\n}\n.user-block .username,\n.user-block .description,\n.user-block .comment {\n  display: block;\n  margin-left: 50px;\n}\n.user-block .username {\n  font-size: 16px;\n  font-weight: 600;\n}\n.user-block .description {\n  color: #999;\n  font-size: 13px;\n}\n.user-block.user-block-sm .username,\n.user-block.user-block-sm .description,\n.user-block.user-block-sm .comment {\n  margin-left: 40px;\n}\n.user-block.user-block-sm .username {\n  font-size: 14px;\n}\n.img-sm,\n.img-md,\n.img-lg,\n.box-comments .box-comment img,\n.user-block.user-block-sm img {\n  float: left;\n}\n.img-sm,\n.box-comments .box-comment img,\n.user-block.user-block-sm img {\n  width: 30px !important;\n  height: 30px !important;\n}\n.img-sm + .img-push {\n  margin-left: 40px;\n}\n.img-md {\n  width: 60px;\n  height: 60px;\n}\n.img-md + .img-push {\n  margin-left: 70px;\n}\n.img-lg {\n  width: 100px;\n  height: 100px;\n}\n.img-lg + .img-push {\n  margin-left: 110px;\n}\n.img-bordered {\n  border: 3px solid #d2d6de;\n  padding: 3px;\n}\n.img-bordered-sm {\n  border: 2px solid #d2d6de;\n  padding: 2px;\n}\n.attachment-block {\n  border: 1px solid #f4f4f4;\n  padding: 5px;\n  margin-bottom: 10px;\n  background: #f7f7f7;\n}\n.attachment-block .attachment-img {\n  max-width: 100px;\n  max-height: 100px;\n  height: auto;\n  float: left;\n}\n.attachment-block .attachment-pushed {\n  margin-left: 110px;\n}\n.attachment-block .attachment-heading {\n  margin: 0;\n}\n.attachment-block .attachment-text {\n  color: #555;\n}\n.connectedSortable {\n  min-height: 100px;\n}\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n.sort-highlight {\n  background: #f4f4f4;\n  border: 1px dashed #ddd;\n  margin-bottom: 10px;\n}\n.full-opacity-hover {\n  opacity: 0.65;\n  filter: alpha(opacity=65);\n}\n.full-opacity-hover:hover {\n  opacity: 1;\n  filter: alpha(opacity=100);\n}\n.chart {\n  position: relative;\n  overflow: hidden;\n  width: 100%;\n}\n.chart svg,\n.chart canvas {\n  width: 100% !important;\n}\n/*\n * Misc: print\n * -----------\n */\n@media print {\n  .no-print,\n  .main-sidebar,\n  .left-side,\n  .main-header,\n  .content-header {\n    display: none !important;\n  }\n  .content-wrapper,\n  .right-side,\n  .main-footer {\n    margin-left: 0 !important;\n    min-height: 0 !important;\n    -webkit-transform: translate(0, 0) !important;\n    -ms-transform: translate(0, 0) !important;\n    -o-transform: translate(0, 0) !important;\n    transform: translate(0, 0) !important;\n  }\n  .fixed .content-wrapper,\n  .fixed .right-side {\n    padding-top: 0 !important;\n  }\n  .invoice {\n    width: 100%;\n    border: 0;\n    margin: 0;\n    padding: 0;\n  }\n  .invoice-col {\n    float: left;\n    width: 33.3333333%;\n  }\n  .table-responsive {\n    overflow: auto;\n  }\n  .table-responsive > .table tr th,\n  .table-responsive > .table tr td {\n    white-space: normal !important;\n  }\n}\n", "/*\n * Core: General Layout Style\n * -------------------------\n */\nhtml,\nbody {\n  height: 100%;\n  .layout-boxed & {\n    height: 100%;\n  }\n}\n\nbody {\n  font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n  font-weight: 400;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n/* Layout */\n.wrapper {\n  .clearfix();\n  height: 100%;\n  position: relative;\n  overflow-x: hidden;\n  overflow-y: auto;\n  .layout-boxed & {\n    max-width: 1250px;\n    margin: 0 auto;\n    min-height: 100%;\n    box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\n    position: relative;\n  }\n}\n\n.layout-boxed {\n  background: url('@{boxed-layout-bg-image-path}') repeat fixed;\n}\n\n/*\n * Content Wrapper - contains the main content\n * ```.right-side has been deprecated as of v2.0.0 in favor of .content-wrapper  ```\n */\n.content-wrapper,\n.right-side,\n.main-footer {\n  //Using disposable variable to join statements with a comma\n  @transition-rule: @transition-speed @transition-fn,\n  margin @transition-speed @transition-fn;\n  .transition-transform(@transition-rule);\n  margin-left: @sidebar-width;\n  z-index: 820;\n  //Top nav layout\n  .layout-top-nav & {\n    margin-left: 0;\n  }\n  @media (max-width: @screen-xs-max) {\n    margin-left: 0;\n  }\n  //When opening the sidebar on large screens\n  .sidebar-collapse & {\n    @media (min-width: @screen-sm) {\n      margin-left: 0;\n    }\n  }\n  //When opening the sidebar on small screens\n  .sidebar-open & {\n    @media (max-width: @screen-xs-max) {\n      .translate(@sidebar-width, 0);\n    }\n  }\n}\n\n.content-wrapper,\n.right-side {\n  min-height: 100%;\n  background-color: @body-bg;\n  z-index: 800;\n}\n\n.main-footer {\n  background: #fff;\n  padding: 15px;\n  color: #444;\n  border-top: 1px solid @gray-lte;\n}\n\n/* Fixed layout */\n.fixed {\n  .main-header,\n  .main-sidebar,\n  .left-side {\n    position: fixed;\n  }\n  .main-header {\n    top: 0;\n    right: 0;\n    left: 0;\n  }\n  .content-wrapper,\n  .right-side {\n    padding-top: 50px;\n    @media (max-width: @screen-header-collapse) {\n      padding-top: 100px;\n    }\n  }\n  &.layout-boxed {\n    .wrapper {\n      max-width: 100%;\n    }\n  }\n}\n\nbody.hold-transition {\n  .content-wrapper,\n  .right-side,\n  .main-footer,\n  .main-sidebar,\n  .left-side,\n  .main-header .navbar,\n  .main-header .logo {\n    /* Fix for IE */\n    .transition(none);\n  }\n}\n\n/* Content */\n.content {\n  min-height: 250px;\n  padding: 15px;\n  .container-fixed(@grid-gutter-width);\n}\n\n/* H1 - H6 font */\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\n.h1,\n.h2,\n.h3,\n.h4,\n.h5,\n.h6 {\n  font-family: 'Source Sans Pro', sans-serif;\n}\n\n/* General Links */\na {\n  color: @link-color;\n}\n\na:hover,\na:active,\na:focus {\n  outline: none;\n  text-decoration: none;\n  color: @link-hover-color;\n}\n\n/* Page Header */\n.page-header {\n  margin: 10px 0 20px 0;\n  font-size: 22px;\n\n  > small {\n    color: #666;\n    display: block;\n    margin-top: 5px;\n  }\n}\n", "// Clearfix\n//\n// For modern browsers\n// 1. The space content is one way to avoid an Opera bug when the\n//    contenteditable attribute is included anywhere else in the document.\n//    Otherwise it causes space to appear at the top and bottom of elements\n//    that are clearfixed.\n// 2. The use of `table` rather than `block` is only necessary if using\n//    `:before` to contain the top-margins of child elements.\n//\n// Source: http://nicolasgallagher.com/micro-clearfix-hack/\n\n.clearfix() {\n  &:before,\n  &:after {\n    content: \" \"; // 1\n    display: table; // 2\n  }\n  &:after {\n    clear: both;\n  }\n}\n", "// Vendor Prefixes\n//\n// All vendor mixins are deprecated as of v3.2.0 due to the introduction of\n// Autoprefixer in our Gruntfile. They will be removed in v4.\n\n// - Animations\n// - Backface visibility\n// - Box shadow\n// - Box sizing\n// - Content columns\n// - Hyphens\n// - Placeholder text\n// - Transformations\n// - Transitions\n// - User Select\n\n// Animations\n.animation(@animation) {\n  -webkit-animation: @animation;\n  -o-animation: @animation;\n  animation: @animation;\n}\n\n.animation-name(@name) {\n  -webkit-animation-name: @name;\n  animation-name: @name;\n}\n\n.animation-duration(@duration) {\n  -webkit-animation-duration: @duration;\n  animation-duration: @duration;\n}\n\n.animation-timing-function(@timing-function) {\n  -webkit-animation-timing-function: @timing-function;\n  animation-timing-function: @timing-function;\n}\n\n.animation-delay(@delay) {\n  -webkit-animation-delay: @delay;\n  animation-delay: @delay;\n}\n\n.animation-iteration-count(@iteration-count) {\n  -webkit-animation-iteration-count: @iteration-count;\n  animation-iteration-count: @iteration-count;\n}\n\n.animation-direction(@direction) {\n  -webkit-animation-direction: @direction;\n  animation-direction: @direction;\n}\n\n.animation-fill-mode(@fill-mode) {\n  -webkit-animation-fill-mode: @fill-mode;\n  animation-fill-mode: @fill-mode;\n}\n\n// Backface visibility\n// Prevent browsers from flickering when using CSS 3D transforms.\n// Default value is `visible`, but can be changed to `hidden`\n\n.backface-visibility(@visibility) {\n  -webkit-backface-visibility: @visibility;\n  -moz-backface-visibility: @visibility;\n  backface-visibility: @visibility;\n}\n\n// Drop shadows\n//\n// Note: Deprecated `.box-shadow()` as of v3.1.0 since all of Bootstrap's\n// supported browsers that have box shadow capabilities now support it.\n\n.box-shadow(@shadow) {\n  -webkit-box-shadow: @shadow; // iOS <4.3 & Android <4.1\n  box-shadow: @shadow;\n}\n\n// Box sizing\n.box-sizing(@boxmodel) {\n  -webkit-box-sizing: @boxmodel;\n  -moz-box-sizing: @boxmodel;\n  box-sizing: @boxmodel;\n}\n\n// CSS3 Content Columns\n.content-columns(@column-count; @column-gap: @grid-gutter-width) {\n  -webkit-column-count: @column-count;\n  -moz-column-count: @column-count;\n  column-count: @column-count;\n  -webkit-column-gap: @column-gap;\n  -moz-column-gap: @column-gap;\n  column-gap: @column-gap;\n}\n\n// Optional hyphenation\n.hyphens(@mode: auto) {\n  word-wrap: break-word;\n  -webkit-hyphens: @mode;\n  -moz-hyphens: @mode;\n  -ms-hyphens: @mode; // IE10+\n  -o-hyphens: @mode;\n  hyphens: @mode;\n}\n\n// Placeholder text\n.placeholder(@color: @input-color-placeholder) {\n  // Firefox\n  &::-moz-placeholder {\n    color: @color;\n    opacity: 1; // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526\n  }\n  &:-ms-input-placeholder {\n    color: @color;\n  }\n  // Internet Explorer 10+\n  &::-webkit-input-placeholder {\n    color: @color;\n  }\n  // Safari and Chrome\n}\n\n// Transformations\n.scale(@ratio) {\n  -webkit-transform: scale(@ratio);\n  -ms-transform: scale(@ratio); // IE9 only\n  -o-transform: scale(@ratio);\n  transform: scale(@ratio);\n}\n\n.scale(@ratioX; @ratioY) {\n  -webkit-transform: scale(@ratioX, @ratioY);\n  -ms-transform: scale(@ratioX, @ratioY); // IE9 only\n  -o-transform: scale(@ratioX, @ratioY);\n  transform: scale(@ratioX, @ratioY);\n}\n\n.scaleX(@ratio) {\n  -webkit-transform: scaleX(@ratio);\n  -ms-transform: scaleX(@ratio); // IE9 only\n  -o-transform: scaleX(@ratio);\n  transform: scaleX(@ratio);\n}\n\n.scaleY(@ratio) {\n  -webkit-transform: scaleY(@ratio);\n  -ms-transform: scaleY(@ratio); // IE9 only\n  -o-transform: scaleY(@ratio);\n  transform: scaleY(@ratio);\n}\n\n.skew(@x; @y) {\n  -webkit-transform: skewX(@x) skewY(@y);\n  -ms-transform: skewX(@x) skewY(@y); // See https://github.com/twbs/bootstrap/issues/4885; IE9+\n  -o-transform: skewX(@x) skewY(@y);\n  transform: skewX(@x) skewY(@y);\n}\n\n.translate(@x; @y) {\n  -webkit-transform: translate(@x, @y);\n  -ms-transform: translate(@x, @y); // IE9 only\n  -o-transform: translate(@x, @y);\n  transform: translate(@x, @y);\n}\n\n.translate3d(@x; @y; @z) {\n  -webkit-transform: translate3d(@x, @y, @z);\n  transform: translate3d(@x, @y, @z);\n}\n\n.rotate(@degrees) {\n  -webkit-transform: rotate(@degrees);\n  -ms-transform: rotate(@degrees); // IE9 only\n  -o-transform: rotate(@degrees);\n  transform: rotate(@degrees);\n}\n\n.rotateX(@degrees) {\n  -webkit-transform: rotateX(@degrees);\n  -ms-transform: rotateX(@degrees); // IE9 only\n  -o-transform: rotateX(@degrees);\n  transform: rotateX(@degrees);\n}\n\n.rotateY(@degrees) {\n  -webkit-transform: rotateY(@degrees);\n  -ms-transform: rotateY(@degrees); // IE9 only\n  -o-transform: rotateY(@degrees);\n  transform: rotateY(@degrees);\n}\n\n.perspective(@perspective) {\n  -webkit-perspective: @perspective;\n  -moz-perspective: @perspective;\n  perspective: @perspective;\n}\n\n.perspective-origin(@perspective) {\n  -webkit-perspective-origin: @perspective;\n  -moz-perspective-origin: @perspective;\n  perspective-origin: @perspective;\n}\n\n.transform-origin(@origin) {\n  -webkit-transform-origin: @origin;\n  -moz-transform-origin: @origin;\n  -ms-transform-origin: @origin; // IE9 only\n  transform-origin: @origin;\n}\n\n// Transitions\n\n.transition(@transition) {\n  -webkit-transition: @transition;\n  -o-transition: @transition;\n  transition: @transition;\n}\n\n.transition-property(@transition-property) {\n  -webkit-transition-property: @transition-property;\n  transition-property: @transition-property;\n}\n\n.transition-delay(@transition-delay) {\n  -webkit-transition-delay: @transition-delay;\n  transition-delay: @transition-delay;\n}\n\n.transition-duration(@transition-duration) {\n  -webkit-transition-duration: @transition-duration;\n  transition-duration: @transition-duration;\n}\n\n.transition-timing-function(@timing-function) {\n  -webkit-transition-timing-function: @timing-function;\n  transition-timing-function: @timing-function;\n}\n\n.transition-transform(@transition) {\n  -webkit-transition: -webkit-transform @transition;\n  -moz-transition: -moz-transform @transition;\n  -o-transition: -o-transform @transition;\n  transition: transform @transition;\n}\n\n// User select\n// For selecting text on the page\n\n.user-select(@select) {\n  -webkit-user-select: @select;\n  -moz-user-select: @select;\n  -ms-user-select: @select; // IE10+\n  user-select: @select;\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n// Centered container element\n.container-fixed(@gutter: @grid-gutter-width) {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left: (@gutter / 2);\n  padding-right: (@gutter / 2);\n  &:extend(.clearfix all);\n}\n\n// Creates a wrapper for a series of columns\n.make-row(@gutter: @grid-gutter-width) {\n  margin-left: ceil((@gutter / -2));\n  margin-right: floor((@gutter / -2));\n  &:extend(.clearfix all);\n}\n\n// Generate the extra small columns\n.make-xs-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  float: left;\n  width: percentage((@columns / @grid-columns));\n  min-height: 1px;\n  padding-left: (@gutter / 2);\n  padding-right: (@gutter / 2);\n}\n\n.make-xs-column-offset(@columns) {\n  margin-left: percentage((@columns / @grid-columns));\n}\n\n.make-xs-column-push(@columns) {\n  left: percentage((@columns / @grid-columns));\n}\n\n.make-xs-column-pull(@columns) {\n  right: percentage((@columns / @grid-columns));\n}\n\n// Generate the small columns\n.make-sm-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left: (@gutter / 2);\n  padding-right: (@gutter / 2);\n\n  @media (min-width: @screen-sm-min) {\n    float: left;\n    width: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-sm-column-offset(@columns) {\n  @media (min-width: @screen-sm-min) {\n    margin-left: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-sm-column-push(@columns) {\n  @media (min-width: @screen-sm-min) {\n    left: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-sm-column-pull(@columns) {\n  @media (min-width: @screen-sm-min) {\n    right: percentage((@columns / @grid-columns));\n  }\n}\n\n// Generate the medium columns\n.make-md-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left: (@gutter / 2);\n  padding-right: (@gutter / 2);\n\n  @media (min-width: @screen-md-min) {\n    float: left;\n    width: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-md-column-offset(@columns) {\n  @media (min-width: @screen-md-min) {\n    margin-left: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-md-column-push(@columns) {\n  @media (min-width: @screen-md-min) {\n    left: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-md-column-pull(@columns) {\n  @media (min-width: @screen-md-min) {\n    right: percentage((@columns / @grid-columns));\n  }\n}\n\n// Generate the large columns\n.make-lg-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left: (@gutter / 2);\n  padding-right: (@gutter / 2);\n\n  @media (min-width: @screen-lg-min) {\n    float: left;\n    width: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-lg-column-offset(@columns) {\n  @media (min-width: @screen-lg-min) {\n    margin-left: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-lg-column-push(@columns) {\n  @media (min-width: @screen-lg-min) {\n    left: percentage((@columns / @grid-columns));\n  }\n}\n\n.make-lg-column-pull(@columns) {\n  @media (min-width: @screen-lg-min) {\n    right: percentage((@columns / @grid-columns));\n  }\n}\n", "/*\n * Component: Main Header\n * ----------------------\n */\n\n.main-header {\n  position: relative;\n  max-height: 100px;\n  z-index: 1030;\n  //Navbar\n  .navbar {\n    .transition(margin-left @transition-speed @transition-fn);\n    margin-bottom: 0;\n    margin-left: @sidebar-width;\n    border: none;\n    min-height: @navbar-height;\n    border-radius: 0;\n    .layout-top-nav & {\n      margin-left: 0;\n    }\n  }\n  //Navbar search text input\n  #navbar-search-input.form-control {\n    background: rgba(255, 255, 255, .2);\n    border-color: transparent;\n    &:focus,\n    &:active {\n      border-color: rgba(0, 0, 0, .1);\n      background: rgba(255, 255, 255, .9);\n    }\n    &::-moz-placeholder {\n      color: #ccc;\n      opacity: 1;\n    }\n    &:-ms-input-placeholder {\n      color: #ccc;\n    }\n    &::-webkit-input-placeholder {\n      color: #ccc;\n    }\n  }\n  //Navbar Right Menu\n  .navbar-custom-menu,\n  .navbar-right {\n    float: right;\n    @media (max-width: @screen-sm-max) {\n      a {\n        color: inherit;\n        background: transparent;\n      }\n    }\n  }\n  .navbar-right {\n    @media (max-width: @screen-header-collapse) {\n      float: none;\n      .navbar-collapse & {\n        margin: 7.5px -15px;\n      }\n\n      > li {\n        color: inherit;\n        border: 0;\n      }\n    }\n  }\n  //Navbar toggle button\n  .sidebar-toggle {\n    float: left;\n    background-color: transparent;\n    background-image: none;\n    padding: @navbar-padding-vertical @navbar-padding-horizontal;\n    //Add the fontawesome bars icon\n    font-family: fontAwesome;\n    &:before {\n      content: \"\\f0c9\";\n    }\n    &:hover {\n      color: #fff;\n    }\n    &:focus,\n    &:active {\n      background: transparent;\n    }\n  }\n  .sidebar-toggle .icon-bar {\n    display: none;\n  }\n  //Navbar User Menu\n  .navbar .nav > li.user > a {\n    > .fa,\n    > .glyphicon,\n    > .ion {\n      margin-right: 5px;\n    }\n  }\n\n  //Labels in navbar\n  .navbar .nav > li > a > .label {\n    position: absolute;\n    top: 9px;\n    right: 7px;\n    text-align: center;\n    font-size: 9px;\n    padding: 2px 3px;\n    line-height: .9;\n  }\n\n  //Logo bar\n  .logo {\n    .transition(width @transition-speed @transition-fn);\n    display: block;\n    float: left;\n    height: @navbar-height;\n    font-size: 20px;\n    line-height: 50px;\n    text-align: center;\n    width: @sidebar-width;\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n    padding: 0 15px;\n    font-weight: 300;\n    overflow: hidden;\n    //Add support to sidebar mini by allowing the user to create\n    //2 logo designs. mini and lg\n    .logo-lg {\n      //should be visibile when sidebar isn't collapsed\n      display: block;\n    }\n    .logo-mini {\n      display: none;\n    }\n  }\n  //Navbar Brand. Alternative logo with layout-top-nav\n  .navbar-brand {\n    color: #fff;\n  }\n}\n\n// Content Header\n.content-header {\n  position: relative;\n  padding: 15px 15px 0 15px;\n  // Header Text\n  > h1 {\n    margin: 0;\n    font-size: 24px;\n    > small {\n      font-size: 15px;\n      display: inline-block;\n      padding-left: 4px;\n      font-weight: 300;\n    }\n  }\n\n  > .breadcrumb {\n    float: right;\n    background: transparent;\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 12px;\n    padding: 7px 5px;\n    position: absolute;\n    top: 15px;\n    right: 10px;\n    .border-radius(2px);\n    > li > a {\n      color: #444;\n      text-decoration: none;\n      display: inline-block;\n      > .fa, > .glyphicon, > .ion {\n        margin-right: 5px;\n      }\n    }\n    > li + li:before {\n      content: '>\\00a0';\n    }\n  }\n\n  @media (max-width: @screen-sm-max) {\n    > .breadcrumb {\n      position: relative;\n      margin-top: 5px;\n      top: 0;\n      right: 0;\n      float: none;\n      background: @gray-lte;\n      padding-left: 10px;\n      li:before {\n        color: darken(@gray-lte, 20%);\n      }\n    }\n  }\n}\n\n.navbar-toggle {\n  color: #fff;\n  border: 0;\n  margin: 0;\n  padding: @navbar-padding-vertical @navbar-padding-horizontal;\n}\n\n//Control navbar scaffolding on x-small screens\n@media (max-width: @screen-sm-max) {\n  .navbar-custom-menu .navbar-nav > li {\n    float: left;\n  }\n\n  //Dont't let links get full width\n  .navbar-custom-menu .navbar-nav {\n    margin: 0;\n    float: left;\n  }\n\n  .navbar-custom-menu .navbar-nav > li > a {\n    padding-top: 15px;\n    padding-bottom: 15px;\n    line-height: 20px;\n  }\n}\n\n// Collapse header\n@media (max-width: @screen-header-collapse) {\n  .main-header {\n    position: relative;\n    .logo,\n    .navbar {\n      width: 100%;\n      float: none;\n    }\n    .navbar {\n      margin: 0;\n    }\n    .navbar-custom-menu {\n      float: right;\n    }\n  }\n}\n\n.navbar-collapse.pull-left {\n  @media (max-width: @screen-sm-max) {\n    float: none !important;\n    + .navbar-custom-menu {\n      display: block;\n      position: absolute;\n      top: 0;\n      right: 40px;\n    }\n  }\n}\n", "//AdminLTE mixins\n//===============\n\n//Changes the color and the hovering properties of the navbar\n.navbar-variant(@color; @font-color: rgba(255, 255, 255, 0.8); @hover-color: #f6f6f6; @hover-bg: rgba(0, 0, 0, 0.1)) {\n  background-color: @color;\n  //Navbar links\n  .nav > li > a {\n    color: @font-color;\n  }\n\n  .nav > li > a:hover,\n  .nav > li > a:active,\n  .nav > li > a:focus,\n  .nav .open > a,\n  .nav .open > a:hover,\n  .nav .open > a:focus,\n  .nav > .active > a {\n    background: @hover-bg;\n    color: @hover-color;\n  }\n\n  //Add color to the sidebar toggle button\n  .sidebar-toggle {\n    color: @font-color;\n    &:hover {\n      color: @hover-color;\n      background: @hover-bg;\n    }\n  }\n}\n\n//Logo color variation\n.logo-variant(@bg-color; @color: #fff; @border-bottom-color: transparent; @border-bottom-width: 0) {\n  background-color: @bg-color;\n  color: @color;\n  border-bottom: @border-bottom-width solid @border-bottom-color;\n\n  &:hover {\n    background-color: darken(@bg-color, 1%);\n  }\n}\n\n//Box solid color variantion creator\n.box-solid-variant(@color; @text-color: #fff) {\n  border: 1px solid @color;\n  > .box-header {\n    color: @text-color;\n    background: @color;\n    background-color: @color;\n    a,\n    .btn {\n      color: @text-color;\n    }\n  }\n}\n\n//Direct Chat Variant\n.direct-chat-variant(@bg-color; @color: #fff) {\n  .right > .direct-chat-text {\n    background: @bg-color;\n    border-color: @bg-color;\n    color: @color;\n    &:after,\n    &:before {\n      border-left-color: @bg-color;\n    }\n  }\n}\n\n//border radius creator\n.border-radius(@radius) {\n  border-radius: @radius;\n}\n\n//Different radius each side\n.border-radius(@top-left;\n@top-right\n;\n@bottom-left\n;\n@bottom-right\n)\n{\n  border-top-left-radius: @top-left\n;\n  border-top-right-radius: @top-right\n;\n  border-bottom-right-radius: @bottom-right\n;\n  border-bottom-left-radius: @bottom-left\n;\n}\n\n//Gradient background\n.gradient(@color: #F5F5F5, @start: #EEE, @stop: #FFF) {\n  background: @color;\n  background: -webkit-gradient(linear,\n  left bottom,\n  left top,\n  color-stop(0, @start),\n  color-stop(1, @stop));\n  background: -ms-linear-gradient(bottom,\n  @start,\n  @stop);\n  background: -moz-linear-gradient(center bottom,\n  @start 0%,\n  @stop 100%);\n  background: -o-linear-gradient(@stop,\n  @start);\n  filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)\",@stop,@start));\n}\n\n//Added 2.1.0\n//Skins Mixins\n\n//Dark Sidebar Mixin\n.skin-dark-sidebar(@link-hover-border-color) {\n  // Sidebar background color (Both .wrapper and .left-side are responsible for sidebar bg color)\n  .wrapper,\n  .main-sidebar,\n  .left-side {\n    background-color: @sidebar-dark-bg;\n  }\n  //User Panel (resides in the sidebar)\n  .user-panel {\n    > .info, > .info > a {\n      color: #fff;\n    }\n  }\n  //Sidebar Menu. First level links\n  .sidebar-menu > li {\n    //Section Headning\n    &.header {\n      color: lighten(@sidebar-dark-bg, 20%);\n      background: darken(@sidebar-dark-bg, 4%);\n    }\n    //links\n    > a {\n      border-left: 3px solid transparent;\n    }\n    //Hover and active states\n    &:hover > a, &.active > a {\n      color: @sidebar-dark-hover-color;\n      background: @sidebar-dark-hover-bg;\n      border-left-color: @link-hover-border-color;\n    }\n    //First Level Submenu\n    > .treeview-menu {\n      margin: 0 1px;\n      background: @sidebar-dark-submenu-bg;\n    }\n  }\n  //All links within the sidebar menu\n  .sidebar a {\n    color: @sidebar-dark-color;\n    &:hover {\n      text-decoration: none;\n    }\n  }\n  //All submenus\n  .treeview-menu {\n    > li {\n      > a {\n        color: @sidebar-dark-submenu-color;\n      }\n      &.active > a, > a:hover {\n        color: @sidebar-dark-submenu-hover-color;\n      }\n    }\n  }\n  //The sidebar search form\n  .sidebar-form {\n    .border-radius(3px);\n    border: 1px solid lighten(@sidebar-dark-bg, 10%);\n    margin: 10px 10px;\n    input[type=\"text\"], .btn {\n      box-shadow: none;\n      background-color: lighten(@sidebar-dark-bg, 10%);\n      border: 1px solid transparent;\n      height: 35px;\n      //.transition(all @transition-speed @transition-fn);\n    }\n    input[type=\"text\"] {\n      color: #666;\n      .border-radius(2px, 0, 2px, 0);\n      &:focus,\n      &:focus + .input-group-btn .btn {\n        background-color: #fff;\n        color: #666;\n      }\n      &:focus + .input-group-btn .btn {\n        border-left-color: #fff;\n\n      }\n    }\n    .btn {\n      color: #999;\n      .border-radius(0, 2px, 0, 2px);\n    }\n  }\n}\n\n//Light Sidebar Mixin\n.skin-light-sidebar(@icon-active-color) {\n  // Sidebar background color (Both .wrapper and .left-side are responsible for sidebar bg color)\n  .wrapper,\n  .main-sidebar,\n  .left-side {\n    background-color: @sidebar-light-bg;\n  }\n  .content-wrapper,\n  .main-footer {\n    border-left: 1px solid @gray-lte;\n  }\n  //User Panel (resides in the sidebar)\n  .user-panel {\n    > .info, > .info > a {\n      color: @sidebar-light-color;\n    }\n  }\n  //Sidebar Menu. First level links\n  .sidebar-menu > li {\n    .transition(border-left-color .3s ease);\n    //border-left: 3px solid transparent;\n    //Section Headning\n    &.header {\n      color: lighten(@sidebar-light-color, 25%);\n      background: @sidebar-light-bg;\n    }\n    //links\n    > a {\n      border-left: 3px solid transparent;\n      font-weight: 600;\n    }\n    //Hover and active states\n    &:hover > a,\n    &.active > a {\n      color: @sidebar-light-hover-color;\n      background: @sidebar-light-hover-bg;\n    }\n    &:hover > a {\n\n    }\n    &.active {\n      border-left-color: @icon-active-color;\n      > a {\n        font-weight: 600;\n      }\n    }\n    //First Level Submenu\n    > .treeview-menu {\n      background: @sidebar-light-submenu-bg;\n    }\n  }\n  //All links within the sidebar menu\n  .sidebar a {\n    color: @sidebar-light-color;\n    &:hover {\n      text-decoration: none;\n    }\n  }\n  //All submenus\n  .treeview-menu {\n    > li {\n      > a {\n        color: @sidebar-light-submenu-color;\n      }\n      &.active > a,\n      > a:hover {\n        color: @sidebar-light-submenu-hover-color;\n      }\n      &.active > a {\n        font-weight: 600;\n      }\n    }\n  }\n  //The sidebar search form\n  .sidebar-form {\n    .border-radius(3px);\n    border: 1px solid @gray-lte;  //darken(@sidebar-light-bg, 5%);\n    margin: 10px 10px;\n    input[type=\"text\"],\n    .btn {\n      box-shadow: none;\n      background-color: #fff; //darken(@sidebar-light-bg, 3%);\n      border: 1px solid transparent;\n      height: 35px;\n      //.transition(all @transition-speed @transition-fn);\n    }\n    input[type=\"text\"] {\n      color: #666;\n      .border-radius(2px, 0, 2px, 0);\n      &:focus,\n      &:focus + .input-group-btn .btn {\n        background-color: #fff;\n        color: #666;\n      }\n      &:focus + .input-group-btn .btn {\n        border-left-color: #fff;\n      }\n    }\n    .btn {\n      color: #999;\n      .border-radius(0, 2px, 0, 2px);\n    }\n  }\n  @media (min-width: @screen-sm-min) {\n    &.sidebar-mini.sidebar-collapse {\n      .sidebar-menu > li > .treeview-menu {\n        border-left: 1px solid @gray-lte;\n      }\n    }\n  }\n}\n", "/*\n * Component: Sidebar\n * ------------------\n */\n//Main Sidebar\n// ``` .left-side has been deprecated as of 2.0.0 in favor of .main-sidebar ```\n\n.main-sidebar,\n.left-side {\n  position: absolute;\n  top: 0;\n  left: 0;\n  padding-top: 50px;\n  min-height: 100%;\n  width: @sidebar-width;\n  z-index: 810;\n  //Using disposable variable to join statements with a comma\n  @transition-rule: @transition-speed @transition-fn,\n  width @transition-speed @transition-fn;\n  .transition-transform(@transition-rule);\n  @media (max-width: @screen-header-collapse) {\n    padding-top: 100px;\n  }\n  @media (max-width: @screen-xs-max) {\n    .translate(-@sidebar-width, 0);\n  }\n  .sidebar-collapse & {\n    @media (min-width: @screen-sm) {\n      .translate(-@sidebar-width, 0);\n    }\n  }\n  .sidebar-open & {\n    @media (max-width: @screen-xs-max) {\n      .translate(0, 0);\n    }\n  }\n}\n\n.sidebar {\n  padding-bottom: 10px;\n}\n\n// remove border from form\n.sidebar-form {\n  input:focus {\n    border-color: transparent;\n  }\n}\n\n//Sidebar user panel\n.user-panel {\n  position: relative;\n  width: 100%;\n  padding: 10px;\n  overflow: hidden;\n  .clearfix();\n  > .image > img {\n    width: 100%;\n    max-width: 45px;\n    height: auto;\n  }\n  > .info {\n    padding: 5px 5px 5px 15px;\n    line-height: 1;\n    position: absolute;\n    left: 55px;\n    > p {\n      font-weight: 600;\n      margin-bottom: 9px;\n    }\n    > a {\n      text-decoration: none;\n      padding-right: 5px;\n      margin-top: 3px;\n      font-size: 11px;\n      > .fa,\n      > .ion,\n      > .glyphicon {\n        margin-right: 3px;\n      }\n    }\n  }\n}\n\n// Sidebar menu\n.sidebar-menu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  //First Level\n  > li {\n    position: relative;\n    margin: 0;\n    padding: 0;\n    > a {\n      padding: 12px 5px 12px 15px;\n      display: block;\n      > .fa,\n      > .glyphicon,\n      > .ion {\n        width: 20px;\n      }\n    }\n    .label,\n    .badge {\n      margin-right: 5px;\n    }\n    .badge {\n      margin-top: 3px;\n    }\n  }\n  li.header {\n    padding: 10px 25px 10px 15px;\n    font-size: 12px;\n  }\n  li > a > .fa-angle-left,\n  li > a > .pull-right-container > .fa-angle-left {\n    width: auto;\n    height: auto;\n    padding: 0;\n    margin-right: 10px;\n  }\n  li > a > .fa-angle-left {\n    position: absolute;\n    top: 50%;\n    right: 10px;\n    margin-top: -8px;\n  }\n  li.active {\n    > a > .fa-angle-left,\n    > a > .pull-right-container > .fa-angle-left {\n      .rotate(-90deg);\n    }\n    > .treeview-menu {\n      display: block;\n    }\n  }\n\n  // Tree view menu\n  .treeview-menu {\n    display: none;\n    list-style: none;\n    padding: 0;\n    margin: 0;\n    padding-left: 5px;\n    .treeview-menu {\n      padding-left: 20px;\n    }\n    > li {\n      margin: 0;\n      > a {\n        padding: 5px 5px 5px 15px;\n        display: block;\n        font-size: 14px;\n        > .fa,\n        > .glyphicon,\n        > .ion {\n          width: 20px;\n        }\n        > .pull-right-container > .fa-angle-left,\n        > .pull-right-container > .fa-angle-down,\n        > .fa-angle-left,\n        > .fa-angle-down {\n          width: auto;\n        }\n      }\n    }\n  }\n}\n", "/*\n * Component: Sidebar Mini\n */\n\n//Add sidebar-mini class to the body tag to activate this feature\n.sidebar-mini {\n  //Sidebar mini should work only on devices larger than @screen-sm\n  @media (min-width: @screen-sm) {\n    //When the sidebar is collapsed...\n    &.sidebar-collapse {\n\n      //Apply the new margining to the main content and footer\n      .content-wrapper,\n      .right-side,\n      .main-footer {\n        margin-left: 50px !important;\n        z-index: 840;\n      }\n\n      //Modify the sidebar to shrink instead of disappearing\n      .main-sidebar {\n        //Don't go away! Just shrink\n        .translate(0, 0);\n        width: 50px !important;\n        z-index: 850;\n      }\n\n      .sidebar-menu {\n        > li {\n          position: relative;\n          > a {\n            margin-right: 0;\n          }\n          > a > span {\n            border-top-right-radius: 4px;\n          }\n\n          &:not(.treeview) {\n            > a > span {\n              border-bottom-right-radius: 4px;\n            }\n          }\n\n          > .treeview-menu {\n            //Add some padding to the treeview menu\n            padding-top: 5px;\n            padding-bottom: 5px;\n            border-bottom-right-radius: 4px;\n          }\n\n          //Show menu items on hover\n          &:hover {\n            > a {\n              //overflow: visible;\n            }\n            > a > span:not(.pull-right),//:not(.pull-right-container),\n            > .treeview-menu {\n              display: block !important;\n              position: absolute;\n              width: @sidebar-width - 50;\n              left: 50px;\n            }\n\n            //position the header & treeview menus\n            > a > span {\n              top: 0;\n              margin-left: -3px;\n              padding: 12px 5px 12px 20px;\n              background-color: inherit;\n            }\n            > a > .pull-right-container {\n              //display: block!important;\n              position: relative!important;\n              float: right;\n              width: auto!important;\n              left: 200px - 20px!important;\n              top: -22px!important;\n              z-index: 900;\n              > .label:not(:first-of-type) {\n                display: none;\n              }\n            }\n            > .treeview-menu {\n              top: 44px;\n              margin-left: 0;\n            }\n          }\n        }\n      }\n\n      //Make the sidebar links, menus, labels, badges\n      //and angle icons disappear\n      .main-sidebar .user-panel > .info,\n      .sidebar-form,\n      .sidebar-menu > li > a > span,\n      .sidebar-menu > li > .treeview-menu,\n      .sidebar-menu > li > a > .pull-right,\n      .sidebar-menu li.header {\n        display: none !important;\n        -webkit-transform: translateZ(0);\n      }\n\n      .main-header {\n        //Let's make the logo also shrink and the mini logo to appear\n        .logo {\n          width: 50px;\n          > .logo-mini {\n            display: block;\n            margin-left: -15px;\n            margin-right: -15px;\n            font-size: 18px;\n          }\n          > .logo-lg {\n            display: none;\n          }\n        }\n\n        //Since the logo got smaller, we need to fix the navbar's position\n        .navbar {\n          margin-left: 50px;\n        }\n      }\n    }\n  }\n}\n\n//A fix for text overflow while transitioning from sidebar mini to full sidebar\n.sidebar-menu,\n.main-sidebar .user-panel,\n.sidebar-menu > li.header {\n  white-space: nowrap;\n  overflow: hidden;\n}\n\n.sidebar-menu:hover {\n  overflow: visible;\n}\n\n.sidebar-form,\n.sidebar-menu > li.header {\n  overflow: hidden;\n  text-overflow: clip;\n}\n\n.sidebar-menu li > a {\n  position: relative;\n  > .pull-right-container {\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    margin-top: -7px;\n  }\n}\n", "/*\n * Component: Control sidebar. By default, this is the right sidebar.\n */\n//The sidebar's background control class\n//This is a hack to make the background visible while scrolling\n.control-sidebar-bg {\n  position: fixed;\n  z-index: 1000;\n  bottom: 0;\n}\n\n//Transitions\n.control-sidebar-bg,\n.control-sidebar {\n  top: 0;\n  right: -@control-sidebar-width;\n  width: @control-sidebar-width;\n  .transition(right @transition-speed ease-in-out);\n}\n\n//The sidebar\n.control-sidebar {\n  position: absolute;\n  padding-top: @navbar-height;\n  z-index: 1010;\n  //Fix position after header collapse\n  @media (max-width: @screen-sm) {\n    padding-top: @navbar-height + 50;\n  }\n  //Tab panes\n  > .tab-content {\n    padding: 10px 15px;\n  }\n  //Open state with slide over content effect\n  &.control-sidebar-open {\n    &,\n    + .control-sidebar-bg {\n      right: 0;\n    }\n  }\n}\n\n//Open without slide over content\n.control-sidebar-open {\n  .control-sidebar-bg,\n  .control-sidebar {\n    right: 0;\n  }\n  @media (min-width: @screen-sm) {\n    .content-wrapper,\n    .right-side,\n    .main-footer {\n      margin-right: @control-sidebar-width;\n    }\n  }\n}\n\n//Control sidebar tabs\n.nav-tabs.control-sidebar-tabs {\n  > li {\n    &:first-of-type > a {\n      &,\n      &:hover,\n      &:focus {\n        border-left-width: 0;\n      }\n    }\n    > a {\n      .border-radius(0);\n\n      //Hover and active states\n      &,\n      &:hover {\n        border-top: none;\n        border-right: none;\n        border-left: 1px solid transparent;\n        border-bottom: 1px solid transparent;\n      }\n      .icon {\n        font-size: 16px;\n      }\n    }\n    //Active state\n    &.active {\n      > a {\n        &,\n        &:hover,\n        &:focus,\n        &:active {\n          border-top: none;\n          border-right: none;\n          border-bottom: none;\n        }\n      }\n    }\n  }\n  //Remove responsiveness on small screens\n  @media (max-width: @screen-sm) {\n    display: table;\n    > li {\n      display: table-cell;\n    }\n  }\n}\n\n//Headings in the sidebar content\n.control-sidebar-heading {\n  font-weight: 400;\n  font-size: 16px;\n  padding: 10px 0;\n  margin-bottom: 10px;\n}\n\n//Subheadings\n.control-sidebar-subheading {\n  display: block;\n  font-weight: 400;\n  font-size: 14px;\n}\n\n//Control Sidebar Menu\n.control-sidebar-menu {\n  list-style: none;\n  padding: 0;\n  margin: 0 -15px;\n  > li > a {\n    .clearfix();\n    display: block;\n    padding: 10px 15px;\n    > .control-sidebar-subheading {\n      margin-top: 0;\n    }\n  }\n  .menu-icon {\n    float: left;\n    width: 35px;\n    height: 35px;\n    border-radius: 50%;\n    text-align: center;\n    line-height: 35px;\n  }\n  .menu-info {\n    margin-left: 45px;\n    margin-top: 3px;\n    > .control-sidebar-subheading {\n      margin: 0;\n    }\n    > p {\n      margin: 0;\n      font-size: 11px;\n    }\n  }\n  .progress {\n    margin: 0;\n  }\n}\n\n//Dark skin\n.control-sidebar-dark {\n  color: @sidebar-dark-color;\n  // Background\n  &,\n  + .control-sidebar-bg {\n    background: @sidebar-dark-bg;\n  }\n  // Sidebar tabs\n  .nav-tabs.control-sidebar-tabs {\n    border-bottom: darken(@sidebar-dark-bg, 3%);\n    > li {\n      > a {\n        background: darken(@sidebar-dark-bg, 5%);\n        color: @sidebar-dark-color;\n        //Hover and active states\n        &,\n        &:hover,\n        &:focus {\n          border-left-color: darken(@sidebar-dark-bg, 7%);\n          border-bottom-color: darken(@sidebar-dark-bg, 7%);\n        }\n        &:hover,\n        &:focus,\n        &:active {\n          background: darken(@sidebar-dark-bg, 3%);\n        }\n        &:hover {\n          color: #fff;\n        }\n      }\n      //Active state\n      &.active {\n        > a {\n          &,\n          &:hover,\n          &:focus,\n          &:active {\n            background: @sidebar-dark-bg;\n            color: #fff;\n          }\n        }\n      }\n    }\n  }\n  //Heading & subheading\n  .control-sidebar-heading,\n  .control-sidebar-subheading {\n    color: #fff;\n  }\n  //Sidebar list\n  .control-sidebar-menu {\n    > li {\n      > a {\n        &:hover {\n          background: @sidebar-dark-hover-bg;\n        }\n        .menu-info {\n          > p {\n            color: @sidebar-dark-color;\n          }\n        }\n      }\n    }\n  }\n}\n\n//Light skin\n.control-sidebar-light {\n  color: lighten(@sidebar-light-color, 10%);\n  // Background\n  &,\n  + .control-sidebar-bg {\n    background: @sidebar-light-bg;\n    border-left: 1px solid @gray-lte;\n  }\n  // Sidebar tabs\n  .nav-tabs.control-sidebar-tabs {\n    border-bottom: @gray-lte;\n    > li {\n      > a {\n        background: darken(@sidebar-light-bg, 5%);\n        color: @sidebar-light-color;\n        //Hover and active states\n        &,\n        &:hover,\n        &:focus {\n          border-left-color: @gray-lte;\n          border-bottom-color: @gray-lte;\n        }\n        &:hover,\n        &:focus,\n        &:active {\n          background: darken(@sidebar-light-bg, 3%);\n        }\n      }\n      //Active state\n      &.active {\n        > a {\n          &,\n          &:hover,\n          &:focus,\n          &:active {\n            background: @sidebar-light-bg;\n            color: #111;\n          }\n        }\n      }\n    }\n  }\n  //Heading & subheading\n  .control-sidebar-heading,\n  .control-sidebar-subheading {\n    color: #111;\n  }\n  //Sidebar list\n  .control-sidebar-menu {\n    margin-left: -14px;\n    > li {\n      > a {\n        &:hover {\n          background: @sidebar-light-hover-bg;\n        }\n        .menu-info {\n          > p {\n            color: lighten(@sidebar-light-color, 10%);\n          }\n        }\n      }\n    }\n  }\n}\n", "/*\n * Component: Dropdown menus\n * -------------------------\n */\n\n/*Dropdowns in general*/\n.dropdown-menu {\n  box-shadow: none;\n  border-color: #eee;\n  > li > a {\n    color: #777;\n  }\n  > li > a > .glyphicon,\n  > li > a > .fa,\n  > li > a > .ion {\n    margin-right: 10px;\n  }\n  > li > a:hover {\n    background-color: lighten(@gray-lte, 5%);\n    color: #333;\n  }\n  > .divider {\n    background-color: #eee;\n  }\n}\n\n//Navbar custom dropdown menu\n.navbar-nav > .notifications-menu,\n.navbar-nav > .messages-menu,\n.navbar-nav > .tasks-menu {\n  //fix width and padding\n  > .dropdown-menu {\n    > li {\n      position: relative;\n    }\n    width: 280px;\n    //Remove padding and margins\n    padding: 0 0 0 0;\n    margin: 0;\n    top: 100%;\n  }\n  //Define header class\n  > .dropdown-menu > li.header {\n    .border-radius(4px; 4px; 0; 0);\n    background-color: #ffffff;\n    padding: 7px 10px;\n    border-bottom: 1px solid #f4f4f4;\n    color: #444444;\n    font-size: 14px;\n  }\n\n  //Define footer class\n  > .dropdown-menu > li.footer > a {\n    .border-radius(0; 0; 4px; 4px);\n    font-size: 12px;\n    background-color: #fff;\n    padding: 7px 10px;\n    border-bottom: 1px solid #eeeeee;\n    color: #444 !important;\n    @media (max-width: @screen-sm-max) {\n      background: #fff !important;\n      color: #444 !important;\n    }\n    text-align: center;\n    //Hover state\n    &:hover {\n      text-decoration: none;\n      font-weight: normal;\n    }\n  }\n\n  //Clear inner menu padding and margins\n  > .dropdown-menu > li .menu {\n    max-height: 200px;\n    margin: 0;\n    padding: 0;\n    list-style: none;\n    overflow-x: hidden;\n    > li > a {\n      display: block;\n      white-space: nowrap; /* Prevent text from breaking */\n      border-bottom: 1px solid #f4f4f4;\n      // Hove state\n      &:hover {\n        background: #f4f4f4;\n        text-decoration: none;\n      }\n    }\n  }\n}\n\n//Notifications menu\n.navbar-nav > .notifications-menu {\n  > .dropdown-menu > li .menu {\n    // Links inside the menu\n    > li > a {\n      color: #444444;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      padding: 10px;\n      // Icons inside the menu\n      > .glyphicon,\n      > .fa,\n      > .ion {\n        width: 20px;\n      }\n    }\n\n  }\n}\n\n//Messages menu\n.navbar-nav > .messages-menu {\n  //Inner menu\n  > .dropdown-menu > li .menu {\n    // Messages menu item\n    > li > a {\n      margin: 0;\n      //line-height: 20px;\n      padding: 10px 10px;\n      // User image\n      > div > img {\n        margin: auto 10px auto auto;\n        width: 40px;\n        height: 40px;\n      }\n      // Message heading\n      > h4 {\n        padding: 0;\n        margin: 0 0 0 45px;\n        color: #444444;\n        font-size: 15px;\n        position: relative;\n        // Small for message time display\n        > small {\n          color: #999999;\n          font-size: 10px;\n          position: absolute;\n          top: 0;\n          right: 0;\n        }\n      }\n\n      > p {\n        margin: 0 0 0 45px;\n        font-size: 12px;\n        color: #888888;\n      }\n\n      .clearfix();\n\n    }\n\n  }\n}\n\n//Tasks menu\n.navbar-nav > .tasks-menu {\n  > .dropdown-menu > li .menu {\n    > li > a {\n      padding: 10px;\n\n      > h3 {\n        font-size: 14px;\n        padding: 0;\n        margin: 0 0 10px 0;\n        color: #666666;\n      }\n\n      > .progress {\n        padding: 0;\n        margin: 0;\n      }\n    }\n  }\n}\n\n//User menu\n.navbar-nav > .user-menu {\n  > .dropdown-menu {\n    .border-top-radius(0);\n    padding: 1px 0 0 0;\n    border-top-width: 0;\n    width: 280px;\n\n    &,\n    > .user-body {\n      .border-bottom-radius(4px);\n    }\n    // Header menu\n    > li.user-header {\n      height: 175px;\n      padding: 10px;\n      text-align: center;\n      // User image\n      > img {\n        z-index: 5;\n        height: 90px;\n        width: 90px;\n        border: 3px solid;\n        border-color: transparent;\n        border-color: rgba(255, 255, 255, 0.2);\n      }\n      > p {\n        z-index: 5;\n        color: #fff;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 17px;\n        //text-shadow: 2px 2px 3px #333333;\n        margin-top: 10px;\n        > small {\n          display: block;\n          font-size: 12px;\n        }\n      }\n    }\n\n    // Menu Body\n    > .user-body {\n      padding: 15px;\n      border-bottom: 1px solid #f4f4f4;\n      border-top: 1px solid #dddddd;\n      .clearfix();\n      a {\n        color: #444 !important;\n        @media (max-width: @screen-sm-max) {\n          background: #fff !important;\n          color: #444 !important;\n        }\n      }\n    }\n\n    // Menu Footer\n    > .user-footer {\n      background-color: #f9f9f9;\n      padding: 10px;\n      .clearfix();\n      .btn-default {\n        color: #666666;\n        &:hover {\n          @media (max-width: @screen-sm-max) {\n            background-color: #f9f9f9;\n          }\n        }\n      }\n    }\n  }\n  .user-image {\n    float: left;\n    width: 25px;\n    height: 25px;\n    border-radius: 50%;\n    margin-right: 10px;\n    margin-top: -2px;\n    @media (max-width: @screen-xs-max) {\n      float: none;\n      margin-right: 0;\n      margin-top: -8px;\n      line-height: 10px;\n    }\n  }\n}\n\n/* Add fade animation to dropdown menus by appending\n the class .animated-dropdown-menu to the .dropdown-menu ul (or ol)*/\n.open:not(.dropup) > .animated-dropdown-menu {\n  backface-visibility: visible !important;\n  .animation(flipInX .7s both);\n\n}\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n@-webkit-keyframes flipInX {\n  0% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    -webkit-transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    -webkit-transition-timing-function: ease-in;\n  }\n\n  60% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    -webkit-transform: perspective(400px);\n  }\n}\n\n/* Fix dropdown menu in navbars */\n.navbar-custom-menu > .navbar-nav {\n  > li {\n    position: relative;\n    > .dropdown-menu {\n      position: absolute;\n      right: 0;\n      left: auto;\n    }\n  }\n}\n\n@media (max-width: @screen-sm-max) {\n  .navbar-custom-menu > .navbar-nav {\n    float: right;\n    > li {\n      position: static;\n      > .dropdown-menu {\n        position: absolute;\n        right: 5%;\n        left: auto;\n        border: 1px solid #ddd;\n        background: #fff;\n      }\n    }\n  }\n}\n", "// Single side border-radius\n\n.border-top-radius(@radius) {\n  border-top-right-radius: @radius;\n  border-top-left-radius: @radius;\n}\n\n.border-right-radius(@radius) {\n  border-bottom-right-radius: @radius;\n  border-top-right-radius: @radius;\n}\n\n.border-bottom-radius(@radius) {\n  border-bottom-right-radius: @radius;\n  border-bottom-left-radius: @radius;\n}\n\n.border-left-radius(@radius) {\n  border-bottom-left-radius: @radius;\n  border-top-left-radius: @radius;\n}\n", "/*\n * Component: Form\n * ---------------\n */\n.form-control {\n  .border-radius(@input-radius);\n  box-shadow: none;\n  border-color: @gray-lte;\n  &:focus {\n    border-color: @light-blue;\n    box-shadow: none;\n  }\n  &::-moz-placeholder,\n  &:-ms-input-placeholder,\n  &::-webkit-input-placeholder {\n    color: #bbb;\n    opacity: 1;\n  }\n\n  &:not(select) {\n    -webkit-appearance: none;\n    -moz-appearance: none;\n    appearance: none;\n  }\n}\n\n.form-group {\n  &.has-success {\n    label {\n      color: @green;\n    }\n    .form-control,\n    .input-group-addon {\n      border-color: @green;\n      box-shadow: none;\n    }\n    .help-block {\n      color: @green;\n    }\n  }\n\n  &.has-warning {\n    label {\n      color: @yellow;\n    }\n    .form-control,\n    .input-group-addon {\n      border-color: @yellow;\n      box-shadow: none;\n    }\n    .help-block {\n      color: @yellow;\n    }\n  }\n\n  &.has-error {\n    label {\n      color: @red;\n    }\n    .form-control,\n    .input-group-addon {\n      border-color: @red;\n      box-shadow: none;\n    }\n    .help-block {\n      color: @red;\n    }\n  }\n}\n\n/* Input group */\n.input-group {\n  .input-group-addon {\n    .border-radius(@input-radius);\n    border-color: @gray-lte;\n    background-color: #fff;\n  }\n}\n\n/* button groups */\n.btn-group-vertical {\n  .btn {\n    &.btn-flat:first-of-type, &.btn-flat:last-of-type {\n      .border-radius(0);\n    }\n  }\n}\n\n.icheck > label {\n  padding-left: 0;\n}\n\n/* support Font Awesome icons in form-control */\n.form-control-feedback.fa {\n  line-height: @input-height-base;\n}\n\n.input-lg + .form-control-feedback.fa,\n.input-group-lg + .form-control-feedback.fa,\n.form-group-lg .form-control + .form-control-feedback.fa {\n  line-height: @input-height-large;\n}\n\n.input-sm + .form-control-feedback.fa,\n.input-group-sm + .form-control-feedback.fa,\n.form-group-sm .form-control + .form-control-feedback.fa {\n  line-height: @input-height-small;\n}\n", "/*\n * Component: Progress Bar\n * -----------------------\n */\n\n//General CSS\n.progress,\n.progress > .progress-bar {\n  .box-shadow(none);\n  &, .progress-bar {\n    .border-radius(@progress-bar-border-radius);\n  }\n}\n\n/* size variation */\n.progress.sm,\n.progress-sm {\n  height: 10px;\n  &, .progress-bar {\n    .border-radius(@progress-bar-sm-border-radius);\n  }\n}\n\n.progress.xs,\n.progress-xs {\n  height: 7px;\n  &, .progress-bar {\n    .border-radius(@progress-bar-xs-border-radius);\n  }\n}\n\n.progress.xxs,\n.progress-xxs {\n  height: 3px;\n  &, .progress-bar {\n    .border-radius(@progress-bar-xs-border-radius);\n  }\n}\n\n/* Vertical bars */\n.progress.vertical {\n  position: relative;\n  width: 30px;\n  height: 200px;\n  display: inline-block;\n  margin-right: 10px;\n  > .progress-bar {\n    width: 100%;\n    position: absolute;\n    bottom: 0;\n  }\n\n  //Sizes\n  &.sm,\n  &.progress-sm {\n    width: 20px;\n  }\n\n  &.xs,\n  &.progress-xs {\n    width: 10px;\n  }\n  &.xxs,\n  &.progress-xxs {\n    width: 3px;\n  }\n}\n\n//Progress Groups\n.progress-group {\n  .progress-text {\n    font-weight: 600;\n  }\n  .progress-number {\n    float: right;\n  }\n}\n\n/* Remove margins from progress bars when put in a table */\n.table {\n  tr > td .progress {\n    margin: 0;\n  }\n}\n\n// Variations\n// -------------------------\n.progress-bar-light-blue,\n.progress-bar-primary {\n  .progress-bar-variant(@light-blue);\n}\n\n.progress-bar-green,\n.progress-bar-success {\n  .progress-bar-variant(@green);\n}\n\n.progress-bar-aqua,\n.progress-bar-info {\n  .progress-bar-variant(@aqua);\n}\n\n.progress-bar-yellow,\n.progress-bar-warning {\n  .progress-bar-variant(@yellow);\n}\n\n.progress-bar-red,\n.progress-bar-danger {\n  .progress-bar-variant(@red);\n}\n", "// Progress bars\n\n.progress-bar-variant(@color) {\n  background-color: @color;\n\n  // Deprecated parent class requirement as of v3.2.0\n  .progress-striped & {\n    #gradient > .striped();\n  }\n}\n", "// Gradients\n\n#gradient {\n\n  // Horizontal gradient, from left to right\n  //\n  // Creates two color stops, start and end, by specifying a color and position for each color stop.\n  // Color stops are not available in IE9 and below.\n  .horizontal(@start-color: #555; @end-color: #333; @start-percent: 0%; @end-percent: 100%) {\n    background-image: -webkit-linear-gradient(left, @start-color @start-percent, @end-color @end-percent); // Safari 5.1-6, Chrome 10+\n    background-image: -o-linear-gradient(left, @start-color @start-percent, @end-color @end-percent); // Opera 12\n    background-image: linear-gradient(to right, @start-color @start-percent, @end-color @end-percent); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n    background-repeat: repeat-x;\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=1)\", argb(@start-color), argb(@end-color))); // IE9 and down\n  }\n\n  // Vertical gradient, from top to bottom\n  //\n  // Creates two color stops, start and end, by specifying a color and position for each color stop.\n  // Color stops are not available in IE9 and below.\n  .vertical(@start-color: #555; @end-color: #333; @start-percent: 0%; @end-percent: 100%) {\n    background-image: -webkit-linear-gradient(top, @start-color @start-percent, @end-color @end-percent); // Safari 5.1-6, Chrome 10+\n    background-image: -o-linear-gradient(top, @start-color @start-percent, @end-color @end-percent); // Opera 12\n    background-image: linear-gradient(to bottom, @start-color @start-percent, @end-color @end-percent); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n    background-repeat: repeat-x;\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)\", argb(@start-color), argb(@end-color))); // IE9 and down\n  }\n\n  .directional(@start-color: #555; @end-color: #333; @deg: 45deg) {\n    background-repeat: repeat-x;\n    background-image: -webkit-linear-gradient(@deg, @start-color, @end-color); // Safari 5.1-6, Chrome 10+\n    background-image: -o-linear-gradient(@deg, @start-color, @end-color); // Opera 12\n    background-image: linear-gradient(@deg, @start-color, @end-color); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n  }\n  .horizontal-three-colors(@start-color: #00b3ee; @mid-color: #7a43b6; @color-stop: 50%; @end-color: #c3325f) {\n    background-image: -webkit-linear-gradient(left, @start-color, @mid-color @color-stop, @end-color);\n    background-image: -o-linear-gradient(left, @start-color, @mid-color @color-stop, @end-color);\n    background-image: linear-gradient(to right, @start-color, @mid-color @color-stop, @end-color);\n    background-repeat: no-repeat;\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=1)\", argb(@start-color), argb(@end-color))); // IE9 and down, gets no color-stop at all for proper fallback\n  }\n  .vertical-three-colors(@start-color: #00b3ee; @mid-color: #7a43b6; @color-stop: 50%; @end-color: #c3325f) {\n    background-image: -webkit-linear-gradient(@start-color, @mid-color @color-stop, @end-color);\n    background-image: -o-linear-gradient(@start-color, @mid-color @color-stop, @end-color);\n    background-image: linear-gradient(@start-color, @mid-color @color-stop, @end-color);\n    background-repeat: no-repeat;\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)\", argb(@start-color), argb(@end-color))); // IE9 and down, gets no color-stop at all for proper fallback\n  }\n  .radial(@inner-color: #555; @outer-color: #333) {\n    background-image: -webkit-radial-gradient(circle, @inner-color, @outer-color);\n    background-image: radial-gradient(circle, @inner-color, @outer-color);\n    background-repeat: no-repeat;\n  }\n  .striped(@color: rgba(255,255,255,.15); @angle: 45deg) {\n    background-image: -webkit-linear-gradient(@angle, @color 25%, transparent 25%, transparent 50%, @color 50%, @color 75%, transparent 75%, transparent);\n    background-image: -o-linear-gradient(@angle, @color 25%, transparent 25%, transparent 50%, @color 50%, @color 75%, transparent 75%, transparent);\n    background-image: linear-gradient(@angle, @color 25%, transparent 25%, transparent 50%, @color 50%, @color 75%, transparent 75%, transparent);\n  }\n}\n", "/*\n * Component: Small Box\n * --------------------\n */\n\n.small-box {\n  .border-radius(2px);\n  position: relative;\n  display: block;\n  margin-bottom: 20px;\n  box-shadow: @box-boxshadow;\n  // content wrapper\n  > .inner {\n    padding: 10px;\n  }\n\n  > .small-box-footer {\n    position: relative;\n    text-align: center;\n    padding: 3px 0;\n    color: #fff;\n    color: rgba(255, 255, 255, 0.8);\n    display: block;\n    z-index: 10;\n    background: rgba(0, 0, 0, 0.1);\n    text-decoration: none;\n    &:hover {\n      color: #fff;\n      background: rgba(0, 0, 0, 0.15);\n    }\n  }\n\n  h3 {\n    font-size: 38px;\n    font-weight: bold;\n    margin: 0 0 10px 0;\n    white-space: nowrap;\n    padding: 0;\n\n  }\n\n  p {\n    font-size: 15px;\n    > small {\n      display: block;\n      color: #f9f9f9;\n      font-size: 13px;\n      margin-top: 5px;\n    }\n  }\n\n  h3, p {\n    z-index: 5;\n  }\n\n  // the icon\n  .icon {\n    .transition(all @transition-speed linear);\n    position: absolute;\n    top: -10px;\n    right: 10px;\n    z-index: 0;\n    font-size: 90px;\n    color: rgba(0, 0, 0, 0.15);\n  }\n\n  // Small box hover state\n  &:hover {\n    text-decoration: none;\n    color: #f9f9f9;\n    // Animate icons on small box hover\n    .icon {\n      font-size: 95px;\n    }\n  }\n}\n\n@media (max-width: @screen-xs-max) {\n  // No need for icons on very small devices\n  .small-box {\n    text-align: center;\n    .icon {\n      display: none;\n    }\n    p {\n      font-size: 12px;\n    }\n  }\n}\n", "/*\n * Component: Box\n * --------------\n */\n.box {\n  position: relative;\n  .border-radius(@box-border-radius);\n  background: #ffffff;\n  border-top: 3px solid @box-default-border-top-color;\n  margin-bottom: 20px;\n  width: 100%;\n  box-shadow: @box-boxshadow;\n\n  // Box color variations\n  &.box-primary {\n    border-top-color: @light-blue;\n  }\n  &.box-info {\n    border-top-color: @aqua;\n  }\n  &.box-danger {\n    border-top-color: @red;\n  }\n  &.box-warning {\n    border-top-color: @yellow;\n  }\n  &.box-success {\n    border-top-color: @green;\n  }\n  &.box-default {\n    border-top-color: @gray-lte;\n  }\n\n  // collapsed mode\n  &.collapsed-box {\n    .box-body,\n    .box-footer {\n      display: none;\n    }\n  }\n\n  .nav-stacked {\n    > li {\n      border-bottom: 1px solid @box-border-color;\n      margin: 0;\n      &:last-of-type {\n        border-bottom: none;\n      }\n    }\n  }\n\n  // fixed height to 300px\n  &.height-control {\n    .box-body {\n      max-height: 300px;\n      overflow: auto;\n    }\n  }\n\n  .border-right {\n    border-right: 1px solid @box-border-color;\n  }\n  .border-left {\n    border-left: 1px solid @box-border-color;\n  }\n\n  //SOLID BOX\n  //---------\n  //use this class to get a colored header and borders\n\n  &.box-solid {\n    border-top: 0;\n    > .box-header {\n      .btn.btn-default {\n        background: transparent;\n      }\n      .btn,\n      a {\n        &:hover {\n          background: rgba(0, 0, 0, 0.1);\n        }\n      }\n    }\n\n    // Box color variations\n    &.box-default {\n      .box-solid-variant(@gray-lte, #444);\n    }\n    &.box-primary {\n      .box-solid-variant(@light-blue);\n    }\n    &.box-info {\n      .box-solid-variant(@aqua);\n    }\n    &.box-danger {\n      .box-solid-variant(@red);\n    }\n    &.box-warning {\n      .box-solid-variant(@yellow);\n    }\n    &.box-success {\n      .box-solid-variant(@green);\n    }\n\n    > .box-header > .box-tools .btn {\n      border: 0;\n      box-shadow: none;\n    }\n\n    // Fix font color for tiles\n    &[class*='bg'] {\n      > .box-header {\n        color: #fff;\n      }\n    }\n\n  }\n\n  //BOX GROUP\n  .box-group {\n    > .box {\n      margin-bottom: 5px;\n    }\n  }\n\n  // jQuery Knob in a box\n  .knob-label {\n    text-align: center;\n    color: #333;\n    font-weight: 100;\n    font-size: 12px;\n    margin-bottom: 0.3em;\n  }\n}\n\n.box,\n.overlay-wrapper {\n  // Box overlay for LOADING STATE effect\n  > .overlay,\n  > .loading-img {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n\n  .overlay {\n    z-index: 50;\n    background: rgba(255, 255, 255, 0.7);\n    .border-radius(@box-border-radius);\n    > .fa {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      margin-left: -15px;\n      margin-top: -15px;\n      color: #000;\n      font-size: 30px;\n    }\n  }\n\n  .overlay.dark {\n    background: rgba(0, 0, 0, 0.5);\n  }\n}\n\n//Add clearfix to header, body and footer\n.box-header,\n.box-body,\n.box-footer {\n  .clearfix();\n}\n\n//Box header\n.box-header {\n  color: #444;\n  display: block;\n  padding: @box-padding;\n  position: relative;\n\n  //Add bottom border\n  &.with-border {\n    border-bottom: 1px solid @box-border-color;\n    .collapsed-box & {\n      border-bottom: none;\n    }\n  }\n\n  //Icons and box title\n  > .fa,\n  > .glyphicon,\n  > .ion,\n  .box-title {\n    display: inline-block;\n    font-size: 18px;\n    margin: 0;\n    line-height: 1;\n  }\n  > .fa,\n  > .glyphicon,\n  > .ion {\n    margin-right: 5px;\n  }\n  > .box-tools {\n    position: absolute;\n    right: 10px;\n    top: 5px;\n    [data-toggle=\"tooltip\"] {\n      position: relative;\n    }\n\n    &.pull-right {\n      .dropdown-menu {\n        right: 0;\n        left: auto;\n      }\n    }\n\n    .dropdown-menu > li > a {\n      color: #444!important;\n    }\n  }\n}\n\n//Box Tools Buttons\n.btn-box-tool {\n  padding: 5px;\n  font-size: 12px;\n  background: transparent;\n  color: darken(@box-default-border-top-color, 20%);\n  .open &,\n  &:hover {\n    color: darken(@box-default-border-top-color, 40%);\n  }\n  &.btn:active {\n    box-shadow: none;\n  }\n}\n\n//Box Body\n.box-body {\n  .border-radius(0; 0; @box-border-radius; @box-border-radius);\n  padding: @box-padding;\n  .no-header & {\n    .border-top-radius(@box-border-radius);\n  }\n  // Tables within the box body\n  > .table {\n    margin-bottom: 0;\n  }\n\n  // Calendar within the box body\n  .fc {\n    margin-top: 5px;\n  }\n\n  .full-width-chart {\n    margin: -19px;\n  }\n  &.no-padding .full-width-chart {\n    margin: -9px;\n  }\n\n  .box-pane {\n    .border-radius(0; 0; @box-border-radius; 0);\n  }\n  .box-pane-right {\n    .border-radius(0; 0; 0; @box-border-radius);\n  }\n}\n\n//Box footer\n.box-footer {\n  .border-radius(0; 0; @box-border-radius; @box-border-radius);\n  border-top: 1px solid @box-border-color;\n  padding: @box-padding;\n  background-color: @box-footer-bg;\n}\n\n.chart-legend {\n  &:extend(.list-unstyled);\n  margin: 10px 0;\n  > li {\n    @media (max-width: @screen-sm-max) {\n      float: left;\n      margin-right: 10px;\n    }\n  }\n}\n\n//Comment Box\n.box-comments {\n  background: #f7f7f7;\n  .box-comment {\n    .clearfix();\n    padding: 8px 0;\n    border-bottom: 1px solid #eee;\n    &:last-of-type {\n      border-bottom: 0;\n    }\n    &:first-of-type {\n      padding-top: 0;\n    }\n    img {\n      &:extend(.img-sm);\n      float: left;\n    }\n  }\n  .comment-text {\n    margin-left: 40px;\n    color: #555;\n  }\n  .username {\n    color: #444;\n    display: block;\n    font-weight: 600;\n  }\n  .text-muted {\n    font-weight: 400;\n    font-size: 12px;\n  }\n}\n\n//Widgets\n//-----------\n\n/* Widget: TODO LIST */\n\n.todo-list {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  overflow: auto;\n  // Todo list element\n  > li {\n    .border-radius(2px);\n    padding: 10px;\n    background: #f4f4f4;\n    margin-bottom: 2px;\n    border-left: 2px solid #e6e7e8;\n    color: #444;\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n\n    > input[type='checkbox'] {\n      margin: 0 10px 0 5px;\n    }\n\n    .text {\n      display: inline-block;\n      margin-left: 5px;\n      font-weight: 600;\n    }\n\n    // Time labels\n    .label {\n      margin-left: 10px;\n      font-size: 9px;\n    }\n\n    // Tools and options box\n    .tools {\n      display: none;\n      float: right;\n      color: @red;\n      // icons\n      > .fa, > .glyphicon, > .ion {\n        margin-right: 5px;\n        cursor: pointer;\n      }\n\n    }\n    &:hover .tools {\n      display: inline-block;\n    }\n\n    &.done {\n      color: #999;\n      .text {\n        text-decoration: line-through;\n        font-weight: 500;\n      }\n\n      .label {\n        background: @gray-lte !important;\n      }\n    }\n  }\n\n  // Color varaity\n  .danger {\n    border-left-color: @red;\n  }\n  .warning {\n    border-left-color: @yellow;\n  }\n  .info {\n    border-left-color: @aqua;\n  }\n  .success {\n    border-left-color: @green;\n  }\n  .primary {\n    border-left-color: @light-blue;\n  }\n\n  .handle {\n    display: inline-block;\n    cursor: move;\n    margin: 0 5px;\n  }\n\n}\n\n// END TODO WIDGET\n\n/* Chat widget (DEPRECATED - this will be removed in the next major release. Use Direct Chat instead)*/\n.chat {\n  padding: 5px 20px 5px 10px;\n\n  .item {\n    .clearfix();\n    margin-bottom: 10px;\n    // The image\n    > img {\n      width: 40px;\n      height: 40px;\n      border: 2px solid transparent;\n      .border-radius(50%);\n    }\n\n    > .online {\n      border: 2px solid @green;\n    }\n    > .offline {\n      border: 2px solid @red;\n    }\n\n    // The message body\n    > .message {\n      margin-left: 55px;\n      margin-top: -40px;\n      > .name {\n        display: block;\n        font-weight: 600;\n      }\n    }\n\n    // The attachment\n    > .attachment {\n      .border-radius(@attachment-border-radius);\n      background: #f4f4f4;\n      margin-left: 65px;\n      margin-right: 15px;\n      padding: 10px;\n      > h4 {\n        margin: 0 0 5px 0;\n        font-weight: 600;\n        font-size: 14px;\n      }\n      > p, > .filename {\n        font-weight: 600;\n        font-size: 13px;\n        font-style: italic;\n        margin: 0;\n\n      }\n      .clearfix();\n    }\n  }\n\n}\n\n//END CHAT WIDGET\n\n//Input in box\n.box-input {\n  max-width: 200px;\n}\n\n//A fix for panels body text color when placed within\n// a modal\n.modal {\n  .panel-body {\n    color: #444;\n  }\n}\n", "/*\n * Component: Info Box\n * -------------------\n */\n.info-box {\n  display: block;\n  min-height: 90px;\n  background: #fff;\n  width: 100%;\n  box-shadow: @box-boxshadow;\n  .border-radius(2px);\n  margin-bottom: 15px;\n  small {\n    font-size: 14px;\n  }\n  .progress {\n    background: rgba(0, 0, 0, .2);\n    margin: 5px -10px 5px -10px;\n    height: 2px;\n    &,\n    & .progress-bar {\n      .border-radius(0);\n    }\n    .progress-bar {\n      background: #fff;\n    }\n  }\n}\n\n.info-box-icon {\n  .border-radius(2px; 0; 2px; 0);\n  display: block;\n  float: left;\n  height: 90px;\n  width: 90px;\n  text-align: center;\n  font-size: 45px;\n  line-height: 90px;\n  background: rgba(0, 0, 0, 0.2);\n  > img {\n    max-width: 100%;\n  }\n}\n\n.info-box-content {\n  padding: 5px 10px;\n  margin-left: 90px;\n}\n\n.info-box-number {\n  display: block;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.progress-description,\n.info-box-text {\n  display: block;\n  font-size: 14px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.info-box-text {\n  text-transform: uppercase;\n}\n\n.info-box-more {\n  display: block;\n}\n\n.progress-description {\n  margin: 0;\n}\n", "/*\n * Component: Timeline\n * -------------------\n */\n\n.timeline {\n  position: relative;\n  margin: 0 0 30px 0;\n  padding: 0;\n  list-style: none;\n\n  // The line\n  &:before {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    width: 4px;\n    background: #ddd;\n    left: 31px;\n    margin: 0;\n    .border-radius(2px);\n  }\n\n  > li {\n    position: relative;\n    margin-right: 10px;\n    margin-bottom: 15px;\n    .clearfix();\n\n    // The content\n    > .timeline-item {\n      .box-shadow(@box-boxshadow);\n      .border-radius(@box-border-radius);\n      margin-top: 0;\n      background: #fff;\n      color: #444;\n      margin-left: 60px;\n      margin-right: 15px;\n      padding: 0;\n      position: relative;\n\n      // The time and header\n      > .time {\n        color: #999;\n        float: right;\n        padding: 10px;\n        font-size: 12px;\n      }\n      > .timeline-header {\n        margin: 0;\n        color: #555;\n        border-bottom: 1px solid @box-border-color;\n        padding: 10px;\n        font-size: 16px;\n        line-height: 1.1;\n        > a {\n          font-weight: 600;\n        }\n      }\n      // Item body and footer\n      > .timeline-body, > .timeline-footer {\n        padding: 10px;\n      }\n\n    }\n\n    // The icons\n    > .fa,\n    > .glyphicon,\n    > .ion {\n      width: 30px;\n      height: 30px;\n      font-size: 15px;\n      line-height: 30px;\n      position: absolute;\n      color: #666;\n      background: @gray-lte;\n      border-radius: 50%;\n      text-align: center;\n      left: 18px;\n      top: 0;\n    }\n  }\n\n  // Time label\n  > .time-label {\n    > span {\n      font-weight: 600;\n      padding: 5px;\n      display: inline-block;\n      background-color: #fff;\n\n      .border-radius(4px);\n    }\n  }\n}\n\n.timeline-inverse {\n  > li {\n    > .timeline-item {\n      background: #f0f0f0;\n      border: 1px solid #ddd;\n      .box-shadow(none);\n      > .timeline-header {\n        border-bottom-color: #ddd;\n      }\n    }\n  }\n}\n", "/*\n * Component: Button\n * -----------------\n */\n\n.btn {\n  .border-radius(@btn-border-radius);\n  .box-shadow(@btn-boxshadow);\n  border: 1px solid transparent;\n\n  &.uppercase {\n    text-transform: uppercase\n  }\n\n  // Flat buttons\n  &.btn-flat {\n    .border-radius(0);\n    -webkit-box-shadow: none;\n    -moz-box-shadow: none;\n    box-shadow: none;\n    border-width: 1px;\n  }\n\n  // Active state\n  &:active {\n    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n    -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n  }\n\n  &:focus {\n    outline: none;\n  }\n\n  // input file btn\n  &.btn-file {\n    position: relative;\n    overflow: hidden;\n    > input[type='file'] {\n      position: absolute;\n      top: 0;\n      right: 0;\n      min-width: 100%;\n      min-height: 100%;\n      font-size: 100px;\n      text-align: right;\n      .opacity(0);\n      outline: none;\n      background: white;\n      cursor: inherit;\n      display: block;\n    }\n  }\n}\n\n//Button color variations\n.btn-default {\n  background-color: #f4f4f4;\n  color: #444;\n  border-color: #ddd;\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken(#f4f4f4, 5%);\n  }\n}\n\n.btn-primary {\n  background-color: @light-blue;\n  border-color: darken(@light-blue, 5%);\n  &:hover, &:active, &.hover {\n    background-color: darken(@light-blue, 5%);\n  }\n}\n\n.btn-success {\n  background-color: @green;\n  border-color: darken(@green, 5%);\n  &:hover, &:active, &.hover {\n    background-color: darken(@green, 5%);\n  }\n}\n\n.btn-info {\n  background-color: @aqua;\n  border-color: darken(@aqua, 5%);\n  &:hover, &:active, &.hover {\n    background-color: darken(@aqua, 5%);\n  }\n}\n\n.btn-danger {\n  background-color: @red;\n  border-color: darken(@red, 5%);\n  &:hover, &:active, &.hover {\n    background-color: darken(@red, 5%);\n  }\n}\n\n.btn-warning {\n  background-color: @yellow;\n  border-color: darken(@yellow, 5%);\n  &:hover, &:active, &.hover {\n    background-color: darken(@yellow, 5%);\n  }\n}\n\n.btn-outline {\n  border: 1px solid #fff;\n  background: transparent;\n  color: #fff;\n  &:hover,\n  &:focus,\n  &:active {\n    color: rgba(255, 255, 255, .7);\n    border-color: rgba(255, 255, 255, .7);\n  }\n}\n\n.btn-link {\n  .box-shadow(none);\n}\n\n//General .btn with bg class\n.btn[class*='bg-']:hover {\n  .box-shadow(inset 0 0 100px rgba(0, 0, 0, 0.2));\n}\n\n// Application buttons\n.btn-app {\n  .border-radius(3px);\n  position: relative;\n  padding: 15px 5px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  height: 60px;\n  text-align: center;\n  color: #666;\n  border: 1px solid #ddd;\n  background-color: #f4f4f4;\n  font-size: 12px;\n  //Icons within the btn\n  > .fa, > .glyphicon, > .ion {\n    font-size: 20px;\n    display: block;\n  }\n\n  &:hover {\n    background: #f4f4f4;\n    color: #444;\n    border-color: #aaa;\n  }\n\n  &:active, &:focus {\n    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n    -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n  }\n\n  //The badge\n  > .badge {\n    position: absolute;\n    top: -3px;\n    right: -10px;\n    font-size: 10px;\n    font-weight: 400;\n  }\n}\n", "// Opacity\n\n.opacity(@opacity) {\n  opacity: @opacity;\n  // IE8 filter\n  @opacity-ie: (@opacity * 100);\n  filter: ~\"alpha(opacity=@{opacity-ie})\";\n}\n", "/*\n * Component: Callout\n * ------------------\n */\n\n// Base styles (regardless of theme)\n.callout {\n  .border-radius(3px);\n  margin: 0 0 20px 0;\n  padding: 15px 30px 15px 15px;\n  border-left: 5px solid #eee;\n  a {\n    color: #fff;\n    text-decoration: underline;\n    &:hover {\n      color: #eee;\n    }\n  }\n  h4 {\n    margin-top: 0;\n    font-weight: 600;\n  }\n  p:last-child {\n    margin-bottom: 0;\n  }\n  code,\n  .highlight {\n    background-color: #fff;\n  }\n\n  // Themes for different contexts\n  &.callout-danger {\n    &:extend(.bg-red);\n    border-color: darken(@red, 10%);\n  }\n  &.callout-warning {\n    &:extend(.bg-yellow);\n    border-color: darken(@yellow, 10%);\n  }\n  &.callout-info {\n    &:extend(.bg-aqua);\n    border-color: darken(@aqua, 10%);\n  }\n  &.callout-success {\n    &:extend(.bg-green);\n    border-color: darken(@green, 10%);\n  }\n}\n", "/*\n * Component: alert\n * ----------------\n */\n\n.alert {\n  .border-radius(3px);\n  h4 {\n    font-weight: 600;\n  }\n  .icon {\n    margin-right: 10px;\n  }\n  .close {\n    color: #000;\n    .opacity(.2);\n    &:hover {\n      .opacity(.5);\n    }\n  }\n  a {\n    color: #fff;\n    text-decoration: underline;\n  }\n}\n\n//Alert Variants\n.alert-success {\n  &:extend(.bg-green);\n  border-color: darken(@green, 5%);\n}\n\n.alert-danger,\n.alert-error {\n  &:extend(.bg-red);\n  border-color: darken(@red, 5%);\n}\n\n.alert-warning {\n  &:extend(.bg-yellow);\n  border-color: darken(@yellow, 5%);\n}\n\n.alert-info {\n  &:extend(.bg-aqua);\n  border-color: darken(@aqua, 5%);\n}\n", "/*\n * Component: Nav\n * --------------\n */\n\n.nav {\n  > li > a:hover,\n  > li > a:active,\n  > li > a:focus {\n    color: #444;\n    background: #f7f7f7;\n  }\n}\n\n/* NAV PILLS */\n.nav-pills {\n  > li > a {\n    .border-radius(0);\n    border-top: 3px solid transparent;\n    color: #444;\n    > .fa,\n    > .glyphicon,\n    > .ion {\n      margin-right: 5px;\n    }\n  }\n  > li.active > a,\n  > li.active > a:hover,\n  > li.active > a:focus {\n    border-top-color: @light-blue;\n  }\n  > li.active > a {\n    font-weight: 600;\n  }\n}\n\n/* NAV STACKED */\n.nav-stacked {\n  > li > a {\n    .border-radius(0);\n    border-top: 0;\n    border-left: 3px solid transparent;\n    color: #444;\n  }\n  > li.active > a,\n  > li.active > a:hover {\n    background: transparent;\n    color: #444;\n    border-top: 0;\n    border-left-color: @light-blue;\n  }\n\n  > li.header {\n    border-bottom: 1px solid #ddd;\n    color: #777;\n    margin-bottom: 10px;\n    padding: 5px 10px;\n    text-transform: uppercase;\n  }\n}\n\n/* NAV TABS */\n.nav-tabs-custom {\n  margin-bottom: 20px;\n  background: #fff;\n  box-shadow: @box-boxshadow;\n  border-radius: @box-border-radius;\n  > .nav-tabs {\n    margin: 0;\n    border-bottom-color: #f4f4f4;\n    .border-top-radius(@box-border-radius);\n    > li {\n      border-top: 3px solid transparent;\n      margin-bottom: -2px;\n      > a {\n        color: #444;\n        .border-radius(0);\n        &.text-muted {\n          color: #999;\n        }\n        &,\n        &:hover {\n          background: transparent;\n          margin: 0;\n        }\n        &:hover {\n          color: #999;\n        }\n      }\n      &:not(.active) {\n        > a:hover,\n        > a:focus,\n        > a:active {\n          border-color: transparent;\n        }\n      }\n      margin-right: 5px;\n    }\n\n    > li.active {\n      border-top-color: @light-blue;\n      & > a,\n      &:hover > a {\n        background-color: #fff;\n        color: #444;\n      }\n      > a {\n        border-top-color: transparent;\n        border-left-color: #f4f4f4;\n        border-right-color: #f4f4f4;\n      }\n\n    }\n\n    > li:first-of-type {\n      margin-left: 0;\n      &.active {\n        > a {\n          border-left-color: transparent;\n        }\n      }\n    }\n\n    //Pulled to the right\n    &.pull-right {\n      float: none !important;\n      > li {\n        float: right;\n      }\n      > li:first-of-type {\n        margin-right: 0;\n        > a {\n          border-left-width: 1px;\n        }\n        &.active {\n          > a {\n            border-left-color: #f4f4f4;\n            border-right-color: transparent;\n          }\n        }\n      }\n    }\n\n    > li.header {\n      line-height: 35px;\n      padding: 0 10px;\n      font-size: 20px;\n      color: #444;\n      > .fa,\n      > .glyphicon,\n      > .ion {\n        margin-right: 5px;\n      }\n    }\n  }\n\n  > .tab-content {\n    background: #fff;\n    padding: 10px;\n    .border-bottom-radius(@box-border-radius);\n  }\n\n  .dropdown.open > a {\n    &:active,\n    &:focus {\n      background: transparent;\n      color: #999;\n    }\n  }\n  // Tab color variations\n  &.tab-primary {\n    > .nav-tabs {\n      > li.active {\n        border-top-color: @light-blue;\n      }\n    }\n  }\n  &.tab-info {\n    > .nav-tabs {\n      > li.active {\n        border-top-color: @aqua;\n      }\n    }\n  }\n  &.tab-danger {\n    > .nav-tabs {\n      > li.active {\n        border-top-color: @red;\n      }\n    }\n  }\n  &.tab-warning {\n    > .nav-tabs {\n      > li.active {\n        border-top-color: @yellow;\n      }\n    }\n  }\n  &.tab-success {\n    > .nav-tabs {\n      > li.active {\n        border-top-color: @green;\n      }\n    }\n  }\n  &.tab-default {\n    > .nav-tabs {\n      > li.active {\n        border-top-color: @gray-lte;\n      }\n    }\n  }\n}\n\n/* PAGINATION */\n.pagination {\n  > li > a {\n    background: #fafafa;\n    color: #666;\n  }\n  &.pagination-flat {\n    > li > a {\n      .border-radius(0) !important;\n    }\n  }\n}\n", "/*\n * Component: Products List\n * ------------------------\n */\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  > .item {\n    .border-radius(@box-border-radius);\n    .box-shadow(@box-boxshadow);\n    .clearfix();\n    padding: 10px 0;\n    background: #fff;\n  }\n  .product-img {\n    float: left;\n    img {\n      width: 50px;\n      height: 50px;\n    }\n  }\n  .product-info {\n    margin-left: 60px;\n  }\n  .product-title {\n    font-weight: 600;\n  }\n  .product-description {\n    display: block;\n    color: #999;\n    overflow: hidden;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n  }\n}\n\n.product-list-in-box > .item {\n  .box-shadow(none);\n  .border-radius(0);\n  border-bottom: 1px solid @box-border-color;\n  &:last-of-type {\n    border-bottom-width: 0;\n  }\n}\n", "/*\n * Component: Table\n * ----------------\n */\n\n.table {\n  //Cells\n  > thead,\n  > tbody,\n  > tfoot {\n    > tr {\n      > th,\n      > td {\n        border-top: 1px solid @box-border-color;\n      }\n    }\n  }\n  //thead cells\n  > thead > tr > th {\n    border-bottom: 2px solid @box-border-color;\n  }\n  //progress bars in tables\n  tr td .progress {\n    margin-top: 5px;\n  }\n}\n\n//Bordered Table\n.table-bordered {\n  border: 1px solid @box-border-color;\n  > thead,\n  > tbody,\n  > tfoot {\n    > tr {\n      > th,\n      > td {\n        border: 1px solid @box-border-color;\n      }\n    }\n  }\n  > thead > tr {\n    > th,\n    > td {\n      border-bottom-width: 2px;\n    }\n  }\n}\n\n.table.no-border {\n  &,\n  td,\n  th {\n    border: 0;\n  }\n}\n\n/* .text-center in tables */\ntable.text-center {\n  &, td, th {\n    text-align: center;\n  }\n}\n\n.table.align {\n  th {\n    text-align: left;\n  }\n  td {\n    text-align: right;\n  }\n}\n", "/*\n * Component: Label\n * ----------------\n */\n.label-default {\n  background-color: @gray-lte;\n  color: #444;\n}\n\n.label-danger {\n  &:extend(.bg-red);\n}\n\n.label-info {\n  &:extend(.bg-aqua);\n}\n\n.label-warning {\n  &:extend(.bg-yellow);\n}\n\n.label-primary {\n  &:extend(.bg-light-blue);\n}\n\n.label-success {\n  &:extend(.bg-green);\n}\n", "/*\n * Component: Direct Chat\n * ----------------------\n */\n.direct-chat {\n  .box-body {\n    .border-bottom-radius(0);\n    position: relative;\n    overflow-x: hidden;\n    padding: 0;\n  }\n  &.chat-pane-open {\n    .direct-chat-contacts {\n      .translate(0, 0);\n    }\n  }\n}\n\n.direct-chat-messages {\n  .translate(0, 0);\n  padding: 10px;\n  height: 250px;\n  overflow: auto;\n}\n\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n\n.direct-chat-msg {\n  .clearfix();\n  margin-bottom: 10px;\n}\n\n.direct-chat-messages,\n.direct-chat-contacts {\n  .transition-transform(.5s ease-in-out);\n}\n\n.direct-chat-text {\n  .border-radius(5px);\n  position: relative;\n  padding: 5px 10px;\n  background: @direct-chat-default-msg-bg;\n  border: 1px solid @direct-chat-default-msg-border-color;\n  margin: 5px 0 0 50px;\n  color: @direct-chat-default-font-color;\n\n  //Create the arrow\n  &:after,\n  &:before {\n    position: absolute;\n    right: 100%;\n    top: 15px;\n    border: solid transparent;\n    border-right-color: @direct-chat-default-msg-border-color;\n    content: ' ';\n    height: 0;\n    width: 0;\n    pointer-events: none;\n  }\n\n  &:after {\n    border-width: 5px;\n    margin-top: -5px;\n  }\n  &:before {\n    border-width: 6px;\n    margin-top: -6px;\n  }\n  .right & {\n    margin-right: 50px;\n    margin-left: 0;\n    &:after,\n    &:before {\n      right: auto;\n      left: 100%;\n      border-right-color: transparent;\n      border-left-color: @direct-chat-default-msg-border-color;\n    }\n  }\n}\n\n.direct-chat-img {\n  .border-radius(50%);\n  float: left;\n  width: 40px;\n  height: 40px;\n  .right & {\n    float: right;\n  }\n}\n\n.direct-chat-info {\n  display: block;\n  margin-bottom: 2px;\n  font-size: 12px;\n}\n\n.direct-chat-name {\n  font-weight: 600;\n}\n\n.direct-chat-timestamp {\n  color: #999;\n}\n\n//Direct chat contacts pane\n.direct-chat-contacts-open {\n  .direct-chat-contacts {\n    .translate(0, 0);\n  }\n}\n\n.direct-chat-contacts {\n  .translate(101%, 0);\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  height: 250px;\n  width: 100%;\n  background: #222d32;\n  color: #fff;\n  overflow: auto;\n}\n\n//Contacts list -- for displaying contacts in direct chat contacts pane\n.contacts-list {\n  &:extend(.list-unstyled);\n  > li {\n    .clearfix();\n    border-bottom: 1px solid rgba(0, 0, 0, 0.2);\n    padding: 10px;\n    margin: 0;\n    &:last-of-type {\n      border-bottom: none;\n    }\n  }\n}\n\n.contacts-list-img {\n  .border-radius(50%);\n  width: 40px;\n  float: left;\n}\n\n.contacts-list-info {\n  margin-left: 45px;\n  color: #fff;\n}\n\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n\n.contacts-list-name {\n  font-weight: 600;\n}\n\n.contacts-list-status {\n  font-size: 12px;\n}\n\n.contacts-list-date {\n  color: #aaa;\n  font-weight: normal;\n}\n\n.contacts-list-msg {\n  color: #999;\n}\n\n//Direct Chat Variants\n.direct-chat-danger {\n  .direct-chat-variant(@red);\n}\n\n.direct-chat-primary {\n  .direct-chat-variant(@light-blue);\n}\n\n.direct-chat-warning {\n  .direct-chat-variant(@yellow);\n}\n\n.direct-chat-info {\n  .direct-chat-variant(@aqua);\n}\n\n.direct-chat-success {\n  .direct-chat-variant(@green);\n}\n", "/*\n * Component: Users List\n * ---------------------\n */\n.users-list {\n  &:extend(.list-unstyled);\n  > li {\n    width: 25%;\n    float: left;\n    padding: 10px;\n    text-align: center;\n    img {\n      .border-radius(50%);\n      max-width: 100%;\n      height: auto;\n    }\n    > a:hover {\n      &,\n      .users-list-name {\n        color: #999;\n      }\n    }\n  }\n}\n\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n\n.users-list-name {\n  font-weight: 600;\n  color: #444;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.users-list-date {\n  color: #999;\n  font-size: 12px;\n}\n", "/*\n * Component: Carousel\n * -------------------\n */\n.carousel-control {\n  &.left,\n  &.right {\n    background-image: none;\n  }\n  > .fa {\n    font-size: 40px;\n    position: absolute;\n    top: 50%;\n    z-index: 5;\n    display: inline-block;\n    margin-top: -20px;\n  }\n}\n", "/*\n * Component: modal\n * ----------------\n */\n.modal {\n  background: rgba(0, 0, 0, .3);\n}\n\n.modal-content {\n  .border-radius(0);\n  .box-shadow(0 2px 3px rgba(0, 0, 0, .125));\n  border: 0;\n  @media (min-width: @screen-sm-min) {\n    .box-shadow(0 2px 3px rgba(0, 0, 0, .125));\n  }\n}\n\n.modal-header {\n  border-bottom-color: @box-border-color;\n}\n\n.modal-footer {\n  border-top-color: @box-border-color;\n}\n\n//Modal variants\n.modal-primary {\n  .modal-body {\n    &:extend(.bg-light-blue);\n  }\n  .modal-header,\n  .modal-footer {\n    &:extend(.bg-light-blue-active);\n    border-color: darken(@light-blue, 10%);\n  }\n}\n\n.modal-warning {\n  .modal-body {\n    &:extend(.bg-yellow);\n  }\n  .modal-header,\n  .modal-footer {\n    &:extend(.bg-yellow-active);\n    border-color: darken(@yellow, 10%);\n  }\n}\n\n.modal-info {\n  .modal-body {\n    &:extend(.bg-aqua);\n  }\n  .modal-header,\n  .modal-footer {\n    &:extend(.bg-aqua-active);\n    border-color: darken(@aqua, 10%);\n  }\n}\n\n.modal-success {\n  .modal-body {\n    &:extend(.bg-green);\n  }\n  .modal-header,\n  .modal-footer {\n    &:extend(.bg-green-active);\n    border-color: darken(@green, 10%);\n  }\n}\n\n.modal-danger {\n  .modal-body {\n    &:extend(.bg-red);\n  }\n  .modal-header,\n  .modal-footer {\n    &:extend(.bg-red-active);\n    border-color: darken(@red, 10%);\n  }\n}\n", "/*\n * Component: Social Widgets\n * -------------------------\n */\n//General widget style\n.box-widget {\n  border: none;\n  position: relative;\n}\n\n//User Widget Style 1\n.widget-user {\n  //User name container\n  .widget-user-header {\n    padding: 20px;\n    height: 120px;\n    .border-top-radius(@box-border-radius);\n  }\n  //User name\n  .widget-user-username {\n    margin-top: 0;\n    margin-bottom: 5px;\n    font-size: 25px;\n    font-weight: 300;\n    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);\n  }\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n  //User image container\n  .widget-user-image {\n    position: absolute;\n    top: 65px;\n    left: 50%;\n    margin-left: -45px;\n    > img {\n      width: 90px;\n      height: auto;\n      border: 3px solid #fff;\n    }\n  }\n  .box-footer {\n    padding-top: 30px;\n  }\n}\n\n//User Widget Style 2\n.widget-user-2 {\n  //User name container\n  .widget-user-header {\n    padding: 20px;\n    .border-top-radius(@box-border-radius);\n  }\n  //User name\n  .widget-user-username {\n    margin-top: 5px;\n    margin-bottom: 5px;\n    font-size: 25px;\n    font-weight: 300;\n  }\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n  .widget-user-username,\n  .widget-user-desc {\n    margin-left: 75px;\n  }\n  //User image container\n  .widget-user-image {\n    > img {\n      width: 65px;\n      height: auto;\n      float: left;\n    }\n  }\n}\n", "/*\n * Page: Mailbox\n * -------------\n */\n.mailbox-messages {\n  > .table {\n    margin: 0;\n  }\n}\n\n.mailbox-controls {\n  padding: 5px;\n  &.with-border {\n    border-bottom: 1px solid @box-border-color;\n  }\n}\n\n.mailbox-read-info {\n  border-bottom: 1px solid @box-border-color;\n  padding: 10px;\n  h3 {\n    font-size: 20px;\n    margin: 0;\n  }\n  h5 {\n    margin: 0;\n    padding: 5px 0 0 0;\n  }\n}\n\n.mailbox-read-time {\n  color: #999;\n  font-size: 13px;\n}\n\n.mailbox-read-message {\n  padding: 10px;\n}\n\n.mailbox-attachments {\n  &:extend(.list-unstyled);\n  li {\n    float: left;\n    width: 200px;\n    border: 1px solid #eee;\n    margin-bottom: 10px;\n    margin-right: 10px;\n  }\n}\n\n.mailbox-attachment-name {\n  font-weight: bold;\n  color: #666;\n}\n\n.mailbox-attachment-icon,\n.mailbox-attachment-info,\n.mailbox-attachment-size {\n  display: block;\n}\n\n.mailbox-attachment-info {\n  padding: 10px;\n  background: #f4f4f4;\n}\n\n.mailbox-attachment-size {\n  color: #999;\n  font-size: 12px;\n}\n\n.mailbox-attachment-icon {\n  text-align: center;\n  font-size: 65px;\n  color: #666;\n  padding: 20px 10px;\n  &.has-img {\n    padding: 0;\n    > img {\n      max-width: 100%;\n      height: auto;\n    }\n  }\n}\n\n.mailbox-attachment-close {\n  &:extend(.close);\n}\n", "/*\n * Page: Lock Screen\n * -----------------\n */\n/* ADD THIS CLASS TO THE <BODY> TAG */\n.lockscreen {\n  background: @gray-lte;\n}\n\n.lockscreen-logo {\n  font-size: 35px;\n  text-align: center;\n  margin-bottom: 25px;\n  font-weight: 300;\n  a {\n    color: #444;\n  }\n}\n\n.lockscreen-wrapper {\n  max-width: 400px;\n  margin: 0 auto;\n  margin-top: 10%;\n}\n\n/* User name [optional] */\n.lockscreen .lockscreen-name {\n  text-align: center;\n  font-weight: 600;\n}\n\n/* Will contain the image and the sign in form */\n.lockscreen-item {\n  .border-radius(4px);\n  padding: 0;\n  background: #fff;\n  position: relative;\n  margin: 10px auto 30px auto;\n  width: 290px;\n}\n\n/* User image */\n.lockscreen-image {\n  .border-radius(50%);\n  position: absolute;\n  left: -10px;\n  top: -25px;\n  background: #fff;\n  padding: 5px;\n  z-index: 10;\n  > img {\n    .border-radius(50%);\n    width: 70px;\n    height: 70px;\n  }\n}\n\n/* Contains the password input and the login button */\n.lockscreen-credentials {\n  margin-left: 70px;\n  .form-control {\n    border: 0;\n  }\n  .btn {\n    background-color: #fff;\n    border: 0;\n    padding: 0 10px;\n  }\n}\n\n.lockscreen-footer {\n  margin-top: 10px;\n}\n", "/*\n * Page: Login & Register\n * ----------------------\n */\n\n.login-logo,\n.register-logo {\n  font-size: 35px;\n  text-align: center;\n  margin-bottom: 25px;\n  font-weight: 300;\n  a {\n    color: #444;\n  }\n}\n\n.login-page,\n.register-page {\n  background: @gray-lte;\n}\n\n.login-box,\n.register-box {\n  width: 360px;\n  margin: 7% auto;\n  @media (max-width: @screen-sm) {\n    width: 90%;\n    margin-top: 20px;\n  }\n}\n\n.login-box-body,\n.register-box-body {\n  background: #fff;\n  padding: 20px;\n  border-top: 0;\n  color: #666;\n  .form-control-feedback {\n    color: #777;\n  }\n}\n\n.login-box-msg,\n.register-box-msg {\n  margin: 0;\n  text-align: center;\n  padding: 0 20px 20px 20px;\n}\n\n.social-auth-links {\n  margin: 10px 0;\n}\n", "/*\n * Page: 400 and 500 error pages\n * ------------------------------\n */\n.error-page {\n  width: 600px;\n  margin: 20px auto 0 auto;\n  @media (max-width: @screen-sm-max) {\n    width: 100%;\n  }\n  //For the error number e.g: 404\n  > .headline {\n    float: left;\n    font-size: 100px;\n    font-weight: 300;\n    @media (max-width: @screen-sm-max) {\n      float: none;\n      text-align: center;\n    }\n  }\n  //For the message\n  > .error-content {\n    margin-left: 190px;\n    @media (max-width: @screen-sm-max) {\n      margin-left: 0;\n    }\n    > h3 {\n      font-weight: 300;\n      font-size: 25px;\n      @media (max-width: @screen-sm-max) {\n        text-align: center;\n      }\n    }\n    display: block;\n  }\n}\n", "/*\n * Page: Invoice\n * -------------\n */\n\n.invoice {\n  position: relative;\n  background: #fff;\n  border: 1px solid #f4f4f4;\n  padding: 20px;\n  margin: 10px 25px;\n}\n\n.invoice-title {\n  margin-top: 0;\n}\n", "/*\n * Page: Profile\n * -------------\n */\n\n.profile-user-img {\n  margin: 0 auto;\n  width: 100px;\n  padding: 3px;\n  border: 3px solid @gray-lte;\n}\n\n.profile-username {\n  font-size: 21px;\n  margin-top: 5px;\n}\n\n.post {\n  border-bottom: 1px solid @gray-lte;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  color: #666;\n  &:last-of-type {\n    border-bottom: 0;\n    margin-bottom: 0;\n    padding-bottom: 0;\n  }\n  .user-block {\n    margin-bottom: 15px;\n  }\n}\n", "/*\n * Social Buttons for Bootstrap\n *\n * Copyright 2013-2015 Panayiotis Lipiridis\n * Licensed under the MIT License\n *\n * https://github.com/lipis/bootstrap-social\n */\n\n// Import variables and mixins as a reference for separate plugins version\n@import (reference) \"../bootstrap-less/mixins\";\n@import (reference) \"../bootstrap-less/variables\";\n@import (reference) \"variables\";\n@import (reference) \"mixins\";\n\n@bs-height-base: (@line-height-computed + @padding-base-vertical * 2);\n@bs-height-lg: (floor(@font-size-large * @line-height-base) + @padding-large-vertical * 2);\n@bs-height-sm: (floor(@font-size-small * 1.5) + @padding-small-vertical * 2);\n@bs-height-xs: (floor(@font-size-small * 1.2) + @padding-small-vertical + 1);\n\n.btn-social {\n  position: relative;\n  padding-left: (@bs-height-base + @padding-base-horizontal);\n  text-align: left;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  > :first-child {\n    position: absolute;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    width: @bs-height-base;\n    line-height: (@bs-height-base + 2);\n    font-size: 1.6em;\n    text-align: center;\n    border-right: 1px solid rgba(0, 0, 0, 0.2);\n  }\n  &.btn-lg {\n    padding-left: (@bs-height-lg + @padding-large-horizontal);\n    > :first-child {\n      line-height: @bs-height-lg;\n      width: @bs-height-lg;\n      font-size: 1.8em;\n    }\n  }\n  &.btn-sm {\n    padding-left: (@bs-height-sm + @padding-small-horizontal);\n    > :first-child {\n      line-height: @bs-height-sm;\n      width: @bs-height-sm;\n      font-size: 1.4em;\n    }\n  }\n  &.btn-xs {\n    padding-left: (@bs-height-xs + @padding-small-horizontal);\n    > :first-child {\n      line-height: @bs-height-xs;\n      width: @bs-height-xs;\n      font-size: 1.2em;\n    }\n  }\n}\n\n.btn-social-icon {\n  .btn-social;\n  height: (@bs-height-base + 2);\n  width: (@bs-height-base + 2);\n  padding: 0;\n  > :first-child {\n    border: none;\n    text-align: center;\n    width: 100%;\n  }\n  &.btn-lg {\n    height: @bs-height-lg;\n    width: @bs-height-lg;\n    padding-left: 0;\n    padding-right: 0;\n  }\n  &.btn-sm {\n    height: (@bs-height-sm + 2);\n    width: (@bs-height-sm + 2);\n    padding-left: 0;\n    padding-right: 0;\n  }\n  &.btn-xs {\n    height: (@bs-height-xs + 2);\n    width: (@bs-height-xs + 2);\n    padding-left: 0;\n    padding-right: 0;\n  }\n}\n\n.btn-social(@color-bg, @color: #fff) {\n  background-color: @color-bg;\n  .button-variant(@color, @color-bg, rgba(0, 0, 0, .2));\n}\n\n.btn-adn {\n  .btn-social(#d87a68);\n}\n\n.btn-bitbucket {\n  .btn-social(#205081);\n}\n\n.btn-dropbox {\n  .btn-social(#1087dd);\n}\n\n.btn-facebook {\n  .btn-social(#3b5998);\n}\n\n.btn-flickr {\n  .btn-social(#ff0084);\n}\n\n.btn-foursquare {\n  .btn-social(#f94877);\n}\n\n.btn-github {\n  .btn-social(#444444);\n}\n\n.btn-google {\n  .btn-social(#dd4b39);\n}\n\n.btn-instagram {\n  .btn-social(#3f729b);\n}\n\n.btn-linkedin {\n  .btn-social(#007bb6);\n}\n\n.btn-microsoft {\n  .btn-social(#2672ec);\n}\n\n.btn-openid {\n  .btn-social(#f7931e);\n}\n\n.btn-pinterest {\n  .btn-social(#cb2027);\n}\n\n.btn-reddit {\n  .btn-social(#eff7ff, #000);\n}\n\n.btn-soundcloud {\n  .btn-social(#ff5500);\n}\n\n.btn-tumblr {\n  .btn-social(#2c4762);\n}\n\n.btn-twitter {\n  .btn-social(#55acee);\n}\n\n.btn-vimeo {\n  .btn-social(#1ab7ea);\n}\n\n.btn-vk {\n  .btn-social(#587ea3);\n}\n\n.btn-yahoo {\n  .btn-social(#720e9e);\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n.button-variant(@color; @background; @border) {\n  color: @color;\n  background-color: @background;\n  border-color: @border;\n\n  &:focus,\n  &.focus {\n    color: @color;\n    background-color: darken(@background, 10%);\n    border-color: darken(@border, 25%);\n  }\n  &:hover {\n    color: @color;\n    background-color: darken(@background, 10%);\n    border-color: darken(@border, 12%);\n  }\n  &:active,\n  &.active,\n  .open > .dropdown-toggle& {\n    color: @color;\n    background-color: darken(@background, 10%);\n    border-color: darken(@border, 12%);\n\n    &:hover,\n    &:focus,\n    &.focus {\n      color: @color;\n      background-color: darken(@background, 17%);\n      border-color: darken(@border, 25%);\n    }\n  }\n  &:active,\n  &.active,\n  .open > .dropdown-toggle& {\n    background-image: none;\n  }\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    &,\n    &:hover,\n    &:focus,\n    &.focus,\n    &:active,\n    &.active {\n      background-color: @background;\n      border-color: @border;\n    }\n  }\n\n  .badge {\n    color: @background;\n    background-color: @color;\n  }\n}\n\n// Button sizes\n.button-size(@padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius) {\n  padding: @padding-vertical @padding-horizontal;\n  font-size: @font-size;\n  line-height: @line-height;\n  border-radius: @border-radius;\n}\n", "/*\n * Plugin: Full Calendar\n * ---------------------\n */\n\n// Import variables and mixins as a reference for separate plugins version\n@import (reference) \"../bootstrap-less/mixins\";\n@import (reference) \"../bootstrap-less/variables\";\n@import (reference) \"variables\";\n@import (reference) \"mixins\";\n\n//Fullcalendar buttons\n.fc-button {\n  background: #f4f4f4;\n  background-image: none;\n  color: #444;\n  border-color: #ddd;\n  border-bottom-color: #ddd;\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: #e9e9e9;\n  }\n}\n\n// Calendar title\n.fc-header-title h2 {\n  font-size: 15px;\n  line-height: 1.6em;\n  color: #666;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n// Calendar table header cells\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  width: 100%;\n  border: 0;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar {\n  padding: @box-padding;\n  margin: 0;\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  > li {\n    float: left;\n    font-size: 30px;\n    margin-right: 5px;\n    line-height: 30px;\n    .fa {\n      .transition-transform(linear .3s);\n      &:hover {\n        .rotate(30deg);\n      }\n    }\n  }\n}\n\n#add-new-event {\n  .transition(all linear .3s);\n}\n\n.external-event {\n  padding: 5px 10px;\n  font-weight: bold;\n  margin-bottom: 4px;\n  box-shadow: @box-boxshadow;\n  text-shadow: @box-boxshadow;\n  border-radius: @box-border-radius;\n  cursor: move;\n  &:hover {\n    box-shadow: inset 0 0 90px rgba(0, 0, 0, 0.2);\n  }\n}\n", "/*\n * Plugin: Select2\n * ---------------\n */\n\n// Import variables and mixins as a reference for separate plugins version\n@import (reference) \"../bootstrap-less/mixins\";\n@import (reference) \"../bootstrap-less/variables\";\n@import (reference) \"variables\";\n@import (reference) \"mixins\";\n\n//Signle select\n.select2-container--default,\n.select2-selection {\n  &.select2-container--focus,\n  &:focus,\n  &:active {\n    outline: none;\n  }\n  .select2-selection--single {\n    border: 1px solid @gray-lte;\n    border-radius: @input-radius;\n    padding: 6px 12px;\n    height: 34px;\n  }\n}\n\n.select2-container--default.select2-container--open {\n  border-color: @light-blue;\n}\n\n.select2-dropdown {\n  border: 1px solid @gray-lte;\n  border-radius: @input-radius;\n}\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: @light-blue;\n  color: white;\n}\n\n.select2-results__option {\n  padding: 6px 12px;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-left: 0;\n  padding-right: 0;\n  height: auto;\n  margin-top: -4px;\n}\n\n.select2-container[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n  padding-right: 6px;\n  padding-left: 20px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow {\n  height: 28px;\n  right: 3px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow b {\n  margin-top: 0;\n}\n\n.select2-dropdown,\n.select2-search--inline {\n  .select2-search__field {\n    border: 1px solid @gray-lte;\n    &:focus {\n      outline: none;\n      border: 1px solid @light-blue;\n    }\n  }\n}\n\n.select2-container--default .select2-results__option[aria-disabled=true] {\n  color: #999;\n}\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #ddd;\n  &,\n  &:hover {\n    color: #444;\n  }\n}\n\n//Multiple select\n.select2-container--default {\n  .select2-selection--multiple {\n    border: 1px solid @gray-lte;\n    border-radius: @input-radius;\n    &:focus {\n      border-color: @light-blue;\n    }\n  }\n  &.select2-container--focus .select2-selection--multiple {\n    border-color: @gray-lte;\n  }\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: @light-blue;\n  border-color: darken(@light-blue, 5%);\n  padding: 1px 10px;\n  color: #fff;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  margin-right: 5px;\n  color: rgba(255, 255, 255, .7);\n  &:hover {\n    color: #fff;\n  }\n}\n\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-right: 10px;\n}\n", "/*\n * General: Miscellaneous\n * ----------------------\n */\n// 10px padding and margins\n.pad {\n  padding: 10px;\n}\n\n.margin {\n  margin: 10px;\n}\n\n.margin-bottom {\n  margin-bottom: 20px;\n}\n\n.margin-bottom-none {\n  margin-bottom: 0;\n}\n\n.margin-r-5 {\n  margin-right: 5px;\n}\n\n// Display inline\n.inline {\n  display: inline;\n}\n\n// Description Blocks\n.description-block {\n  display: block;\n  margin: 10px 0;\n  text-align: center;\n  &.margin-bottom {\n    margin-bottom: 25px;\n  }\n  > .description-header {\n    margin: 0;\n    padding: 0;\n    font-weight: 600;\n    font-size: 16px;\n  }\n  > .description-text {\n    text-transform: uppercase;\n  }\n}\n\n// Background colors\n.bg-red,\n.bg-yellow,\n.bg-aqua,\n.bg-blue,\n.bg-light-blue,\n.bg-green,\n.bg-navy,\n.bg-teal,\n.bg-olive,\n.bg-lime,\n.bg-orange,\n.bg-fuchsia,\n.bg-purple,\n.bg-maroon,\n.bg-black,\n.bg-red-active,\n.bg-yellow-active,\n.bg-aqua-active,\n.bg-blue-active,\n.bg-light-blue-active,\n.bg-green-active,\n.bg-navy-active,\n.bg-teal-active,\n.bg-olive-active,\n.bg-lime-active,\n.bg-orange-active,\n.bg-fuchsia-active,\n.bg-purple-active,\n.bg-maroon-active,\n.bg-black-active {\n  color: #fff !important;\n}\n\n.bg-gray {\n  color: #000;\n  background-color: @gray-lte !important;\n}\n\n.bg-gray-light {\n  background-color: #f7f7f7;\n}\n\n.bg-black {\n  background-color: @black !important;\n}\n\n.bg-red {\n  background-color: @red !important;\n}\n\n.bg-yellow {\n  background-color: @yellow !important;\n}\n\n.bg-aqua {\n  background-color: @aqua !important;\n}\n\n.bg-blue {\n  background-color: @blue !important;\n}\n\n.bg-light-blue {\n  background-color: @light-blue !important;\n}\n\n.bg-green {\n  background-color: @green !important;\n}\n\n.bg-navy {\n  background-color: @navy !important;\n}\n\n.bg-teal {\n  background-color: @teal !important;\n}\n\n.bg-olive {\n  background-color: @olive !important;\n}\n\n.bg-lime {\n  background-color: @lime !important;\n}\n\n.bg-orange {\n  background-color: @orange !important;\n}\n\n.bg-fuchsia {\n  background-color: @fuchsia !important;\n}\n\n.bg-purple {\n  background-color: @purple !important;\n}\n\n.bg-maroon {\n  background-color: @maroon !important;\n}\n\n//Set of Active Background Colors\n.bg-gray-active {\n  color: #000;\n  background-color: darken(@gray-lte, 10%) !important;\n}\n\n.bg-black-active {\n  background-color: darken(@black, 10%) !important;\n}\n\n.bg-red-active {\n  background-color: darken(@red , 6%) !important;\n}\n\n.bg-yellow-active {\n  background-color: darken(@yellow , 6%) !important;\n}\n\n.bg-aqua-active {\n  background-color: darken(@aqua , 6%) !important;\n}\n\n.bg-blue-active {\n  background-color: darken(@blue , 10%) !important;\n}\n\n.bg-light-blue-active {\n  background-color: darken(@light-blue , 6%) !important;\n}\n\n.bg-green-active {\n  background-color: darken(@green , 5%) !important;\n}\n\n.bg-navy-active {\n  background-color: darken(@navy , 2%) !important;\n}\n\n.bg-teal-active {\n  background-color: darken(@teal , 5%) !important;\n}\n\n.bg-olive-active {\n  background-color: darken(@olive , 5%) !important;\n}\n\n.bg-lime-active {\n  background-color: darken(@lime , 5%) !important;\n}\n\n.bg-orange-active {\n  background-color: darken(@orange , 5%) !important;\n}\n\n.bg-fuchsia-active {\n  background-color: darken(@fuchsia , 5%) !important;\n}\n\n.bg-purple-active {\n  background-color: darken(@purple , 5%) !important;\n}\n\n.bg-maroon-active {\n  background-color: darken(@maroon , 3%) !important;\n}\n\n//Disabled!\n[class^=\"bg-\"].disabled {\n  .opacity(.65);\n}\n\n// Text colors\n.text-red {\n  color: @red !important;\n}\n\n.text-yellow {\n  color: @yellow !important;\n}\n\n.text-aqua {\n  color: @aqua !important;\n}\n\n.text-blue {\n  color: @blue !important;\n}\n\n.text-black {\n  color: @black !important;\n}\n\n.text-light-blue {\n  color: @light-blue !important;\n}\n\n.text-green {\n  color: @green !important;\n}\n\n.text-gray {\n  color: @gray-lte !important;\n}\n\n.text-navy {\n  color: @navy !important;\n}\n\n.text-teal {\n  color: @teal !important;\n}\n\n.text-olive {\n  color: @olive !important;\n}\n\n.text-lime {\n  color: @lime !important;\n}\n\n.text-orange {\n  color: @orange !important;\n}\n\n.text-fuchsia {\n  color: @fuchsia !important;\n}\n\n.text-purple {\n  color: @purple !important;\n}\n\n.text-maroon {\n  color: @maroon !important;\n}\n\n.link-muted {\n  color: darken(@gray-lte, 30%);\n  &:hover,\n  &:focus {\n    color: darken(@gray-lte, 40%);\n  }\n}\n\n.link-black {\n  color: #666;\n  &:hover,\n  &:focus {\n    color: #999;\n  }\n}\n\n// Hide elements by display none only\n.hide {\n  display: none !important;\n}\n\n// Remove borders\n.no-border {\n  border: 0 !important;\n}\n\n// Remove padding\n.no-padding {\n  padding: 0 !important;\n}\n\n// Remove margins\n.no-margin {\n  margin: 0 !important;\n}\n\n// Remove box shadow\n.no-shadow {\n  box-shadow: none !important;\n}\n\n// Unstyled List\n.list-unstyled {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.list-group-unbordered {\n  > .list-group-item {\n    border-left: 0;\n    border-right: 0;\n    border-radius: 0;\n    padding-left: 0;\n    padding-right: 0;\n  }\n}\n\n// Remove border radius\n.flat {\n  .border-radius(0) !important;\n}\n\n.text-bold {\n  &, &.table td, &.table th {\n    font-weight: 700;\n  }\n}\n\n.text-sm {\n  font-size: 12px;\n}\n\n// _fix for sparkline tooltip\n.jqstooltip {\n  padding: 5px !important;\n  width: auto !important;\n  height: auto !important;\n}\n\n// Gradient Background colors\n.bg-teal-gradient {\n  .gradient(@teal; @teal; lighten(@teal, 16%)) !important;\n  color: #fff;\n}\n\n.bg-light-blue-gradient {\n  .gradient(@light-blue; @light-blue; lighten(@light-blue, 12%)) !important;\n  color: #fff;\n}\n\n.bg-blue-gradient {\n  .gradient(@blue; @blue; lighten(@blue, 7%)) !important;\n  color: #fff;\n}\n\n.bg-aqua-gradient {\n  .gradient(@aqua; @aqua; lighten(@aqua, 7%)) !important;\n  color: #fff;\n}\n\n.bg-yellow-gradient {\n  .gradient(@yellow; @yellow; lighten(@yellow, 16%)) !important;\n  color: #fff;\n}\n\n.bg-purple-gradient {\n  .gradient(@purple; @purple; lighten(@purple, 16%)) !important;\n  color: #fff;\n}\n\n.bg-green-gradient {\n  .gradient(@green; @green; lighten(@green, 7%)) !important;\n  color: #fff;\n}\n\n.bg-red-gradient {\n  .gradient(@red; @red; lighten(@red, 10%)) !important;\n  color: #fff;\n}\n\n.bg-black-gradient {\n  .gradient(@black; @black; lighten(@black, 10%)) !important;\n  color: #fff;\n}\n\n.bg-maroon-gradient {\n  .gradient(@maroon; @maroon; lighten(@maroon, 10%)) !important;\n  color: #fff;\n}\n\n//Description Block Extension\n.description-block {\n  .description-icon {\n    font-size: 16px;\n  }\n}\n\n//Remove top padding\n.no-pad-top {\n  padding-top: 0;\n}\n\n//Make position static\n.position-static {\n  position: static !important;\n}\n\n//List utility classes\n.list-header {\n  font-size: 15px;\n  padding: 10px 4px;\n  font-weight: bold;\n  color: #666;\n}\n\n.list-seperator {\n  height: 1px;\n  background: @box-border-color;\n  margin: 15px 0 9px 0;\n}\n\n.list-link {\n  > a {\n    padding: 4px;\n    color: #777;\n    &:hover {\n      color: #222;\n    }\n  }\n}\n\n//Light font weight\n.font-light {\n  font-weight: 300;\n}\n\n//User block\n.user-block {\n  .clearfix();\n  img {\n    width: 40px;\n    height: 40px;\n    float: left;\n  }\n  .username,\n  .description,\n  .comment {\n    display: block;\n    margin-left: 50px;\n  }\n  .username {\n    font-size: 16px;\n    font-weight: 600;\n  }\n  .description {\n    color: #999;\n    font-size: 13px;\n  }\n  &.user-block-sm {\n    img {\n      &:extend(.img-sm);\n    }\n    .username,\n    .description,\n    .comment {\n      margin-left: 40px;\n    }\n    .username {\n      font-size: 14px;\n    }\n  }\n}\n\n//Image sizes\n.img-sm,\n.img-md,\n.img-lg {\n  float: left;\n}\n\n.img-sm {\n  width: 30px !important;\n  height: 30px !important;\n  + .img-push {\n    margin-left: 40px;\n  }\n}\n\n.img-md {\n  width: 60px;\n  height: 60px;\n  + .img-push {\n    margin-left: 70px;\n  }\n}\n\n.img-lg {\n  width: 100px;\n  height: 100px;\n  + .img-push {\n    margin-left: 110px;\n  }\n}\n\n// Image bordered\n.img-bordered {\n  border: 3px solid @gray-lte;\n  padding: 3px;\n}\n\n.img-bordered-sm {\n  border: 2px solid @gray-lte;\n  padding: 2px;\n}\n\n//General attachemnt block\n.attachment-block {\n  border: 1px solid @box-border-color;\n  padding: 5px;\n  margin-bottom: 10px;\n  background: #f7f7f7;\n\n  .attachment-img {\n    max-width: 100px;\n    max-height: 100px;\n    height: auto;\n    float: left;\n  }\n  .attachment-pushed {\n    margin-left: 110px;\n  }\n  .attachment-heading {\n    margin: 0;\n  }\n  .attachment-text {\n    color: #555;\n  }\n}\n\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: #f4f4f4;\n  border: 1px dashed #ddd;\n  margin-bottom: 10px;\n}\n\n.full-opacity-hover {\n  .opacity(.65);\n  &:hover {\n    .opacity(1);\n  }\n}\n\n// Charts\n.chart {\n  position: relative;\n  overflow: hidden;\n  width: 100%;\n  svg,\n  canvas {\n    width: 100% !important;\n  }\n}\n", "/*\n * Misc: print\n * -----------\n */\n@media print {\n  //Add to elements that you do not want to show when printing\n  .no-print {\n    display: none !important;\n  }\n\n  //Elements that we want to hide when printing\n  .main-sidebar,\n  .left-side,\n  .main-header,\n  .content-header {\n    &:extend(.no-print);\n  }\n\n  //This is the only element that should appear, so let's remove the margins\n  .content-wrapper,\n  .right-side,\n  .main-footer {\n    margin-left: 0 !important;\n    min-height: 0 !important;\n    .translate(0, 0) !important;\n  }\n\n  .fixed .content-wrapper,\n  .fixed .right-side {\n    padding-top: 0 !important;\n  }\n\n  //Invoice printing\n  .invoice {\n    width: 100%;\n    border: 0;\n    margin: 0;\n    padding: 0;\n  }\n\n  .invoice-col {\n    float: left;\n    width: 33.3333333%;\n  }\n\n  //Make sure table content displays properly\n  .table-responsive {\n    overflow: auto;\n    > .table tr th,\n    > .table tr td {\n      white-space: normal !important;\n    }\n  }\n}\n"]}