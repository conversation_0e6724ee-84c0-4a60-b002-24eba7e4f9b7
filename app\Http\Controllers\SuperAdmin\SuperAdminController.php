<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use Session;

class SuperAdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('superadmin');
    }

    function register_admin()
    {
        // Get all colleges for dropdown
        $colleges = \App\College::where('is_active', 1)->get();
        return view('super_admin.register_admin', compact('colleges'));
    }

    function register_admin_save(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|max:255|unique:users',
            'name' => 'required|max:255',
            'lastname' => 'required|max:255',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|min:6|confirmed',
            'role' => 'required|in:0,50', // 0 for Admin, 50 for College Admin
        ]);

        if ($validator->fails()) {
            return redirect('/superadmin/register_admin')
                ->withErrors($validator)
                ->withInput();
        } else {
            // Determine if college_code is required for College Admin
            if ($request->role == 50 && empty($request->college_code)) {
                return redirect('/superadmin/register_admin')
                    ->withErrors(['college_code' => 'The college code field is required for College Admin.'])
                    ->withInput();
            }

            \App\User::create([
                'username' => $request->username,
                'name' => $request->name,
                'middlename' => $request->middlename,
                'lastname' => $request->lastname,
                'accesslevel' => $request->role,
                'college_code' => $request->role == 50 ? $request->college_code : null,
                'email' => $request->email,
                'password' => bcrypt($request->password),
            ]);

            $roleText = $request->role == 0 ? 'Administrator' : 'College Admin';
            Session::flash('success', "Successfully Created a {$roleText}!");
            return redirect('/superadmin/register_admin');
        }
    }
}
