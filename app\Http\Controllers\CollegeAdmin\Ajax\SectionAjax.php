<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\CtrSection;
use App\College;
use App\curriculum;
use App\Services\ProgramService;
use Auth;
use View;
use Illuminate\Support\Facades\Request as RequestFacade;
use Illuminate\Support\Facades\Input;

class SectionAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Get available curriculum subjects for a section (college admin)
     */
    public function getAvailableCurricula()
    {
        if (RequestFacade::ajax()) {
            $section_id = Input::get('section_id');
            $collegeCode = Auth::user()->college_code;

            $section = CtrSection::where('id', $section_id)
                ->where('college_code', $collegeCode)
                ->first();

            if (!$section) {
                return response()->json([
                    'success' => false,
                    'message' => 'Section not found or you do not have permission to access it'
                ]);
            }

            $availableCurricula = $section->getAvailableCurricula();

            return view('collegeadmin.section_management.ajax.get_available_curricula', compact('section', 'availableCurricula'))->render();
        }
    }

    /**
     * Search sections with curricula (college admin)
     */
    public function searchSectionsWithCurricula()
    {
        // Enhanced debugging - log all request details
        \Log::info('College Admin Section Search Request', [
            'is_ajax' => RequestFacade::ajax(),
            'request_method' => request()->method(),
            'request_headers' => request()->headers->all(),
            'request_data' => request()->all(),
            'user_id' => Auth::id(),
            'user_college_code' => Auth::user()->college_code ?? 'null'
        ]);

        // Allow both AJAX and regular requests for debugging
        if (RequestFacade::ajax() || request()->has('debug')) {
            $collegeCode = Auth::user()->college_code;
            $program_code = Input::get('program_code');
            $level = Input::get('level');
            $section_name = Input::get('section_name');

            // Debug logging
            \Log::info('College Admin Section Search', [
                'college_code' => $collegeCode,
                'program_code' => $program_code,
                'level' => $level,
                'section_name' => $section_name,
                'user_id' => Auth::id()
            ]);

            $query = CtrSection::where('is_active', 1)
                ->where('college_code', $collegeCode);

            if (!empty($program_code) && $program_code !== 'All Programs') {
                $query->where('program_code', $program_code);
            }

            if (!empty($level) && $level !== 'All Levels') {
                $query->where('level', $level);
            }

            if (!empty($section_name)) {
                $query->where('section_name', 'LIKE', '%' . $section_name . '%');
            }

            $sections = $query->get();

            \Log::info('College Admin Section Search Results', [
                'sections_found' => $sections->count(),
                'query_sql' => $query->toSql()
            ]);

            // If debug parameter is present, return JSON for easier debugging
            if (request()->has('debug')) {
                return response()->json([
                    'success' => true,
                    'college_code' => $collegeCode,
                    'filters' => [
                        'program_code' => $program_code,
                        'level' => $level,
                        'section_name' => $section_name
                    ],
                    'sections_found' => $sections->count(),
                    'sections' => $sections,
                    'query_sql' => $query->toSql()
                ]);
            }

            return view('collegeadmin.section_management.ajax.search_results', compact('sections'))->render();
        }

        // If not AJAX request, return error with more details
        return response()->json([
            'error' => 'Invalid request',
            'is_ajax' => RequestFacade::ajax(),
            'request_method' => request()->method(),
            'user_authenticated' => Auth::check(),
            'user_id' => Auth::id()
        ], 400);
    }

    /**
     * Edit section modal (college admin)
     */
    public function editSection()
    {
        if (RequestFacade::ajax()) {
            $section_id = Input::get('section_id');
            $collegeCode = Auth::user()->college_code;

            $section = CtrSection::where('id', $section_id)
                ->where('college_code', $collegeCode)
                ->first();

            if (!$section) {
                return '<div class="alert alert-danger">Section not found or you do not have permission to edit it</div>';
            }

            // Get college information
            $college = College::where('college_code', $collegeCode)->first();

            // Get programs from this college using ProgramService
            $programService = new ProgramService();
            $programs = $programService->getProgramsByCollege($collegeCode);

            return View::make('collegeadmin.section_management.ajax.edit_section', compact('section', 'programs', 'college'))->render();
        }
    }

    /**
     * Get sections for a specific program and level (college admin)
     */
    public function getSections()
    {
        if (RequestFacade::ajax()) {
            $level = Input::get('level');
            $program_code = Input::get('program_code');
            $collegeCode = Auth::user()->college_code;

            $sections = CtrSection::where('level', $level)
                ->where('is_active', 1)
                ->where('program_code', $program_code)
                ->where('college_code', $collegeCode)
                ->get();

            return view('collegeadmin.section_management.ajax.get_sections', compact('sections'))->render();
        }
    }

    /**
     * Get programs for a specific college (AJAX helper)
     */
    public function getPrograms()
    {
        if (RequestFacade::ajax()) {
            $college_code = Input::get('college_code');
            $collegeCode = Auth::user()->college_code;

            // Ensure the requested college matches the logged-in college admin's college
            if ($college_code !== $collegeCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only access programs from your college'
                ]);
            }

            $programService = new ProgramService();
            $programs = $programService->getProgramsByCollege($college_code);

            return view('collegeadmin.section_management.ajax.get_programs', compact('programs'))->render();
        }
    }

    /**
     * Get curriculum subjects for a specific program and level (college admin)
     */
    public function getCurriculaForProgram()
    {
        if (RequestFacade::ajax()) {
            $program_code = Input::get('program_code');
            $level = Input::get('level');
            $collegeCode = Auth::user()->college_code;

            // Verify the program belongs to this college
            $programService = new ProgramService();
            $programCollegeCode = $programService->getCollegeCodeForProgram($program_code);

            if ($programCollegeCode !== $collegeCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only access curricula from your college'
                ]);
            }

            $curricula = curriculum::where('program_code', $program_code)
                ->where('level', $level)
                ->where('college_code', $collegeCode)
                ->where('is_active', 1)
                ->get();

            return view('collegeadmin.section_management.ajax.get_curricula', compact('curricula'))->render();
        }
    }

    /**
     * Search archived sections (college admin)
     */
    public function searchArchivedSections()
    {
        if (RequestFacade::ajax()) {
            $collegeCode = Auth::user()->college_code;
            $program_code = Input::get('program_code');
            $level = Input::get('level');
            $section_name = Input::get('section_name');

            $query = CtrSection::where('is_active', 0)
                ->where('college_code', $collegeCode);

            if (!empty($program_code) && $program_code !== 'All Programs') {
                $query->where('program_code', $program_code);
            }

            if (!empty($level) && $level !== 'All Levels') {
                $query->where('level', $level);
            }

            if (!empty($section_name)) {
                $query->where('section_name', 'LIKE', '%' . $section_name . '%');
            }

            $sections = $query->get();

            return view('collegeadmin.section_management.ajax.search_archived_results', compact('sections'))->render();
        }
    }

    /**
     * Restore an archived section (college admin)
     */
    public function restoreSection()
    {
        if (RequestFacade::ajax()) {
            $section_id = Input::get('section_id');
            $collegeCode = Auth::user()->college_code;

            $section = CtrSection::where('id', $section_id)
                ->where('college_code', $collegeCode)
                ->where('is_active', 0)
                ->first();

            if (!$section) {
                return response()->json([
                    'success' => false,
                    'message' => 'Section not found or you do not have permission to restore it'
                ]);
            }

            $section->is_active = 1;
            $section->save();

            return response()->json([
                'success' => true,
                'message' => "Section '{$section->section_name}' has been restored successfully!"
            ]);
        }
    }
}
