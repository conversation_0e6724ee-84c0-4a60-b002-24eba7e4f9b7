<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-list"></i>
        Curriculum Subjects
        <small>{{ $programCode }} ({{ $curriculumYear }})</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.view_curricula', $programCode) }}">View Curricula</a></li>
        <li class="active">Curriculum Subjects</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Subjects for {{ $programCode }} ({{ $curriculumYear }})</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.curriculum.fix_data') }}" class="btn btn-warning btn-sm">
                            <i class="fa fa-wrench"></i> Fix Missing Data
                        </a>
                        <a href="{{ route('collegeadmin.curriculum.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Add New Curriculum
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($curriculum) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Subject Code</th>
                                        <th>Subject Name</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                        <th>Level</th>
                                        <th>Period</th>
                                        <th>CompLab</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($curriculum as $subject)
                                        <tr>
                                            <td>{{ $subject->course_code }}</td>
                                            <td>{{ $subject->course_name }}</td>
                                            <td>{{ $subject->lec }}</td>
                                            <td>{{ $subject->lab }}</td>
                                            <td>{{ $subject->units }}</td>
                                            <td>{{ $subject->level }}</td>
                                            <td>{{ $subject->period }}</td>
                                            <td>{{ $subject->is_complab ? 'Yes' : 'No' }}</td>
                                            <td>
                                                <a href="{{ route('collegeadmin.curriculum.archive_subject', $subject->id) }}" class="btn btn-warning btn-sm" onclick="return confirm('Are you sure you want to archive this subject?')">
                                                    <i class="fa fa-archive"></i> Archive
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No subjects found for this curriculum.
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.curriculum.view_curricula', $programCode) }}" class="btn btn-default">Back to Curriculum Years</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
