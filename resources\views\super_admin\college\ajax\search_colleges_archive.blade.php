@if(!$colleges->isEmpty())
    @foreach($colleges as $college)
    <tr>
        <td>{{ $college->college_code }}</td>
        <td>{{ $college->college_name }}</td>
        <td>
            @php
                $programs = \App\Program::where('college_id', $college->id)->get();
            @endphp
            @if(count($programs) > 0)
                <ul class="list-unstyled">
                    @foreach($programs as $program)
                        <li><span class="label label-info">{{ $program->program_code }} @if(!empty($program->program_name)) - {{ $program->program_name }} @endif</span></li>
                    @endforeach
                </ul>
            @else
                <span class="text-muted">No programs specified</span>
            @endif
        </td>
        <td>
            <a href="{{ url('/superadmin/college/archive', [$college->id]) }}" class="btn btn-flat btn-success" title="Change to Active Status?" onclick="return confirm('Do you wish to restore the Record?')"><i class="fa fa-recycle"></i></a>
        </td>
    </tr>
    @endforeach
@else
    <tr>
        <td colspan="4" class="text-center">No colleges found matching your search criteria.</td>
    </tr>
@endif
