<?php

namespace App\Http\Controllers\Instructor;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\CtrRoom;
use App\CtrSection;
use App\instructors_infos;
use App\room_schedules;
use App\User;
use App\curriculum;
use App\offerings_infos_table;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use PDF;

class InstructorReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
    }

    /**
     * Export teaching schedule as PDF
     */
    public function exportSchedule()
    {
        // Verify user is an instructor
        if (Auth::user()->accesslevel != 1) {
            return redirect('/401');
        }

        $instructor_id = Auth::user()->id;
        $instructor = User::find($instructor_id);
        
        // Get teaching schedule
        $schedules = $this->getTeachingSchedule($instructor_id);
        
        // Generate PDF
        $pdf = PDF::loadView('instructor.reports.teaching_schedule', compact('instructor', 'schedules'));
        return $pdf->download('teaching_schedule_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export attendance report as PDF
     */
    public function exportAttendance(Request $request)
    {
        // Verify user is an instructor
        if (Auth::user()->accesslevel != 1) {
            return redirect('/401');
        }

        $instructor_id = Auth::user()->id;
        $instructor = User::find($instructor_id);
        $offering_id = $request->input('offering_id');
        
        // Get attendance data
        if ($offering_id) {
            $attendance_data = $this->getAttendanceDataForCourse($instructor_id, $offering_id);
            $course_info = $this->getCourseInfo($offering_id);
            
            // Generate PDF for specific course
            $pdf = PDF::loadView('instructor.reports.course_attendance', compact('instructor', 'attendance_data', 'course_info'));
            return $pdf->download('attendance_' . $course_info['course_code'] . '_' . date('Y-m-d') . '.pdf');
        } else {
            // Get summary for all courses
            $attendance_summary = $this->getAttendanceSummary($instructor_id);
            
            // Generate PDF for all courses
            $pdf = PDF::loadView('instructor.reports.attendance_summary', compact('instructor', 'attendance_summary'));
            return $pdf->download('attendance_summary_' . date('Y-m-d') . '.pdf');
        }
    }

    /**
     * Get teaching schedule for an instructor
     */
    private function getTeachingSchedule($instructor_id)
    {
        $schedules = room_schedules::where('instructor', $instructor_id)
            ->where('is_active', 1)
            ->get();
            
        $formatted_schedules = [];
        
        foreach ($schedules as $schedule) {
            $offering = offerings_infos_table::find($schedule->offering_id);
            
            if ($offering) {
                $curriculum_details = curriculum::find($offering->curriculum_id);
                
                if ($curriculum_details) {
                    $formatted_schedules[] = [
                        'id' => $schedule->id,
                        'day' => $schedule->day,
                        'time_starts' => $schedule->time_starts,
                        'time_end' => $schedule->time_end,
                        'room' => $schedule->room,
                        'course_code' => $curriculum_details->course_code,
                        'course_name' => $curriculum_details->course_name,
                        'section_name' => $offering->section_name,
                        'level' => $offering->level
                    ];
                }
            }
        }
        
        return $formatted_schedules;
    }

    /**
     * Get attendance summary for an instructor's classes
     */
    private function getAttendanceSummary($instructor_id)
    {
        // Get all courses taught by this instructor
        $schedules = room_schedules::where('instructor', $instructor_id)
            ->where('is_active', 1)
            ->get()
            ->unique('offering_id');
            
        $attendance_summary = [];
        
        foreach ($schedules as $schedule) {
            $offering = offerings_infos_table::find($schedule->offering_id);
            
            if ($offering) {
                $curriculum_details = curriculum::find($offering->curriculum_id);
                
                if ($curriculum_details) {
                    // For now, we'll use placeholder data for attendance
                    // In a real implementation, this would come from an attendance database
                    $attendance_summary[] = [
                        'offering_id' => $schedule->offering_id,
                        'course_code' => $curriculum_details->course_code,
                        'course_name' => $curriculum_details->course_name,
                        'section_name' => $offering->section_name,
                        'total_classes' => rand(10, 30), // Placeholder
                        'total_students' => rand(15, 40), // Placeholder
                        'attendance_rate' => rand(75, 100) // Placeholder percentage
                    ];
                }
            }
        }
        
        return $attendance_summary;
    }

    /**
     * Get detailed attendance data for a specific course
     */
    private function getAttendanceDataForCourse($instructor_id, $offering_id)
    {
        // In a real implementation, this would fetch actual attendance data
        // For now, we'll generate placeholder data
        
        // Generate placeholder attendance data
        $total_students = rand(15, 40);
        $attendance_data = [];
        
        // Generate data for the last 10 sessions
        for ($i = 10; $i >= 1; $i--) {
            $date = Carbon::now()->subDays($i * 7)->format('Y-m-d');
            $present = rand(round($total_students * 0.7), $total_students);
            
            $attendance_data[] = [
                'date' => $date,
                'present' => $present,
                'absent' => $total_students - $present,
                'percentage' => round(($present / $total_students) * 100, 2)
            ];
        }
        
        return [
            'total_students' => $total_students,
            'sessions' => $attendance_data
        ];
    }

    /**
     * Get course information
     */
    private function getCourseInfo($offering_id)
    {
        $offering = offerings_infos_table::find($offering_id);
        
        if (!$offering) {
            return null;
        }
        
        $curriculum_details = curriculum::find($offering->curriculum_id);
        
        if (!$curriculum_details) {
            return null;
        }
        
        return [
            'offering_id' => $offering->id,
            'course_code' => $curriculum_details->course_code,
            'course_name' => $curriculum_details->course_name,
            'section_name' => $offering->section_name,
            'level' => $offering->level
        ];
    }
}
