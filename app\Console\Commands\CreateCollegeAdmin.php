<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\User;
use App\College;
use Illuminate\Support\Facades\Hash;

class CreateCollegeAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:create-college-admin {username} {firstname} {lastname} {email} {password} {college_code} {--middlename=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new College Admin user';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $username = $this->argument('username');
        $firstname = $this->argument('firstname');
        $middlename = $this->option('middlename');
        $lastname = $this->argument('lastname');
        $email = $this->argument('email');
        $password = $this->argument('password');
        $collegeCode = $this->argument('college_code');

        // Check if the username already exists
        if (User::where('username', $username)->exists()) {
            $this->error("Username '{$username}' already exists!");
            return 1;
        }

        // Check if the email already exists
        if (User::where('email', $email)->exists()) {
            $this->error("Email '{$email}' already exists!");
            return 1;
        }

        // Check if the college exists
        $college = College::where('college_code', $collegeCode)->first();
        if (!$college) {
            $this->error("College with code '{$collegeCode}' does not exist!");
            return 1;
        }

        // Create the user
        $user = new User();
        $user->username = $username;
        $user->name = $firstname;
        $user->middlename = $middlename;
        $user->lastname = $lastname;
        $user->accesslevel = 50; // College Admin
        $user->college_code = $collegeCode;
        $user->email = $email;
        $user->password = Hash::make($password);
        $user->is_first_login = 0; // No need to change password on first login
        $user->save();

        $this->info("College Admin user '{$username}' created successfully!");
        return 0;
    }
}
