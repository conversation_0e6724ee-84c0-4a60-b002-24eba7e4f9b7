<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\SchedulingNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;

class NotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Get notifications for the current user
     */
    public function getNotifications()
    {
        $userId = Auth::user()->id;
        $notifications = SchedulingNotification::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();
        
        $unreadCount = SchedulingNotification::where('user_id', $userId)
            ->where('is_read', 0)
            ->count();
        
        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $unreadCount
        ]);
    }
    
    /**
     * Mark a notification as read
     */
    public function markAsRead(Request $request)
    {
        $notificationId = $request->input('notification_id');
        $notification = SchedulingNotification::find($notificationId);
        
        if (!$notification) {
            return response()->json(['success' => false, 'message' => 'Notification not found']);
        }
        
        if ($notification->user_id != Auth::user()->id) {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }
        
        $notification->markAsRead();
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        $userId = Auth::user()->id;
        SchedulingNotification::where('user_id', $userId)
            ->where('is_read', 0)
            ->update(['is_read' => 1]);
        
        return response()->json(['success' => true]);
    }
}
