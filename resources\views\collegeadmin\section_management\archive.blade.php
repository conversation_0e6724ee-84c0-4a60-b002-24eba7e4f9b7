@extends('vendor.adminlte.collegeadmin_layout.app')

@section('title', 'CLASSMOS - Archived Sections')

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-archive"></i>
        Archived Sections
        <small>{{ $college->college_name }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.section.index') }}">Section Management</a></li>
        <li class="active">Archived Sections</li>
    </ol>
</section>

<section class="content">
    <div class="container-fluid">
        @if(Session::has('success'))
        <div class='col-sm-12'>
            <div class='alert alert-success alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-check"></i> {{ Session::get('success') }}
            </div>
        </div>
        @endif

        @if(Session::has('error'))
        <div class='col-sm-12'>
            <div class='alert alert-danger alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-ban"></i> {{ Session::get('error') }}
            </div>
        </div>
        @endif

        <div class="row">
            <div class="col-sm-12">
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title">Archived Sections - {{ $college->college_name }}</h3>
                        <div class="box-tools pull-right">
                            <a href="{{ route('collegeadmin.section.index') }}" class="btn btn-flat btn-default">
                                <i class="fa fa-arrow-left"></i> Back to Section Management
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        @if(count($sections) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Program Code</th>
                                        <th>Level</th>
                                        <th>Section Name</th>
                                        <th>Archived Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sections as $section)
                                    <tr>
                                        <td>{{ $section->program_code }}</td>
                                        <td>{{ $section->level }}</td>
                                        <td>{{ $section->section_name }}</td>
                                        <td>{{ $section->updated_at ? $section->updated_at->format('M d, Y h:i A') : 'N/A' }}</td>
                                        <td>
                                            <button onclick="restoreSection('{{ $section->id }}')" class="btn btn-flat btn-success btn-sm" title="Restore Section">
                                                <i class="fa fa-undo"></i> Restore
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> No archived sections found for {{ $college->college_name }}.
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
function restoreSection(section_id) {
    if (confirm('Are you sure you want to restore this section?')) {
        $.ajax({
            type: "GET",
            url: "/ajax/collegeadmin/section_management/restore_section",
            data: {
                section_id: section_id
            },
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('Error restoring section');
            }
        });
    }
}
</script>
@endsection
