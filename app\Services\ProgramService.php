<?php

namespace App\Services;

use App\College;
use App\ProgramName;

class ProgramService
{
    /**
     * Get all programs from all colleges
     *
     * @return array
     */
    public function getAllPrograms()
    {
        $colleges = College::where('is_active', 1)->get();
        $programs = [];

        foreach ($colleges as $college) {
            $collegePrograms = $college->getPrograms();
            foreach ($collegePrograms as $program) {
                $programs[] = [
                    'program_code' => $program['program_code'],
                    'program_name' => $program['program_name'],
                    'college_code' => $college->college_code,
                    'college_name' => $college->college_name
                ];
            }
        }

        // Remove duplicates by program_code
        $uniqueProgramCodes = [];
        $filteredPrograms = [];
        foreach ($programs as $program) {
            if (!in_array($program['program_code'], $uniqueProgramCodes)) {
                $uniqueProgramCodes[] = $program['program_code'];
                $filteredPrograms[] = $program;
            }
        }

        return $filteredPrograms;
    }

    /**
     * Get all programs as objects for blade templates
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllProgramsAsObjects()
    {
        $programs = $this->getAllPrograms();
        $programObjects = [];

        foreach ($programs as $program) {
            $programObjects[] = (object) [
                'program_code' => $program['program_code'],
                'program_name' => $program['program_name'],
                'college_code' => $program['college_code'],
                'college_name' => $program['college_name']
            ];
        }

        return collect($programObjects);
    }

    /**
     * Get programs for a specific college
     *
     * @param string $collegeCode
     * @return array
     */
    public function getProgramsByCollege($collegeCode)
    {
        $college = College::where('college_code', $collegeCode)->first();

        if (!$college) {
            return [];
        }

        return $college->getPrograms();
    }

    /**
     * Get a program by its code
     *
     * @param string $programCode
     * @return object|null
     */
    public function getProgramByCode($programCode)
    {
        $allPrograms = $this->getAllProgramsAsObjects();

        foreach ($allPrograms as $program) {
            if ($program->program_code === $programCode) {
                return $program;
            }
        }

        return null;
    }

    /**
     * Find college for a program
     *
     * @param string $programCode
     * @return College|null
     */
    public function findCollegeForProgram($programCode)
    {
        $colleges = College::all();

        foreach ($colleges as $college) {
            if ($college->hasProgram($programCode)) {
                return $college;
            }
        }

        return null;
    }

    /**
     * Get college code for a program
     *
     * @param string $programCode
     * @return string|null
     */
    public function getCollegeCodeForProgram($programCode)
    {
        $college = $this->findCollegeForProgram($programCode);

        if ($college) {
            return $college->college_code;
        }

        // If not found, create or get the unassigned college and add the program
        $unassignedCollege = College::firstOrCreate(
            ['college_code' => 'UNASSIGNED'],
            [
                'college_name' => 'Unassigned College',
                'description' => $programCode,
                'is_active' => 1
            ]
        );

        // Add the program to the unassigned college if it's not already there
        if (!$unassignedCollege->hasProgram($programCode)) {
            $unassignedCollege->addProgram($programCode);
        }

        return $unassignedCollege->college_code;
    }

    /**
     * Check if a program exists in any college
     *
     * @param string $programCode
     * @return bool
     */
    public function programExists($programCode)
    {
        $colleges = College::all();

        foreach ($colleges as $college) {
            if ($college->hasProgram($programCode)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get program name by code
     *
     * @param string $programCode
     * @return string
     */
    public function getProgramName($programCode)
    {
        $name = ProgramName::getNameByCode($programCode);

        if ($name) {
            return $name;
        }

        // If not found in program_names table, return the code as the name
        return $programCode;
    }

    /**
     * Add a new program to a college
     *
     * @param string $programCode
     * @param string $programName
     * @param string $collegeCode
     * @return bool
     */
    public function addProgram($programCode, $programName, $collegeCode)
    {
        $college = College::where('college_code', $collegeCode)->first();

        if (!$college) {
            return false;
        }

        $college->addProgram($programCode, $programName);
        return true;
    }
}

