<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class NormalizeCurriculumData extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 1. Create subjects table if it doesn't exist
        if (!Schema::hasTable('subjects')) {
            Schema::create('subjects', function (Blueprint $table) {
                $table->increments('id');
                $table->string('subject_code')->unique();
                $table->string('subject_name');
                $table->decimal('lec', 5, 2)->default(0);
                $table->decimal('lab', 5, 2)->default(0);
                $table->decimal('units', 5, 2)->default(0);
                $table->boolean('is_complab')->default(0);
                $table->timestamps();
            });

            // Extract unique subjects from curricula
            $subjects = DB::table('curricula')
                ->select('course_code as subject_code', 'course_name as subject_name', 'lec', 'lab', 'units', 'is_complab')
                ->distinct()
                ->get();

            foreach ($subjects as $subject) {
                // Skip if subject_code is empty
                if (empty($subject->subject_code)) {
                    continue;
                }

                // Check if subject already exists
                $existingSubject = DB::table('subjects')
                    ->where('subject_code', $subject->subject_code)
                    ->first();

                if (!$existingSubject) {
                    DB::table('subjects')->insert([
                        'subject_code' => $subject->subject_code,
                        'subject_name' => $subject->subject_name,
                        'lec' => $subject->lec,
                        'lab' => $subject->lab,
                        'units' => $subject->units,
                        'is_complab' => $subject->is_complab,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
        }

        // 2. Modify curricula table to reference subjects
        if (!Schema::hasColumn('curricula', 'subject_id')) {
            Schema::table('curricula', function (Blueprint $table) {
                $table->integer('subject_id')->unsigned()->nullable()->after('course_code');
                $table->foreign('subject_id')->references('id')->on('subjects');
            });

            // Update curricula with subject_id references
            $curricula = DB::table('curricula')->get();
            foreach ($curricula as $curriculum) {
                // Skip if course_code is empty
                if (empty($curriculum->course_code)) {
                    continue;
                }

                $subject = DB::table('subjects')
                    ->where('subject_code', $curriculum->course_code)
                    ->first();

                if ($subject) {
                    DB::table('curricula')
                        ->where('id', $curriculum->id)
                        ->update(['subject_id' => $subject->id]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove the foreign key and column
        if (Schema::hasColumn('curricula', 'subject_id')) {
            Schema::table('curricula', function (Blueprint $table) {
                $table->dropForeign(['subject_id']);
                $table->dropColumn('subject_id');
            });
        }

        // Drop the subjects table
        Schema::dropIfExists('subjects');
    }
}