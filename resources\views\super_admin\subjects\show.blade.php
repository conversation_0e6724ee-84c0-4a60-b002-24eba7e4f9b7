<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('header')
<!-- Content Header (Page header) -->
<section class="content-header">
    <h1>
        Subject Details
        <small>View subject information</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li><a href="{{ route('superadmin.subjects.index') }}"> Subjects</a></li>
        <li class="active">View Subject</li>
    </ol>
</section>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">Subject Information</h3>
                <div class="box-tools pull-right">
                    <a href="{{ route('superadmin.subjects.edit', $subject->id) }}" class="btn btn-primary btn-sm">
                        <i class="fa fa-pencil"></i> Edit Subject
                    </a>
                </div>
            </div>
            <div class="box-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Subject Code:</label>
                            <p class="form-control-static">{{ $subject->subject_code }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Subject Name:</label>
                            <p class="form-control-static">{{ $subject->subject_name }}</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Lecture Units:</label>
                            <p class="form-control-static">{{ $subject->lec }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Laboratory Units:</label>
                            <p class="form-control-static">{{ $subject->lab }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Total Units:</label>
                            <p class="form-control-static">{{ $subject->units }}</p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Computer Laboratory Subject:</label>
                    <p class="form-control-static">{{ $subject->is_complab ? 'Yes' : 'No' }}</p>
                </div>

                <div class="form-group">
                    <label>Created At:</label>
                    <p class="form-control-static">{{ $subject->created_at->format('F d, Y h:i A') }}</p>
                </div>

                <div class="form-group">
                    <label>Last Updated:</label>
                    <p class="form-control-static">{{ $subject->updated_at->format('F d, Y h:i A') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">Curriculum Entries Using This Subject</h3>
            </div>
            <div class="box-body">
                @if(count($curricula) > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="curricula-table">
                        <thead>
                            <tr>
                                <th>Program</th>
                                <th>Curriculum Year</th>
                                <th>Level</th>
                                <th>Period</th>
                                <th>College</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($curricula as $curriculum)
                            <tr>
                                <td>{{ $curriculum->program_code }} - {{ $curriculum->program_name }}</td>
                                <td>{{ $curriculum->curriculum_year }}</td>
                                <td>{{ $curriculum->level }}</td>
                                <td>{{ $curriculum->period }}</td>
                                <td>{{ $curriculum->college_code }}</td>
                                <td>
                                    <a href="{{ url('/superadmin/curriculum_management/list_curriculum', [$curriculum->program_code, $curriculum->curriculum_year]) }}" class="btn btn-info btn-sm" title="View Curriculum">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> This subject is not used in any curriculum yet.
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('#curricula-table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false
        });
    });
</script>
@endsection
