<?php

// Course Offering Curriculum Management Routes
Route::get('/admin/course_offerings/curriculum', 'Admin\CourseOfferingCurriculumController@index');
Route::get('/admin/course_offerings/curriculum/college/{college_code}', 'Admin\CourseOfferingCurriculumController@viewCollegeOfferings');
Route::get('/admin/course_offerings/curriculum/program/{program_code}', 'Admin\CourseOfferingCurriculumController@viewProgramOfferings');
Route::get('/admin/course_offerings/curriculum/program/curriculum/{program_code}/{curriculum_year}', 'Admin\CourseOfferingCurriculumController@viewCurriculumDetails');
Route::get('/admin/course_offerings/curriculum/section/{section_id}', 'Admin\CourseOfferingCurriculumController@viewSectionOfferings');
Route::get('/admin/course_offerings/curriculum/section/add_all/{section_id}', 'Admin\CourseOfferingCurriculumController@addAllCurricula');
Route::post('/admin/course_offerings/curriculum/section/add_selected/{section_id}', 'Admin\CourseOfferingCurriculumController@addSelectedCurricula');
Route::get('/admin/course_offerings/curriculum/section/remove/{section_id}/{offering_id}', 'Admin\CourseOfferingCurriculumController@removeCurriculum');

// AJAX Routes
Route::get('/ajax/admin/course_offerings/curriculum/programs_by_college', 'Admin\Ajax\CourseOfferingCurriculumAjax@getProgramsByCollege');
Route::get('/ajax/admin/course_offerings/curriculum/all_programs', 'Admin\Ajax\CourseOfferingCurriculumAjax@getAllPrograms');
Route::get('/ajax/admin/course_offerings/curriculum/sections_by_program', 'Admin\Ajax\CourseOfferingCurriculumAjax@getSectionsByProgram');
Route::get('/ajax/admin/course_offerings/curriculum/curriculum_years_by_program', 'Admin\Ajax\CourseOfferingCurriculumAjax@getCurriculumYearsByProgram');
Route::get('/ajax/admin/course_offerings/curriculum/curriculum_details', 'Admin\Ajax\CourseOfferingCurriculumAjax@getCurriculumDetails');
Route::get('/ajax/admin/course_offerings/curriculum/section_offerings', 'Admin\Ajax\CourseOfferingCurriculumAjax@getSectionOfferings');
Route::get('/ajax/admin/course_offerings/curriculum/available_curricula', 'Admin\Ajax\CourseOfferingCurriculumAjax@getAvailableCurricula')->name('ajax.course_offerings.available_curricula');
Route::get('/ajax/admin/course_offerings/curriculum/available_subjects', 'Admin\Ajax\CourseOfferingCurriculumAjax@getAvailableSubjects')->name('ajax.course_offerings.available_subjects');

// SuperAdmin Course Offering Curriculum Management Routes
Route::get('/superadmin/course_offerings/curriculum', 'SuperAdmin\CourseOfferingCurriculumController@index');
Route::get('/superadmin/course_offerings/curriculum/college/{college_code}', 'SuperAdmin\CourseOfferingCurriculumController@viewCollegeOfferings');
Route::get('/superadmin/course_offerings/curriculum/program/{program_code}', 'SuperAdmin\CourseOfferingCurriculumController@viewProgramOfferings');
Route::get('/superadmin/course_offerings/curriculum/program/curriculum/{program_code}/{curriculum_year}', 'SuperAdmin\CourseOfferingCurriculumController@viewCurriculumDetails');
Route::get('/superadmin/course_offerings/curriculum/section/{section_id}', 'SuperAdmin\CourseOfferingCurriculumController@viewSectionOfferings');
Route::get('/superadmin/course_offerings/curriculum/section/add_all/{section_id}', 'SuperAdmin\CourseOfferingCurriculumController@addAllCurricula');
Route::post('/superadmin/course_offerings/curriculum/section/add_selected/{section_id}', 'SuperAdmin\CourseOfferingCurriculumController@addSelectedCurricula');
Route::get('/superadmin/course_offerings/curriculum/section/remove/{section_id}/{offering_id}', 'SuperAdmin\CourseOfferingCurriculumController@removeCurriculum');

// SuperAdmin AJAX Routes
Route::get('/ajax/superadmin/course_offerings/curriculum/programs_by_college', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getProgramsByCollege');
Route::get('/ajax/superadmin/course_offerings/curriculum/all_programs', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getAllPrograms');
Route::get('/ajax/superadmin/course_offerings/curriculum/sections_by_program', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getSectionsByProgram');
Route::get('/ajax/superadmin/course_offerings/curriculum/curriculum_years_by_program', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getCurriculumYearsByProgram');
Route::get('/ajax/superadmin/course_offerings/curriculum/curriculum_details', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getCurriculumDetails');
Route::get('/ajax/superadmin/course_offerings/curriculum/section_offerings', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getSectionOfferings');
Route::get('/ajax/superadmin/course_offerings/curriculum/available_curricula', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getAvailableCurricula')->name('ajax.superadmin.course_offerings.available_curricula');
Route::get('/ajax/superadmin/course_offerings/curriculum/available_subjects', 'SuperAdmin\Ajax\CourseOfferingCurriculumAjax@getAvailableSubjects')->name('ajax.superadmin.course_offerings.available_subjects');
