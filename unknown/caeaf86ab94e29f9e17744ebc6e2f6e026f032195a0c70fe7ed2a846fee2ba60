<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <h3 class="modal-title">
                <i class="fa fa-plus-circle"></i> Add Schedule
                @if(isset($multiple_days))
                    <small>(Multiple Days: {{ implode(', ', explode(',', $multiple_days)) }})</small>
                @else
                    <small>({{ $day ?? 'Single Day' }})</small>
                @endif
            </h3>
        </div>
        <form action="{{ url('/collegeadmin/course_scheduling/add_schedule') }}" method="POST">
            {{ csrf_field() }}
            <input type="hidden" name="offering_id" value="{{ $offering_id }}">
            <input type="hidden" name="section_name" value="{{ $section_name }}">
            <input type="hidden" name="day_type" value="{{ $day_type }}">
            <input type="hidden" name="time_start" value="{{ $time_start }}">
            <input type="hidden" name="time_end" value="{{ $time_end }}">
            
            @if(isset($multiple_days))
                <input type="hidden" name="multiple_days" value="{{ $multiple_days }}">
            @else
                <input type="hidden" name="day" value="{{ $day }}">
            @endif

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="room">Room <span class="text-danger">*</span></label>
                            <select class="form-control select2" name="room" id="room" required>
                                <option value="">Select Room</option>
                                @foreach($available_rooms as $room)
                                    <option value="{{ $room->room }}">
                                        {{ $room->room }} 
                                        @if($room->room_type)
                                            ({{ $room->room_type }})
                                        @endif
                                        @if($room->capacity)
                                            - Capacity: {{ $room->capacity }}
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            @if($available_rooms->isEmpty())
                                <small class="text-danger">
                                    <i class="fa fa-warning"></i> No rooms available for the selected time slot.
                                </small>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="instructor_id">Instructor</label>
                            <select class="form-control select2" name="instructor_id" id="instructor_id">
                                <option value="">Select Instructor (Optional)</option>
                                @foreach($instructors as $instructor)
                                    <option value="{{ $instructor->id }}">
                                        {{ $instructor->firstname }} {{ $instructor->lastname }}
                                        @if($instructor->employee_id)
                                            ({{ $instructor->employee_id }})
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h4><i class="fa fa-info-circle"></i> Schedule Details</h4>
                            <p><strong>Time:</strong> {{ $time_start }} - {{ $time_end }}</p>
                            <p><strong>Day Type:</strong> {{ $day_type }}</p>
                            @if(isset($multiple_days))
                                <p><strong>Days:</strong> {{ implode(', ', explode(',', $multiple_days)) }}</p>
                            @else
                                <p><strong>Day:</strong> {{ $day }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                @if($available_rooms->isEmpty())
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-warning">
                                <h4><i class="fa fa-warning"></i> No Available Rooms</h4>
                                <p>All rooms are occupied during the selected time slot. You may:</p>
                                <ul>
                                    <li>Choose a different time slot</li>
                                    <li>Check for conflicts and resolve them</li>
                                    <li>Contact the administrator for assistance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Cancel
                </button>
                @if(!$available_rooms->isEmpty())
                    <button type="submit" class="btn btn-success">
                        <i class="fa fa-plus-circle"></i> Add Schedule
                    </button>
                @endif
            </div>
        </form>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize select2 for the modal dropdowns
    $('.select2').select2({
        width: '100%',
        dropdownParent: $('#myModal')
    });

    // Add real-time conflict checking when room or instructor changes
    $('#room, #instructor_id').on('change', function() {
        checkRealTimeConflicts();
    });
});

function checkRealTimeConflicts() {
    var room = $('#room').val();
    var instructorId = $('#instructor_id').val();
    var timeStart = $('input[name="time_start"]').val();
    var timeEnd = $('input[name="time_end"]').val();
    
    @if(isset($multiple_days))
        var days = '{{ $multiple_days }}'.split(',');
    @else
        var days = ['{{ $day }}'];
    @endif

    if (!room && !instructorId) {
        return; // Nothing to check
    }

    // Check conflicts for each day
    var conflictPromises = [];
    
    days.forEach(function(day) {
        var promise = $.ajax({
            type: "GET",
            url: "/ajax/collegeadmin/course_scheduling/check_conflicts",
            data: {
                day: day,
                time_start: timeStart,
                time_end: timeEnd,
                room: room,
                instructor_id: instructorId
            }
        });
        conflictPromises.push(promise);
    });

    // Wait for all conflict checks
    $.when.apply($, conflictPromises).done(function() {
        var hasConflicts = false;
        var conflictMessages = [];

        // Process responses
        for (var i = 0; i < arguments.length; i++) {
            var response = conflictPromises.length === 1 ? arguments[0] : arguments[i][0];
            
            if (response.has_conflicts) {
                hasConflicts = true;
                response.conflicts.forEach(function(conflict) {
                    conflictMessages.push(conflict.message);
                });
            }
        }

        // Remove existing conflict alerts
        $('.conflict-alert').remove();

        if (hasConflicts) {
            // Show conflict warning
            var conflictAlert = $('<div class="alert alert-warning conflict-alert">' +
                '<h4><i class="fa fa-warning"></i> Conflicts Detected!</h4>' +
                '<ul>' + conflictMessages.map(function(msg) { return '<li>' + msg + '</li>'; }).join('') + '</ul>' +
                '<p><small>You can still proceed with scheduling, but conflicts may need to be resolved.</small></p>' +
                '</div>');
            
            conflictAlert.insertBefore('.modal-footer');
        }
    });
}
</script>
