<div class="table-responsive">
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>Program</th>
                <th>Level</th>
                <th>Section Name</th>
                <th>Subjects Count</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @if(count($sections) > 0)
            @foreach($sections as $section)
            <tr>
                <td>{{ $section->program_code }}</td>
                <td>{{ $section->level }}</td>
                <td>{{ $section->section_name }}</td>
                <td>
                    @php
                    $offeringsCount = \App\offerings_infos_table::where('section_name', $section->section_name)->count();
                    @endphp
                    {{ $offeringsCount }}
                </td>
                <td>
                    <a href="{{ route('collegeadmin.section.view', $section->id) }}" class="btn btn-flat btn-info btn-sm" title="View Section Details">
                        <i class="fa fa-eye"></i>
                    </a>
                    <a href="{{ route('collegeadmin.section.add_all', $section->id) }}" class="btn btn-flat btn-success btn-sm" title="Add All Available Curriculum Subjects" onclick="return confirm('Add all available curriculum subjects to this section?')">
                        <i class="fa fa-plus-circle"></i>
                    </a>
                    <button data-toggle="modal" data-target="#editModal" onclick="editSection('{{ $section->id }}')" title="Edit Section" class="btn btn-flat btn-primary btn-sm">
                        <i class="fa fa-pencil"></i>
                    </button>
                    <a href="{{ route('collegeadmin.section.archive_section', $section->id) }}" class="btn btn-flat btn-danger btn-sm" title="Archive Section" onclick="return confirm('Do you wish to archive this section?')">
                        <i class="fa fa-archive"></i>
                    </a>
                </td>
            </tr>
            @endforeach
            @else
            <tr>
                <td colspan="5" class="text-center">No sections found matching your search criteria</td>
            </tr>
            @endif
        </tbody>
    </table>
</div>
