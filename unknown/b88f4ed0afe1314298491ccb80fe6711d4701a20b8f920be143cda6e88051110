<?php
$layout = '';

if (Auth::user()->is_first_login == 1) {
    $layout = 'layouts.first_login';
} else {
    if (Auth::user()->accesslevel == 100) {
        $layout = 'layouts.superadmin';
    } elseif (Auth::user()->accesslevel == 50) {
        $layout = 'layouts.collegeadmin';
    } elseif (Auth::user()->accesslevel == 1) {
        $layout = 'layouts.instructor';
    } elseif (Auth::user()->accesslevel == 0) {
        $layout = 'layouts.admin';
    }
}

// Helper functions
function getDayName($day) {
    $days = [
        'M' => 'Monday',
        'T' => 'Tuesday',
        'W' => 'Wednesday',
        'Th' => 'Thursday',
        'F' => 'Friday',
        'Sa' => 'Saturday',
        'Su' => 'Sunday'
    ];
    return $days[$day] ?? $day;
}

function getColorForDay($day) {
    $colors = [
        'M' => '#3c8dbc',
        'T' => '#00a65a',
        'W' => '#f39c12',
        'Th' => '#dd4b39',
        'F' => '#605ca8',
        'Sa' => '#d2d6de',
        'Su' => '#001f3f'
    ];
    return $colors[$day] ?? '#3c8dbc';
}

?>
@extends($layout)

@section('main-content')
<link rel="stylesheet" href="{{ asset('plugins/fullcalendar/fullcalendar.css') }}">

<section class="content-header">
    <div class="row">
        <div class="col-md-6">
            <h1><i class="fa fa-table"></i>
                Schedule for Section: {{ $sectionName }}
                <small>Tabular View</small>
            </h1>
        </div>
        <div class="col-md-6 text-right" style="padding-top: 10px;">
            <a href="{{ route('collegeadmin.course_scheduling.index') }}" class="btn btn-default btn-flat">
                <i class="fa fa-arrow-left"></i> Back to Course Scheduling
            </a>
        </div>
    </div>
    <ol class="breadcrumb">
        <li><a href="{{ url('/') }}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.course_scheduling.index') }}">Course Scheduling</a></li>
        <li class="active">Schedule - {{ $sectionName }}</li>
    </ol>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    <div class="row">
        <!-- Tabular View -->
        <div class="col-md-6">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-table"></i> Schedule Table</h3>
                </div>
                <div class="box-body">
                    @if($schedules->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Day</th>
                                        <th>Time</th>
                                        <th>Course</th>
                                        <th>Room</th>
                                        <th>Instructor</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($schedules as $schedule)
                                        <tr>
                                            <td>
                                                <span class="label" style="background-color: {{ getColorForDay($schedule->day) }}; color: white;">
                                                    {{ getDayName($schedule->day) }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    {{ date('g:i A', strtotime($schedule->time_starts)) }} - 
                                                    {{ date('g:i A', strtotime($schedule->time_end)) }}
                                                </small>
                                            </td>
                                            <td>
                                                <strong>{{ $schedule->course_code }}</strong><br>
                                                <small class="text-muted">{{ $schedule->subject_name }}</small>
                                            </td>
                                            <td>
                                                <span class="label label-info">{{ $schedule->room }}</span>
                                            </td>
                                            <td>
                                                @if($schedule->instructor_firstname && $schedule->instructor_lastname)
                                                    <small>
                                                        {{ $schedule->instructor_firstname }} {{ $schedule->instructor_lastname }}
                                                    </small>
                                                @else
                                                    <span class="text-muted"><em>Not Assigned</em></span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <h4><i class="fa fa-info-circle"></i> No Schedules Found</h4>
                            <p>No schedules have been created for section <strong>{{ $sectionName }}</strong> yet.</p>
                            <p>
                                <a href="{{ route('collegeadmin.course_scheduling.index') }}" class="btn btn-primary btn-sm">
                                    <i class="fa fa-plus"></i> Create Schedules
                                </a>
                            </p>
                        </div>
                    @endif
                </div>
                @if($schedules->count() > 0)
                    <div class="box-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="text-muted">
                                    <i class="fa fa-info-circle"></i> 
                                    Total Schedules: {{ $schedules->count() }}
                                </p>
                            </div>
                            <div class="col-md-6 text-right">
                                <button type="button" class="btn btn-success btn-sm" onclick="window.print()">
                                    <i class="fa fa-print"></i> Print Schedule
                                </button>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Calendar View -->
        <div class="col-md-6">
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-calendar"></i> Calendar View</h3>
                </div>
                <div class="box-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    @if($schedules->count() > 0)
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-bar-chart"></i> Schedule Summary</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-blue"><i class="fa fa-book"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Courses</span>
                                        <span class="info-box-number">{{ $schedules->groupBy('course_code')->count() }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-green"><i class="fa fa-home"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Rooms Used</span>
                                        <span class="info-box-number">{{ $schedules->groupBy('room')->count() }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-yellow"><i class="fa fa-user"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Instructors</span>
                                        <span class="info-box-number">{{ $schedules->whereNotNull('instructor_firstname')->groupBy('instructor_firstname')->count() }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-red"><i class="fa fa-clock-o"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Time Slots</span>
                                        <span class="info-box-number">{{ $schedules->count() }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection

@section('header-style')
<style>
    @media print {
        .box-header, .box-footer, .btn, .breadcrumb, .content-header {
            display: none !important;
        }
        
        .box {
            border: none !important;
            box-shadow: none !important;
        }
        
        .col-md-6:last-child {
            display: none !important;
        }
        
        .col-md-6:first-child {
            width: 100% !important;
        }
    }
    
    .table th {
        background-color: #f4f4f4;
    }
    
    .info-box {
        margin-bottom: 15px;
    }
</style>
@endsection

@section('footer-script')
<script src="{{ asset('plugins/fullcalendar/fullcalendar.js') }}"></script>
<script>
    $(document).ready(function() {
        // Initialize calendar
        $('#calendar').fullCalendar({
            firstDay: 1,
            columnFormat: 'ddd',
            defaultView: 'agendaWeek',
            hiddenDays: [0],
            minTime: '07:00:00',
            maxTime: '22:00:00',
            header: false,
            allDaySlot: false,
            eventSources: [{
                events: [
                    @foreach($schedules as $schedule)
                    {
                        id: {{ $schedule->id }},
                        title: '{{ $schedule->course_code }} - {{ $schedule->room }} - {{ $schedule->instructor_lastname ?? "Not Assigned" }}',
                        start: '{{ date("Y-m-d", strtotime("this week " . getDayName($schedule->day))) }}T{{ $schedule->time_starts }}',
                        end: '{{ date("Y-m-d", strtotime("this week " . getDayName($schedule->day))) }}T{{ $schedule->time_end }}',
                        color: '{{ getColorForDay($schedule->day) }}',
                        textColor: 'white'
                    },
                    @endforeach
                ]
            }],
            eventRender: function(event, element) {
                element.find('.fc-title').html(element.find('.fc-title').text());
                
                // Add tooltip
                element.attr('title', 
                    'Course: ' + event.title + '\n' +
                    'Time: ' + moment(event.start).format('h:mm A') + ' - ' + moment(event.end).format('h:mm A')
                );
            }
        });
    });
</script>
@endsection
