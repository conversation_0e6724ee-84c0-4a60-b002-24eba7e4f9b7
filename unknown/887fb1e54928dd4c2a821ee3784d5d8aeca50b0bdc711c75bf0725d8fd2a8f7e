<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Support\Facades\Session;
use DB;
use App\TimeBlock;
use App\College;
use App\curriculum;
use App\CtrRoom;
use App\CtrSection;
use App\offerings_infos_table;
use App\room_schedules;
use App\User;
use App\SchedulePriority;

class CourseScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get only the college admin's college
        $colleges = collect([$college]);

        // Check if there are any sections for this college
        $sectionCount = CtrSection::where('college_code', $collegeCode)->count();

        // Create sample sections if none exist
        if ($sectionCount == 0) {
            $this->createSampleSections($colleges);
        }

        // Get sections for this college only
        $sections = CtrSection::where('college_code', $collegeCode)
            ->distinct()
            ->get(['section_name', 'level', 'college_code', 'program_code']);

        // Get levels for this college only
        $levels = CtrSection::where('college_code', $collegeCode)
            ->distinct()
            ->pluck('level')
            ->toArray();

        // Get program codes and names for this college only
        $programs = collect($college->getPrograms())->map(function ($program) {
            return (object) $program;
        });

        return view('collegeadmin.course_schedule.course_schedule', compact('colleges', 'sections', 'levels', 'programs', 'college'));
    }

    private function createSampleSections($colleges)
    {
        // Create sample sections for the college admin's college only
        $collegeCode = Auth::user()->college_code;

        $levels = ['1st Year', '2nd Year', '3rd Year', '4th Year'];
        $college = College::where('college_code', $collegeCode)->first();
        $programs = collect($college->getPrograms())->map(function ($program) {
            return (object) $program;
        });

        foreach ($levels as $level) {
            foreach ($programs as $program) {
                for ($i = 1; $i <= 2; $i++) {
                    $section = new CtrSection();
                    $section->section_name = $program->program_code . '-' . substr($level, 0, 1) . $i;
                    $section->level = $level;
                    $section->college_code = $collegeCode;
                    $section->program_code = $program->program_code;
                    $section->is_active = 1;
                    $section->save();
                }
            }
        }
    }

    function add_schedule($offering_id, $section_name)
    {
        $offering = offerings_infos_table::find($offering_id);
        $curricula = curriculum::find($offering->curriculum_id);

        // Check if this offering belongs to the college admin's college
        $collegeCode = Auth::user()->college_code;
        if ($curricula->college_code !== $collegeCode) {
            Session::flash('error', 'You do not have permission to schedule this course');
            return redirect()->back();
        }

        $inactive = room_schedules::where('is_active', 0)
            ->where('college_code', $collegeCode)
            ->get();
        $get_schedule = $this->getSchedule($offering_id);
        $is_complab = curriculum::find($offering->curriculum_id)->is_complab;

        return view('collegeadmin.course_schedule.add_schedule', compact('offering', 'curricula', 'inactive', 'offering_id', 'get_schedule', 'section_name', 'is_complab'));
    }

    private function getSchedule($offering_id)
    {
        $collegeCode = Auth::user()->college_code;

        $schedules = room_schedules::where('offering_id', $offering_id)
            ->where('is_active', 1)
            ->where('college_code', $collegeCode)
            ->get();

        $events = [];
        foreach ($schedules as $schedule) {
            $offering = offerings_infos_table::find($schedule->offering_id);
            $curriculum = curriculum::find($offering->curriculum_id);

            $events[] = [
                'id' => $schedule->id,
                'title' => $curriculum->subject_code . ' - ' . $schedule->room,
                'start' => $this->getDateForDay($schedule->day) . 'T' . $schedule->time_starts,
                'end' => $this->getDateForDay($schedule->day) . 'T' . $schedule->time_end,
                'color' => $this->getColorForDay($schedule->day),
                'offering_id' => $schedule->offering_id
            ];
        }

        return json_encode($events);
    }

    private function getDateForDay($day)
    {
        $days = [
            'M' => 'monday',
            'T' => 'tuesday',
            'W' => 'wednesday',
            'Th' => 'thursday',
            'F' => 'friday',
            'Sa' => 'saturday',
            'Su' => 'sunday'
        ];

        return date('Y-m-d', strtotime('this ' . $days[$day]));
    }

    private function getColorForDay($day)
    {
        $colors = [
            'M' => '#3c8dbc',
            'T' => '#00a65a',
            'W' => '#f39c12',
            'Th' => '#dd4b39',
            'F' => '#605ca8',
            'Sa' => '#d2d6de',
            'Su' => '#001f3f'
        ];

        return $colors[$day] ?? '#3c8dbc';
    }

    public function add_schedule_post(Request $request)
    {
        $collegeCode = Auth::user()->college_code;

        $offering_id = $request->offering_id;
        $section_name = $request->section_name;
        $time_start = $request->time_start;
        $time_end = $request->time_end;
        $day_type = $request->day_type;
        $instructor_id = $request->instructor_id;

        // Get the curriculum and verify college access
        $offering = offerings_infos_table::find($offering_id);
        $curriculum = curriculum::find($offering->curriculum_id);

        if ($curriculum->college_code !== $collegeCode) {
            Session::flash('error', 'You do not have permission to schedule this course');
            return redirect()->back();
        }

        // Get priority level if any
        $priority = SchedulePriority::getPriorityLevel($curriculum->id);

        // Check if we have multiple days
        if ($request->has('multiple_days')) {
            $days = explode(',', $request->multiple_days);
        } else {
            $days = [$request->day]; // Single day
        }

        // Validate time format and ensure end time is after start time
        if ($time_start >= $time_end) {
            Session::flash('error', 'End time must be after start time');
            return redirect()->back();
        }

        // Check for conflicts for each day
        foreach ($days as $day) {
            $conflicts = $this->checkConflicts($day, $time_start, $time_end, $request->room, $instructor_id, $collegeCode);

            if (!empty($conflicts)) {
                $conflictMessages = [];
                foreach ($conflicts as $conflict) {
                    $conflictMessages[] = $conflict['type'] . ': ' . $conflict['details'];
                }
                Session::flash('error', 'Schedule conflicts detected for ' . $day . ': ' . implode(', ', $conflictMessages));
                return redirect()->back();
            }
        }

        // Create schedules for each day
        foreach ($days as $day) {
            $schedule = new room_schedules;
            $schedule->day = $day;
            $schedule->day_type = $day_type;
            $schedule->time_starts = $time_start;
            $schedule->time_end = $time_end;
            $schedule->room = $request->room;
            $schedule->offering_id = $offering_id;
            $schedule->instructor = $instructor_id;
            $schedule->college_code = $collegeCode;
            $schedule->priority_level = $priority;
            $schedule->is_active = 1;
            $schedule->save();
        }

        Session::flash('success', 'Schedule successfully created!');
        return redirect()->route('collegeadmin.course_scheduling.add_schedule', [$offering_id, $section_name]);
    }

    private function checkConflicts($day, $time_start, $time_end, $room, $instructor_id, $collegeCode)
    {
        $conflicts = [];

        // Check room conflicts
        $roomConflicts = room_schedules::where('day', $day)
            ->where('room', $room)
            ->where('is_active', 1)
            ->where('college_code', $collegeCode)
            ->where(function ($query) use ($time_start, $time_end) {
                $query->where(function ($q) use ($time_start, $time_end) {
                    $q->where('time_starts', '<', $time_end)
                        ->where('time_end', '>', $time_start);
                });
            })
            ->count();

        if ($roomConflicts > 0) {
            $conflicts[] = [
                'type' => 'Room Conflict',
                'details' => "Room {$room} is already occupied during this time"
            ];
        }

        // Check instructor conflicts
        if ($instructor_id) {
            $instructorConflicts = room_schedules::where('day', $day)
                ->where('instructor', $instructor_id)
                ->where('is_active', 1)
                ->where(function ($query) use ($time_start, $time_end) {
                    $query->where(function ($q) use ($time_start, $time_end) {
                        $q->where('time_starts', '<', $time_end)
                            ->where('time_end', '>', $time_start);
                    });
                })
                ->count();

            if ($instructorConflicts > 0) {
                $instructor = User::find($instructor_id);
                $conflicts[] = [
                    'type' => 'Instructor Conflict',
                    'details' => "Instructor {$instructor->firstname} {$instructor->lastname} is already scheduled during this time"
                ];
            }
        }

        return $conflicts;
    }

    public function remove_schedule($schedule_id)
    {
        $collegeCode = Auth::user()->college_code;

        $schedule = room_schedules::where('id', $schedule_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$schedule) {
            Session::flash('error', 'Schedule not found or you do not have permission to remove it');
            return redirect()->back();
        }

        $offering = offerings_infos_table::find($schedule->offering_id);
        $schedule->delete();

        Session::flash('success', 'Schedule successfully removed!');
        return redirect()->route('collegeadmin.course_scheduling.add_schedule', [$schedule->offering_id, $offering->section_name]);
    }

    public function attach_schedule($schedule_id, $offering_id)
    {
        $collegeCode = Auth::user()->college_code;

        $offering = offerings_infos_table::find($offering_id);
        $curriculum = curriculum::find($offering->curriculum_id);

        if ($curriculum->college_code !== $collegeCode) {
            Session::flash('error', 'You do not have permission to attach this schedule');
            return redirect()->back();
        }

        $schedule = room_schedules::where('id', $schedule_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$schedule) {
            Session::flash('error', 'Schedule not found or you do not have permission to attach it');
            return redirect()->back();
        }

        $schedule->is_active = 1;
        $schedule->offering_id = $offering_id;
        $schedule->update();

        Session::flash('success', 'Successfully attached the schedule!');
        return redirect()->route('collegeadmin.course_scheduling.add_schedule', [$offering_id, $offering->section_name]);
    }

    public function generateSchedule(Request $request)
    {
        $collegeCode = Auth::user()->college_code;
        $sectionName = $request->section_name;

        // Get all unscheduled offerings for this section and college
        $offerings = offerings_infos_table::where('section_name', $sectionName)
            ->whereHas('curriculum', function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode);
            })
            ->whereDoesntHave('schedules', function ($query) {
                $query->where('is_active', 1);
            })
            ->get();

        if ($offerings->isEmpty()) {
            Session::flash('info', 'No unscheduled courses found for this section');
            return redirect()->back();
        }

        // Get available time blocks
        $mwfBlocks = TimeBlock::where('day_type', 'MWF')->orderBy('start_time')->get();
        $tthBlocks = TimeBlock::where('day_type', 'TTh')->orderBy('start_time')->get();

        // Get available rooms for this college
        $rooms = CtrRoom::where('college_code', $collegeCode)->get();

        // Get available instructors for this college
        $instructors = User::where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->get();

        $scheduledCount = 0;
        $failedCount = 0;

        // Sort offerings by priority
        $sortedOfferings = [];
        foreach ($offerings as $offering) {
            $curriculum = curriculum::find($offering->curriculum_id);
            $priority = SchedulePriority::getPriorityLevel($curriculum->id);

            $sortedOfferings[] = [
                'offering' => $offering,
                'curriculum' => $curriculum,
                'priority' => $priority
            ];
        }

        // Sort by priority (higher priority first)
        usort($sortedOfferings, function ($a, $b) {
            return $b['priority'] - $a['priority'];
        });

        // Schedule each offering
        foreach ($sortedOfferings as $offeringData) {
            $offering = $offeringData['offering'];
            $curriculum = $offeringData['curriculum'];

            // Determine day type based on curriculum
            $dayType = 'MWF'; // Default
            $timeBlocks = $mwfBlocks;

            if ($curriculum->is_complab) {
                $dayType = 'TTh';
                $timeBlocks = $tthBlocks;
            }

            // Try to schedule
            $scheduled = false;

            foreach ($timeBlocks as $timeBlock) {
                if ($dayType == 'MWF') {
                    $days = ['M', 'W', 'F'];
                } elseif ($dayType == 'TTh') {
                    $days = ['T', 'Th'];
                } else {
                    $days = ['Sa'];
                }

                foreach ($rooms as $room) {
                    foreach ($instructors as $instructor) {
                        // Check if this instructor is available for all days in this time block
                        $availableForAllDays = true;

                        foreach ($days as $day) {
                            $conflicts = $this->checkConflicts($day, $timeBlock->start_time, $timeBlock->end_time, $room->room, $instructor->id, $collegeCode);
                            if (!empty($conflicts)) {
                                $availableForAllDays = false;
                                break;
                            }
                        }

                        if ($availableForAllDays) {
                            // Create schedule for all days
                            foreach ($days as $day) {
                                $schedule = new room_schedules;
                                $schedule->day = $day;
                                $schedule->day_type = $dayType;
                                $schedule->time_starts = $timeBlock->start_time;
                                $schedule->time_end = $timeBlock->end_time;
                                $schedule->room = $room->room;
                                $schedule->offering_id = $offering->id;
                                $schedule->instructor = $instructor->id;
                                $schedule->college_code = $collegeCode;
                                $schedule->priority_level = $offeringData['priority'];
                                $schedule->is_active = 1;
                                $schedule->save();
                            }

                            $scheduled = true;
                            $scheduledCount++;
                            break 3; // Break out of all loops
                        }
                    }
                }
            }

            if (!$scheduled) {
                $failedCount++;
            }
        }

        $message = "Schedule generation completed. Scheduled: {$scheduledCount}, Failed: {$failedCount}";

        if ($scheduledCount > 0) {
            Session::flash('success', $message);
        } else {
            Session::flash('warning', $message);
        }

        return redirect()->back();
    }

    public function viewTabularSchedule($sectionName)
    {
        $collegeCode = Auth::user()->college_code;

        try {
            // Check if the curricula table has the course_code column
            $hasColumnCourseCode = \Schema::hasColumn('curricula', 'course_code');
            $hasColumnControlCode = \Schema::hasColumn('curricula', 'control_code');

            // Build the query based on available columns and filter by college
            $query = room_schedules::join('offerings_infos', 'offerings_infos.id', '=', 'room_schedules.offering_id')
                ->join('curricula', 'curricula.id', '=', 'offerings_infos.curriculum_id')
                ->leftJoin('users', 'users.id', '=', 'room_schedules.instructor')
                ->where('offerings_infos.section_name', $sectionName)
                ->where('room_schedules.is_active', 1)
                ->where('room_schedules.college_code', $collegeCode);

            // Select appropriate columns based on what's available
            if ($hasColumnCourseCode) {
                $query->select([
                    'room_schedules.*',
                    'curricula.course_code',
                    'curricula.subject_name',
                    'users.firstname as instructor_firstname',
                    'users.lastname as instructor_lastname'
                ]);
            } elseif ($hasColumnControlCode) {
                $query->select([
                    'room_schedules.*',
                    'curricula.control_code as course_code',
                    'curricula.subject_name',
                    'users.firstname as instructor_firstname',
                    'users.lastname as instructor_lastname'
                ]);
            } else {
                $query->select([
                    'room_schedules.*',
                    'curricula.subject_code as course_code',
                    'curricula.subject_name',
                    'users.firstname as instructor_firstname',
                    'users.lastname as instructor_lastname'
                ]);
            }

            $schedules = $query->orderBy('room_schedules.day')
                ->orderBy('room_schedules.time_starts')
                ->get();

            return view('collegeadmin.course_schedule.tabular_schedule', compact('schedules', 'sectionName'));

        } catch (\Exception $e) {
            \Log::error('Error in viewTabularSchedule: ' . $e->getMessage());
            Session::flash('error', 'Error loading schedule: ' . $e->getMessage());
            return redirect()->back();
        }
    }

    public function removeBatchSchedules(Request $request)
    {
        $collegeCode = Auth::user()->college_code;
        $dayType = $request->day_type;
        $sectionName = $request->section_name;

        if (!in_array($dayType, ['MWF', 'TTh', 'Saturday', 'Sunday'])) {
            return response()->json(['success' => false, 'message' => 'Invalid day type']);
        }

        try {
            $query = room_schedules::join('offerings_infos', 'offerings_infos.id', '=', 'room_schedules.offering_id')
                ->where('offerings_infos.section_name', $sectionName)
                ->where('room_schedules.is_active', 1)
                ->where('room_schedules.college_code', $collegeCode);

            if ($dayType === 'MWF') {
                $query->whereIn('room_schedules.day', ['M', 'W', 'F']);
            } elseif ($dayType === 'TTh') {
                $query->whereIn('room_schedules.day', ['T', 'Th']);
            } elseif ($dayType === 'Saturday') {
                $query->where('room_schedules.day', 'Sa');
            } elseif ($dayType === 'Sunday') {
                $query->where('room_schedules.day', 'Su');
            }

            $deletedCount = $query->delete();

            return response()->json([
                'success' => true,
                'message' => "Successfully removed {$deletedCount} {$dayType} schedules"
            ]);

        } catch (\Exception $e) {
            \Log::error('Error in removeBatchSchedules: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error removing schedules: ' . $e->getMessage()
            ]);
        }
    }
}
