<div class="modal-dialog">
    <div class="modal-content">
        <form action="{{ route('collegeadmin.section.update') }}" method="post">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Edit Section: {{ $section->section_name }}</h4>
            </div>
            <div class="modal-body">
                {{ csrf_field() }}
                <input type="hidden" name="section_id" value="{{ $section->id }}">
                
                <div class="form-group">
                    <label>College</label>
                    <input type="text" class="form-control" value="{{ $college->college_code }} - {{ $college->college_name }}" readonly>
                </div>

                <div class="form-group">
                    <label>Program</label>
                    <select class="form-control" name="program_code" required>
                        <option value="">Please Select Program</option>
                        @foreach($programs as $program)
                        <option value="{{ $program['program_code'] }}" {{ $section->program_code == $program['program_code'] ? 'selected' : '' }}>
                            {{ $program['program_code'] }} - {{ $program['program_name'] }}
                        </option>
                        @endforeach
                    </select>
                </div>

                <div class="form-group">
                    <label>Level</label>
                    <select name="level" class="form-control" required>
                        <option value="">Please Select Level</option>
                        <option value="1st Year" {{ $section->level == '1st Year' ? 'selected' : '' }}>1st Year</option>
                        <option value="2nd Year" {{ $section->level == '2nd Year' ? 'selected' : '' }}>2nd Year</option>
                        <option value="3rd Year" {{ $section->level == '3rd Year' ? 'selected' : '' }}>3rd Year</option>
                        <option value="4th Year" {{ $section->level == '4th Year' ? 'selected' : '' }}>4th Year</option>
                        <option value="5th Year" {{ $section->level == '5th Year' ? 'selected' : '' }}>5th Year</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Section Name</label>
                    <input type="text" name="section_name" class="form-control" value="{{ $section->section_name }}" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-flat btn-default" data-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-flat btn-primary" onclick="return confirm('Update this section?')">
                    <i class="fa fa-save"></i> Update Section
                </button>
            </div>
        </form>
    </div>
</div>
