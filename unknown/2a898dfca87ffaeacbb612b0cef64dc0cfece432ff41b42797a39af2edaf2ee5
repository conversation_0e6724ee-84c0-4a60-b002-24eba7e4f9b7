@extends('vendor.adminlte.collegeadmin_layout.app')

@section('title', 'CLASSMOS - Section: ' . $section->section_name)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-book"></i>
        Section: {{ $section->section_name }}
        <small>{{ $section->program_code }} - {{ $section->level }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.section.index') }}">Section Management</a></li>
        <li class="active">{{ $section->section_name }}</li>
    </ol>

    <div class="pull-right">
        <a href="{{ route('collegeadmin.section.index') }}" class="btn btn-flat btn-default">
            <i class="fa fa-arrow-left"></i> Back
        </a>
        <a href="{{ route('collegeadmin.section.add_all', $section->id) }}" class="btn btn-flat btn-success" onclick="return confirm('Add all available curriculum subjects to this section?')">
            <i class="fa fa-plus-circle"></i> Add All Available Subjects
        </a>
    </div>
    <hr>
</section>

<section class="content">
    <div class="container-fluid">
        @if(Session::has('success'))
        <div class='col-sm-12'>
            <div class='alert alert-success alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-check"></i> {{ Session::get('success') }}
            </div>
        </div>
        @endif

        @if(Session::has('error'))
        <div class='col-sm-12'>
            <div class='alert alert-danger alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-ban"></i> {{ Session::get('error') }}
            </div>
        </div>
        @endif

        @if(Session::has('info'))
        <div class='col-sm-12'>
            <div class='alert alert-info alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-info"></i> {{ Session::get('info') }}
            </div>
        </div>
        @endif

        <div class="row">
            <!-- Section Information -->
            <div class="col-sm-4">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Section Information</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-bordered">
                            <tr>
                                <th>Section Name:</th>
                                <td>{{ $section->section_name }}</td>
                            </tr>
                            <tr>
                                <th>Program:</th>
                                <td>{{ $section->program_code }}</td>
                            </tr>
                            <tr>
                                <th>Level:</th>
                                <td>{{ $section->level }}</td>
                            </tr>
                            <tr>
                                <th>College:</th>
                                <td>{{ $section->college_code }}</td>
                            </tr>
                            <tr>
                                <th>Total Subjects:</th>
                                <td>{{ count($offerings) }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Add Subjects -->
                @if(count($availableCurricula) > 0)
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title">Add Subjects</h3>
                    </div>
                    <div class="box-body">
                        <form action="{{ route('collegeadmin.section.add_selected', $section->id) }}" method="post">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <label>Available Curriculum Subjects:</label>
                                <select name="curriculum_ids[]" class="form-control select2" multiple="multiple" data-placeholder="Select subjects to add" style="width: 100%;">
                                    @foreach($availableCurricula as $curriculum)
                                    <option value="{{ $curriculum->id }}">
                                        {{ $curriculum->course_code }} - {{ $curriculum->course_name }}
                                        ({{ $curriculum->level }}, {{ $curriculum->period }})
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <button type="submit" class="btn btn-flat btn-success btn-block" onclick="return confirm('Add selected subjects to this section?')">
                                <i class="fa fa-plus"></i> Add Selected Subjects
                            </button>
                        </form>
                    </div>
                </div>
                @endif
            </div>

            <!-- Current Subjects -->
            <div class="col-sm-8">
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title">Current Subjects in {{ $section->section_name }}</h3>
                    </div>
                    <div class="box-body">
                        @if(count($offerings) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Subject Code</th>
                                        <th>Subject Name</th>
                                        <th>Level</th>
                                        <th>Period</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($offerings as $offering)
                                    @if($offering->curriculum)
                                    <tr>
                                        <td>{{ $offering->curriculum->course_code }}</td>
                                        <td>{{ $offering->curriculum->course_name }}</td>
                                        <td>{{ $offering->curriculum->level }}</td>
                                        <td>{{ $offering->curriculum->period }}</td>
                                        <td>{{ $offering->curriculum->lec }}</td>
                                        <td>{{ $offering->curriculum->lab }}</td>
                                        <td>{{ $offering->curriculum->units }}</td>
                                        <td>
                                            <a href="{{ route('collegeadmin.section.remove_offering', $offering->id) }}" 
                                               class="btn btn-flat btn-danger btn-sm" 
                                               title="Remove Subject" 
                                               onclick="return confirm('Remove {{ $offering->curriculum->course_code }} from this section?')">
                                                <i class="fa fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endif
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> No subjects have been added to this section yet.
                            <br><br>
                            <a href="{{ route('collegeadmin.section.add_all', $section->id) }}" class="btn btn-flat btn-success" onclick="return confirm('Add all available curriculum subjects to this section?')">
                                <i class="fa fa-plus-circle"></i> Add All Available Subjects
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script src='{{asset('plugins/select2/select2.min.js')}}'></script>
<script>
$(document).ready(function() {
    $('.select2').select2();
});
</script>
@endsection
