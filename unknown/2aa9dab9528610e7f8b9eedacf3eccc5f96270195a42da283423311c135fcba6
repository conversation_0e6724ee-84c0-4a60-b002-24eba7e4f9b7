<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header bg-success">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <h4 class="modal-title">Add Subjects to {{ $section->section_name }}</h4>
        </div>
        <form action="{{ route('collegeadmin.section.add_selected', $section->id) }}" method="post">
            <div class="modal-body">
                {{ csrf_field() }}
                
                @if(count($availableCurricula) > 0)
                <p>Select the curriculum subjects you want to add to this section:</p>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th width="50px">
                                    <input type="checkbox" id="select_all"> Select All
                                </th>
                                <th>Subject Code</th>
                                <th>Subject Name</th>
                                <th>Level</th>
                                <th>Period</th>
                                <th>Lec</th>
                                <th>Lab</th>
                                <th>Units</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($availableCurricula as $curriculum)
                            <tr>
                                <td>
                                    <input type="checkbox" name="curriculum_ids[]" value="{{ $curriculum->id }}" class="curriculum_checkbox">
                                </td>
                                <td>{{ $curriculum->course_code }}</td>
                                <td>{{ $curriculum->course_name }}</td>
                                <td>{{ $curriculum->level }}</td>
                                <td>{{ $curriculum->period }}</td>
                                <td>{{ $curriculum->lec }}</td>
                                <td>{{ $curriculum->lab }}</td>
                                <td>{{ $curriculum->units }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> No additional curriculum subjects are available to add to this section.
                    All available subjects for this program and level have already been added.
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-flat btn-default" data-dismiss="modal">Cancel</button>
                @if(count($availableCurricula) > 0)
                <button type="submit" class="btn btn-flat btn-success" onclick="return confirm('Add selected subjects to this section?')">
                    <i class="fa fa-plus"></i> Add Selected Subjects
                </button>
                @endif
            </div>
        </form>
    </div>
</div>

<script>
// Select all functionality
$('#select_all').change(function() {
    $('.curriculum_checkbox').prop('checked', this.checked);
});

// Update select all when individual checkboxes change
$('.curriculum_checkbox').change(function() {
    if ($('.curriculum_checkbox:checked').length == $('.curriculum_checkbox').length) {
        $('#select_all').prop('checked', true);
    } else {
        $('#select_all').prop('checked', false);
    }
});
</script>
