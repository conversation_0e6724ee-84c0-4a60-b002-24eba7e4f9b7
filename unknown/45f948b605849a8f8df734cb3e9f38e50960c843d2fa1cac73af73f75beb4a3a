<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class offerings_infos_table extends Model
{
    protected $table = 'offerings_infos';

    protected $fillable = [
        'curriculum_id',
        'section_name',
        'level',
        'semester',
        'course_type',
        'description'
    ];

    /**
     * Get the curriculum record associated with this offering
     */
    public function curriculum()
    {
        return $this->belongsTo('App\curriculum', 'curriculum_id');
    }

    /**
     * Get the curriculum record with subject preloaded
     */
    public function curriculumWithSubject()
    {
        return $this->belongsTo('App\curriculum', 'curriculum_id')->with('subject');
    }

    /**
     * Get the schedules associated with this offering
     */
    public function schedules()
    {
        return $this->hasMany('App\room_schedules', 'offering_id');
    }

    /**
     * Check if a curriculum is already offered for a section
     *
     * @param int $curriculum_id
     * @param string $section_name
     * @return bool
     */
    public static function isOffered($curriculum_id, $section_name)
    {
        return self::where('curriculum_id', $curriculum_id)
            ->where('section_name', $section_name)
            ->exists();
    }

    /**
     * Add a curriculum to course offerings for a specific section
     *
     * @param int $curriculum_id
     * @param string $section_name
     * @param string $level
     * @param string|null $semester
     * @return offerings_infos_table|null
     */
    public static function addToOfferings($curriculum_id, $section_name, $level, $semester = null)
    {
        // Check if already offered
        if (self::isOffered($curriculum_id, $section_name)) {
            return null;
        }

        // If semester is not provided, try to get it from the curriculum
        if ($semester === null) {
            $curriculum = \App\curriculum::find($curriculum_id);
            if ($curriculum) {
                $semester = $curriculum->period;
            }
        }

        // Create new offering
        $offering = new self;
        $offering->curriculum_id = $curriculum_id;
        $offering->section_name = $section_name;
        $offering->level = $level;
        $offering->semester = $semester;
        $offering->save();

        return $offering;
    }

    /**
     * Get standardized semester options
     *
     * @return array
     */
    public static function getSemesterOptions()
    {
        return [
            'First Semester' => 'First Semester',
            'Second Semester' => 'Second Semester',
            'Mid-year' => 'Mid-year'
        ];
    }
}
