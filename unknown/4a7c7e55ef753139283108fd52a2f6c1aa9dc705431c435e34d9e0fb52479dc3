<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CtrSection extends Model
{
    protected $table = 'ctr_sections';

    protected $fillable = [
        'program_code',
        'level',
        'section_name',
        'college_code'
    ];

    /**
     * Get all course offerings for this section
     */
    public function offerings()
    {
        return $this->hasMany('App\offerings_infos_table', 'section_name', 'section_name');
    }

    /**
     * Get all curricula for this section's program and level
     */
    public function curricula()
    {
        return $this->hasManyThrough(
            'App\curriculum',
            'App\offerings_infos_table',
            'section_name', // Foreign key on offerings_infos_table
            'id', // Foreign key on curriculum
            'section_name', // Local key on ctr_sections
            'curriculum_id' // Local key on offerings_infos_table
        );
    }

    /**
     * Get all available curricula for this section that are not yet offered
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAvailableCurricula()
    {
        // Get all curricula for this program and level
        $query = curriculum::where('program_code', $this->program_code)
            ->where('level', $this->level)
            ->where('is_active', 1);

        // Add college filter if college_code is set
        if (!empty($this->college_code)) {
            $query->where('college_code', $this->college_code);
        }

        $allCurricula = $query->get();

        // Get all curricula already offered for this section
        $offeredCurriculaIds = offerings_infos_table::where('section_name', $this->section_name)
            ->pluck('curriculum_id')
            ->toArray();

        // Filter out curricula that are already offered
        return $allCurricula->filter(function ($curriculum) use ($offeredCurriculaIds) {
            return !in_array($curriculum->id, $offeredCurriculaIds);
        });
    }

    /**
     * Add all available curricula to this section's offerings
     *
     * @return int Number of curricula added
     */
    public function addAllCurriculaToOfferings()
    {
        $availableCurricula = $this->getAvailableCurricula();
        $addedCount = 0;

        foreach ($availableCurricula as $curriculum) {
            $added = offerings_infos_table::addToOfferings(
                $curriculum->id,
                $this->section_name,
                $this->level
            );

            if ($added) {
                $addedCount++;
            }
        }

        return $addedCount;
    }

    /**
     * Get sections for a specific program and level
     *
     * @param string $programCode
     * @param string $level
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSectionsForProgramAndLevel($programCode, $level)
    {
        return self::where('program_code', $programCode)
            ->where('level', $level)
            ->where('is_active', 1)
            ->get();
    }

    /**
     * Create a new section if it doesn't exist
     *
     * @param string $programCode
     * @param string $level
     * @param string $sectionName
     * @param string|null $collegeCode
     * @return CtrSection|null
     */
    public static function createIfNotExists($programCode, $level, $sectionName, $collegeCode = null)
    {
        $exists = self::where('program_code', $programCode)
            ->where('level', $level)
            ->where('section_name', $sectionName)
            ->exists();

        if ($exists) {
            return null;
        }

        // If college code is not provided, try to find it from the program
        if (empty($collegeCode)) {
            // Try to find a college with this program in its description
            $colleges = \App\College::all();
            foreach ($colleges as $college) {
                if ($college->hasProgram($programCode)) {
                    $collegeCode = $college->college_code;
                    break;
                }
            }

            // If still not found, try to get from academic_programs
            if (empty($collegeCode)) {
                $program = \App\academic_programs::where('program_code', $programCode)->first();
                if ($program && !empty($program->college_code)) {
                    $collegeCode = $program->college_code;
                }
            }
        }

        $section = new self;
        $section->program_code = $programCode;
        $section->level = $level;
        $section->section_name = $sectionName;
        $section->college_code = $collegeCode;
        $section->is_active = 1;
        $section->save();

        return $section;
    }

    /**
     * Get the college that this section belongs to
     */
    public function college()
    {
        return $this->belongsTo('App\College', 'college_code', 'college_code');
    }

    /**
     * Get sections for a specific college
     *
     * @param string $collegeCode
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSectionsForCollege($collegeCode)
    {
        return self::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->get();
    }
}
