@charset "UTF-8";
@import url(https://fonts.googleapis.com/css?family=Raleway:300,400,600);
/*!
  Ionicons, v3.0.0-alpha.3
  Created by <PERSON> for the Ionic Framework, http://ionicons.com/
  https://twitter.com/benjsperry  https://twitter.com/ionicframework
  MIT License: https://github.com/driftyco/ionicons

  Android-style icons originally built by Google’s
  Material Design Icons: https://github.com/google/material-design-icons
  used under CC BY http://creativecommons.org/licenses/by/4.0/
  Modified icons to fit ionicon’s grid from original.
*/
@font-face {
  font-family: "Ionicons";
  src: url("../fonts/ionicons.eot?v=3.0.0-alpha.3");
  src: url("../fonts/ionicons.eot?v=3.0.0-alpha.3#iefix") format("embedded-opentype"), url("../fonts/ionicons.woff2?v=3.0.0-alpha.3") format("woff2"), url("../fonts/ionicons.woff?v=3.0.0-alpha.3") format("woff"), url("../fonts/ionicons.ttf?v=3.0.0-alpha.3") format("truetype"), url("../fonts/ionicons.svg?v=3.0.0-alpha.3#Ionicons") format("svg");
  font-weight: normal;
  font-style: normal;
}

.ion, .ionicons,
.ion-ios-add:before,
.ion-ios-add-circle:before,
.ion-ios-add-circle-outline:before,
.ion-ios-add-outline:before,
.ion-ios-alarm:before,
.ion-ios-alarm-outline:before,
.ion-ios-albums:before,
.ion-ios-albums-outline:before,
.ion-ios-alert:before,
.ion-ios-alert-outline:before,
.ion-ios-american-football:before,
.ion-ios-american-football-outline:before,
.ion-ios-analytics:before,
.ion-ios-analytics-outline:before,
.ion-ios-aperture:before,
.ion-ios-aperture-outline:before,
.ion-ios-apps:before,
.ion-ios-apps-outline:before,
.ion-ios-appstore:before,
.ion-ios-appstore-outline:before,
.ion-ios-archive:before,
.ion-ios-archive-outline:before,
.ion-ios-arrow-back:before,
.ion-ios-arrow-back-outline:before,
.ion-ios-arrow-down:before,
.ion-ios-arrow-down-outline:before,
.ion-ios-arrow-dropdown:before,
.ion-ios-arrow-dropdown-circle:before,
.ion-ios-arrow-dropdown-circle-outline:before,
.ion-ios-arrow-dropdown-outline:before,
.ion-ios-arrow-dropleft:before,
.ion-ios-arrow-dropleft-circle:before,
.ion-ios-arrow-dropleft-circle-outline:before,
.ion-ios-arrow-dropleft-outline:before,
.ion-ios-arrow-dropright:before,
.ion-ios-arrow-dropright-circle:before,
.ion-ios-arrow-dropright-circle-outline:before,
.ion-ios-arrow-dropright-outline:before,
.ion-ios-arrow-dropup:before,
.ion-ios-arrow-dropup-circle:before,
.ion-ios-arrow-dropup-circle-outline:before,
.ion-ios-arrow-dropup-outline:before,
.ion-ios-arrow-forward:before,
.ion-ios-arrow-forward-outline:before,
.ion-ios-arrow-round-back:before,
.ion-ios-arrow-round-back-outline:before,
.ion-ios-arrow-round-down:before,
.ion-ios-arrow-round-down-outline:before,
.ion-ios-arrow-round-forward:before,
.ion-ios-arrow-round-forward-outline:before,
.ion-ios-arrow-round-up:before,
.ion-ios-arrow-round-up-outline:before,
.ion-ios-arrow-up:before,
.ion-ios-arrow-up-outline:before,
.ion-ios-at:before,
.ion-ios-at-outline:before,
.ion-ios-attach:before,
.ion-ios-attach-outline:before,
.ion-ios-backspace:before,
.ion-ios-backspace-outline:before,
.ion-ios-barcode:before,
.ion-ios-barcode-outline:before,
.ion-ios-baseball:before,
.ion-ios-baseball-outline:before,
.ion-ios-basket:before,
.ion-ios-basket-outline:before,
.ion-ios-basketball:before,
.ion-ios-basketball-outline:before,
.ion-ios-battery-charging:before,
.ion-ios-battery-charging-outline:before,
.ion-ios-battery-dead:before,
.ion-ios-battery-dead-outline:before,
.ion-ios-battery-full:before,
.ion-ios-battery-full-outline:before,
.ion-ios-beaker:before,
.ion-ios-beaker-outline:before,
.ion-ios-beer:before,
.ion-ios-beer-outline:before,
.ion-ios-bicycle:before,
.ion-ios-bicycle-outline:before,
.ion-ios-bluetooth:before,
.ion-ios-bluetooth-outline:before,
.ion-ios-boat:before,
.ion-ios-boat-outline:before,
.ion-ios-body:before,
.ion-ios-body-outline:before,
.ion-ios-bonfire:before,
.ion-ios-bonfire-outline:before,
.ion-ios-book:before,
.ion-ios-book-outline:before,
.ion-ios-bookmark:before,
.ion-ios-bookmark-outline:before,
.ion-ios-bookmarks:before,
.ion-ios-bookmarks-outline:before,
.ion-ios-bowtie:before,
.ion-ios-bowtie-outline:before,
.ion-ios-briefcase:before,
.ion-ios-briefcase-outline:before,
.ion-ios-browsers:before,
.ion-ios-browsers-outline:before,
.ion-ios-brush:before,
.ion-ios-brush-outline:before,
.ion-ios-bug:before,
.ion-ios-bug-outline:before,
.ion-ios-build:before,
.ion-ios-build-outline:before,
.ion-ios-bulb:before,
.ion-ios-bulb-outline:before,
.ion-ios-bus:before,
.ion-ios-bus-outline:before,
.ion-ios-cafe:before,
.ion-ios-cafe-outline:before,
.ion-ios-calculator:before,
.ion-ios-calculator-outline:before,
.ion-ios-calendar:before,
.ion-ios-calendar-outline:before,
.ion-ios-call:before,
.ion-ios-call-outline:before,
.ion-ios-camera:before,
.ion-ios-camera-outline:before,
.ion-ios-car:before,
.ion-ios-car-outline:before,
.ion-ios-card:before,
.ion-ios-card-outline:before,
.ion-ios-cart:before,
.ion-ios-cart-outline:before,
.ion-ios-cash:before,
.ion-ios-cash-outline:before,
.ion-ios-chatboxes:before,
.ion-ios-chatboxes-outline:before,
.ion-ios-chatbubbles:before,
.ion-ios-chatbubbles-outline:before,
.ion-ios-checkbox:before,
.ion-ios-checkbox-outline:before,
.ion-ios-checkmark:before,
.ion-ios-checkmark-circle:before,
.ion-ios-checkmark-circle-outline:before,
.ion-ios-checkmark-outline:before,
.ion-ios-clipboard:before,
.ion-ios-clipboard-outline:before,
.ion-ios-clock:before,
.ion-ios-clock-outline:before,
.ion-ios-close:before,
.ion-ios-close-circle:before,
.ion-ios-close-circle-outline:before,
.ion-ios-close-outline:before,
.ion-ios-closed-captioning:before,
.ion-ios-closed-captioning-outline:before,
.ion-ios-cloud:before,
.ion-ios-cloud-circle:before,
.ion-ios-cloud-circle-outline:before,
.ion-ios-cloud-done:before,
.ion-ios-cloud-done-outline:before,
.ion-ios-cloud-download:before,
.ion-ios-cloud-download-outline:before,
.ion-ios-cloud-outline:before,
.ion-ios-cloud-upload:before,
.ion-ios-cloud-upload-outline:before,
.ion-ios-cloudy:before,
.ion-ios-cloudy-night:before,
.ion-ios-cloudy-night-outline:before,
.ion-ios-cloudy-outline:before,
.ion-ios-code:before,
.ion-ios-code-download:before,
.ion-ios-code-download-outline:before,
.ion-ios-code-outline:before,
.ion-ios-code-working:before,
.ion-ios-code-working-outline:before,
.ion-ios-cog:before,
.ion-ios-cog-outline:before,
.ion-ios-color-fill:before,
.ion-ios-color-fill-outline:before,
.ion-ios-color-filter:before,
.ion-ios-color-filter-outline:before,
.ion-ios-color-palette:before,
.ion-ios-color-palette-outline:before,
.ion-ios-color-wand:before,
.ion-ios-color-wand-outline:before,
.ion-ios-compass:before,
.ion-ios-compass-outline:before,
.ion-ios-construct:before,
.ion-ios-construct-outline:before,
.ion-ios-contact:before,
.ion-ios-contact-outline:before,
.ion-ios-contacts:before,
.ion-ios-contacts-outline:before,
.ion-ios-contract:before,
.ion-ios-contract-outline:before,
.ion-ios-contrast:before,
.ion-ios-contrast-outline:before,
.ion-ios-copy:before,
.ion-ios-copy-outline:before,
.ion-ios-create:before,
.ion-ios-create-outline:before,
.ion-ios-crop:before,
.ion-ios-crop-outline:before,
.ion-ios-cube:before,
.ion-ios-cube-outline:before,
.ion-ios-cut:before,
.ion-ios-cut-outline:before,
.ion-ios-desktop:before,
.ion-ios-desktop-outline:before,
.ion-ios-disc:before,
.ion-ios-disc-outline:before,
.ion-ios-document:before,
.ion-ios-document-outline:before,
.ion-ios-done-all:before,
.ion-ios-done-all-outline:before,
.ion-ios-download:before,
.ion-ios-download-outline:before,
.ion-ios-easel:before,
.ion-ios-easel-outline:before,
.ion-ios-egg:before,
.ion-ios-egg-outline:before,
.ion-ios-exit:before,
.ion-ios-exit-outline:before,
.ion-ios-expand:before,
.ion-ios-expand-outline:before,
.ion-ios-eye:before,
.ion-ios-eye-off:before,
.ion-ios-eye-off-outline:before,
.ion-ios-eye-outline:before,
.ion-ios-fastforward:before,
.ion-ios-fastforward-outline:before,
.ion-ios-female:before,
.ion-ios-female-outline:before,
.ion-ios-filing:before,
.ion-ios-filing-outline:before,
.ion-ios-film:before,
.ion-ios-film-outline:before,
.ion-ios-finger-print:before,
.ion-ios-finger-print-outline:before,
.ion-ios-flag:before,
.ion-ios-flag-outline:before,
.ion-ios-flame:before,
.ion-ios-flame-outline:before,
.ion-ios-flash:before,
.ion-ios-flash-outline:before,
.ion-ios-flask:before,
.ion-ios-flask-outline:before,
.ion-ios-flower:before,
.ion-ios-flower-outline:before,
.ion-ios-folder:before,
.ion-ios-folder-open:before,
.ion-ios-folder-open-outline:before,
.ion-ios-folder-outline:before,
.ion-ios-football:before,
.ion-ios-football-outline:before,
.ion-ios-funnel:before,
.ion-ios-funnel-outline:before,
.ion-ios-game-controller-a:before,
.ion-ios-game-controller-a-outline:before,
.ion-ios-game-controller-b:before,
.ion-ios-game-controller-b-outline:before,
.ion-ios-git-branch:before,
.ion-ios-git-branch-outline:before,
.ion-ios-git-commit:before,
.ion-ios-git-commit-outline:before,
.ion-ios-git-compare:before,
.ion-ios-git-compare-outline:before,
.ion-ios-git-merge:before,
.ion-ios-git-merge-outline:before,
.ion-ios-git-network:before,
.ion-ios-git-network-outline:before,
.ion-ios-git-pull-request:before,
.ion-ios-git-pull-request-outline:before,
.ion-ios-glasses:before,
.ion-ios-glasses-outline:before,
.ion-ios-globe:before,
.ion-ios-globe-outline:before,
.ion-ios-grid:before,
.ion-ios-grid-outline:before,
.ion-ios-hammer:before,
.ion-ios-hammer-outline:before,
.ion-ios-hand:before,
.ion-ios-hand-outline:before,
.ion-ios-happy:before,
.ion-ios-happy-outline:before,
.ion-ios-headset:before,
.ion-ios-headset-outline:before,
.ion-ios-heart:before,
.ion-ios-heart-outline:before,
.ion-ios-help:before,
.ion-ios-help-buoy:before,
.ion-ios-help-buoy-outline:before,
.ion-ios-help-circle:before,
.ion-ios-help-circle-outline:before,
.ion-ios-help-outline:before,
.ion-ios-home:before,
.ion-ios-home-outline:before,
.ion-ios-ice-cream:before,
.ion-ios-ice-cream-outline:before,
.ion-ios-image:before,
.ion-ios-image-outline:before,
.ion-ios-images:before,
.ion-ios-images-outline:before,
.ion-ios-infinite:before,
.ion-ios-infinite-outline:before,
.ion-ios-information:before,
.ion-ios-information-circle:before,
.ion-ios-information-circle-outline:before,
.ion-ios-information-outline:before,
.ion-ios-ionic:before,
.ion-ios-ionic-outline:before,
.ion-ios-ionitron:before,
.ion-ios-ionitron-outline:before,
.ion-ios-jet:before,
.ion-ios-jet-outline:before,
.ion-ios-key:before,
.ion-ios-key-outline:before,
.ion-ios-keypad:before,
.ion-ios-keypad-outline:before,
.ion-ios-laptop:before,
.ion-ios-laptop-outline:before,
.ion-ios-leaf:before,
.ion-ios-leaf-outline:before,
.ion-ios-link:before,
.ion-ios-link-outline:before,
.ion-ios-list:before,
.ion-ios-list-box:before,
.ion-ios-list-box-outline:before,
.ion-ios-list-outline:before,
.ion-ios-locate:before,
.ion-ios-locate-outline:before,
.ion-ios-lock:before,
.ion-ios-lock-outline:before,
.ion-ios-log-in:before,
.ion-ios-log-in-outline:before,
.ion-ios-log-out:before,
.ion-ios-log-out-outline:before,
.ion-ios-magnet:before,
.ion-ios-magnet-outline:before,
.ion-ios-mail:before,
.ion-ios-mail-open:before,
.ion-ios-mail-open-outline:before,
.ion-ios-mail-outline:before,
.ion-ios-male:before,
.ion-ios-male-outline:before,
.ion-ios-man:before,
.ion-ios-man-outline:before,
.ion-ios-map:before,
.ion-ios-map-outline:before,
.ion-ios-medal:before,
.ion-ios-medal-outline:before,
.ion-ios-medical:before,
.ion-ios-medical-outline:before,
.ion-ios-medkit:before,
.ion-ios-medkit-outline:before,
.ion-ios-megaphone:before,
.ion-ios-megaphone-outline:before,
.ion-ios-menu:before,
.ion-ios-menu-outline:before,
.ion-ios-mic:before,
.ion-ios-mic-off:before,
.ion-ios-mic-off-outline:before,
.ion-ios-mic-outline:before,
.ion-ios-microphone:before,
.ion-ios-microphone-outline:before,
.ion-ios-moon:before,
.ion-ios-moon-outline:before,
.ion-ios-more:before,
.ion-ios-more-outline:before,
.ion-ios-move:before,
.ion-ios-move-outline:before,
.ion-ios-musical-note:before,
.ion-ios-musical-note-outline:before,
.ion-ios-musical-notes:before,
.ion-ios-musical-notes-outline:before,
.ion-ios-navigate:before,
.ion-ios-navigate-outline:before,
.ion-ios-no-smoking:before,
.ion-ios-no-smoking-outline:before,
.ion-ios-notifications:before,
.ion-ios-notifications-off:before,
.ion-ios-notifications-off-outline:before,
.ion-ios-notifications-outline:before,
.ion-ios-nuclear:before,
.ion-ios-nuclear-outline:before,
.ion-ios-nutrition:before,
.ion-ios-nutrition-outline:before,
.ion-ios-open:before,
.ion-ios-open-outline:before,
.ion-ios-options:before,
.ion-ios-options-outline:before,
.ion-ios-outlet:before,
.ion-ios-outlet-outline:before,
.ion-ios-paper:before,
.ion-ios-paper-outline:before,
.ion-ios-paper-plane:before,
.ion-ios-paper-plane-outline:before,
.ion-ios-partly-sunny:before,
.ion-ios-partly-sunny-outline:before,
.ion-ios-pause:before,
.ion-ios-pause-outline:before,
.ion-ios-paw:before,
.ion-ios-paw-outline:before,
.ion-ios-people:before,
.ion-ios-people-outline:before,
.ion-ios-person:before,
.ion-ios-person-add:before,
.ion-ios-person-add-outline:before,
.ion-ios-person-outline:before,
.ion-ios-phone-landscape:before,
.ion-ios-phone-landscape-outline:before,
.ion-ios-phone-portrait:before,
.ion-ios-phone-portrait-outline:before,
.ion-ios-photos:before,
.ion-ios-photos-outline:before,
.ion-ios-pie:before,
.ion-ios-pie-outline:before,
.ion-ios-pin:before,
.ion-ios-pin-outline:before,
.ion-ios-pint:before,
.ion-ios-pint-outline:before,
.ion-ios-pizza:before,
.ion-ios-pizza-outline:before,
.ion-ios-plane:before,
.ion-ios-plane-outline:before,
.ion-ios-planet:before,
.ion-ios-planet-outline:before,
.ion-ios-play:before,
.ion-ios-play-outline:before,
.ion-ios-podium:before,
.ion-ios-podium-outline:before,
.ion-ios-power:before,
.ion-ios-power-outline:before,
.ion-ios-pricetag:before,
.ion-ios-pricetag-outline:before,
.ion-ios-pricetags:before,
.ion-ios-pricetags-outline:before,
.ion-ios-print:before,
.ion-ios-print-outline:before,
.ion-ios-pulse:before,
.ion-ios-pulse-outline:before,
.ion-ios-qr-scanner:before,
.ion-ios-qr-scanner-outline:before,
.ion-ios-quote:before,
.ion-ios-quote-outline:before,
.ion-ios-radio:before,
.ion-ios-radio-button-off:before,
.ion-ios-radio-button-off-outline:before,
.ion-ios-radio-button-on:before,
.ion-ios-radio-button-on-outline:before,
.ion-ios-radio-outline:before,
.ion-ios-rainy:before,
.ion-ios-rainy-outline:before,
.ion-ios-recording:before,
.ion-ios-recording-outline:before,
.ion-ios-redo:before,
.ion-ios-redo-outline:before,
.ion-ios-refresh:before,
.ion-ios-refresh-circle:before,
.ion-ios-refresh-circle-outline:before,
.ion-ios-refresh-outline:before,
.ion-ios-remove:before,
.ion-ios-remove-circle:before,
.ion-ios-remove-circle-outline:before,
.ion-ios-remove-outline:before,
.ion-ios-reorder:before,
.ion-ios-reorder-outline:before,
.ion-ios-repeat:before,
.ion-ios-repeat-outline:before,
.ion-ios-resize:before,
.ion-ios-resize-outline:before,
.ion-ios-restaurant:before,
.ion-ios-restaurant-outline:before,
.ion-ios-return-left:before,
.ion-ios-return-left-outline:before,
.ion-ios-return-right:before,
.ion-ios-return-right-outline:before,
.ion-ios-reverse-camera:before,
.ion-ios-reverse-camera-outline:before,
.ion-ios-rewind:before,
.ion-ios-rewind-outline:before,
.ion-ios-ribbon:before,
.ion-ios-ribbon-outline:before,
.ion-ios-rose:before,
.ion-ios-rose-outline:before,
.ion-ios-sad:before,
.ion-ios-sad-outline:before,
.ion-ios-school:before,
.ion-ios-school-outline:before,
.ion-ios-search:before,
.ion-ios-search-outline:before,
.ion-ios-send:before,
.ion-ios-send-outline:before,
.ion-ios-settings:before,
.ion-ios-settings-outline:before,
.ion-ios-share:before,
.ion-ios-share-alt:before,
.ion-ios-share-alt-outline:before,
.ion-ios-share-outline:before,
.ion-ios-shirt:before,
.ion-ios-shirt-outline:before,
.ion-ios-shuffle:before,
.ion-ios-shuffle-outline:before,
.ion-ios-skip-backward:before,
.ion-ios-skip-backward-outline:before,
.ion-ios-skip-forward:before,
.ion-ios-skip-forward-outline:before,
.ion-ios-snow:before,
.ion-ios-snow-outline:before,
.ion-ios-speedometer:before,
.ion-ios-speedometer-outline:before,
.ion-ios-square:before,
.ion-ios-square-outline:before,
.ion-ios-star:before,
.ion-ios-star-half:before,
.ion-ios-star-half-outline:before,
.ion-ios-star-outline:before,
.ion-ios-stats:before,
.ion-ios-stats-outline:before,
.ion-ios-stopwatch:before,
.ion-ios-stopwatch-outline:before,
.ion-ios-subway:before,
.ion-ios-subway-outline:before,
.ion-ios-sunny:before,
.ion-ios-sunny-outline:before,
.ion-ios-swap:before,
.ion-ios-swap-outline:before,
.ion-ios-switch:before,
.ion-ios-switch-outline:before,
.ion-ios-sync:before,
.ion-ios-sync-outline:before,
.ion-ios-tablet-landscape:before,
.ion-ios-tablet-landscape-outline:before,
.ion-ios-tablet-portrait:before,
.ion-ios-tablet-portrait-outline:before,
.ion-ios-tennisball:before,
.ion-ios-tennisball-outline:before,
.ion-ios-text:before,
.ion-ios-text-outline:before,
.ion-ios-thermometer:before,
.ion-ios-thermometer-outline:before,
.ion-ios-thumbs-down:before,
.ion-ios-thumbs-down-outline:before,
.ion-ios-thumbs-up:before,
.ion-ios-thumbs-up-outline:before,
.ion-ios-thunderstorm:before,
.ion-ios-thunderstorm-outline:before,
.ion-ios-time:before,
.ion-ios-time-outline:before,
.ion-ios-timer:before,
.ion-ios-timer-outline:before,
.ion-ios-train:before,
.ion-ios-train-outline:before,
.ion-ios-transgender:before,
.ion-ios-transgender-outline:before,
.ion-ios-trash:before,
.ion-ios-trash-outline:before,
.ion-ios-trending-down:before,
.ion-ios-trending-down-outline:before,
.ion-ios-trending-up:before,
.ion-ios-trending-up-outline:before,
.ion-ios-trophy:before,
.ion-ios-trophy-outline:before,
.ion-ios-umbrella:before,
.ion-ios-umbrella-outline:before,
.ion-ios-undo:before,
.ion-ios-undo-outline:before,
.ion-ios-unlock:before,
.ion-ios-unlock-outline:before,
.ion-ios-videocam:before,
.ion-ios-videocam-outline:before,
.ion-ios-volume-down:before,
.ion-ios-volume-down-outline:before,
.ion-ios-volume-mute:before,
.ion-ios-volume-mute-outline:before,
.ion-ios-volume-off:before,
.ion-ios-volume-off-outline:before,
.ion-ios-volume-up:before,
.ion-ios-volume-up-outline:before,
.ion-ios-walk:before,
.ion-ios-walk-outline:before,
.ion-ios-warning:before,
.ion-ios-warning-outline:before,
.ion-ios-watch:before,
.ion-ios-watch-outline:before,
.ion-ios-water:before,
.ion-ios-water-outline:before,
.ion-ios-wifi:before,
.ion-ios-wifi-outline:before,
.ion-ios-wine:before,
.ion-ios-wine-outline:before,
.ion-ios-woman:before,
.ion-ios-woman-outline:before,
.ion-logo-android:before,
.ion-logo-angular:before,
.ion-logo-apple:before,
.ion-logo-bitcoin:before,
.ion-logo-buffer:before,
.ion-logo-chrome:before,
.ion-logo-codepen:before,
.ion-logo-css3:before,
.ion-logo-designernews:before,
.ion-logo-dribbble:before,
.ion-logo-dropbox:before,
.ion-logo-euro:before,
.ion-logo-facebook:before,
.ion-logo-foursquare:before,
.ion-logo-freebsd-devil:before,
.ion-logo-github:before,
.ion-logo-google:before,
.ion-logo-googleplus:before,
.ion-logo-hackernews:before,
.ion-logo-html5:before,
.ion-logo-instagram:before,
.ion-logo-javascript:before,
.ion-logo-linkedin:before,
.ion-logo-markdown:before,
.ion-logo-nodejs:before,
.ion-logo-octocat:before,
.ion-logo-pinterest:before,
.ion-logo-playstation:before,
.ion-logo-python:before,
.ion-logo-reddit:before,
.ion-logo-rss:before,
.ion-logo-sass:before,
.ion-logo-skype:before,
.ion-logo-snapchat:before,
.ion-logo-steam:before,
.ion-logo-tumblr:before,
.ion-logo-tux:before,
.ion-logo-twitch:before,
.ion-logo-twitter:before,
.ion-logo-usd:before,
.ion-logo-vimeo:before,
.ion-logo-whatsapp:before,
.ion-logo-windows:before,
.ion-logo-wordpress:before,
.ion-logo-xbox:before,
.ion-logo-yahoo:before,
.ion-logo-yen:before,
.ion-logo-youtube:before,
.ion-md-add:before,
.ion-md-add-circle:before,
.ion-md-alarm:before,
.ion-md-albums:before,
.ion-md-alert:before,
.ion-md-american-football:before,
.ion-md-analytics:before,
.ion-md-aperture:before,
.ion-md-apps:before,
.ion-md-appstore:before,
.ion-md-archive:before,
.ion-md-arrow-back:before,
.ion-md-arrow-down:before,
.ion-md-arrow-dropdown:before,
.ion-md-arrow-dropdown-circle:before,
.ion-md-arrow-dropleft:before,
.ion-md-arrow-dropleft-circle:before,
.ion-md-arrow-dropright:before,
.ion-md-arrow-dropright-circle:before,
.ion-md-arrow-dropup:before,
.ion-md-arrow-dropup-circle:before,
.ion-md-arrow-forward:before,
.ion-md-arrow-round-back:before,
.ion-md-arrow-round-down:before,
.ion-md-arrow-round-forward:before,
.ion-md-arrow-round-up:before,
.ion-md-arrow-up:before,
.ion-md-at:before,
.ion-md-attach:before,
.ion-md-backspace:before,
.ion-md-barcode:before,
.ion-md-baseball:before,
.ion-md-basket:before,
.ion-md-basketball:before,
.ion-md-battery-charging:before,
.ion-md-battery-dead:before,
.ion-md-battery-full:before,
.ion-md-beaker:before,
.ion-md-beer:before,
.ion-md-bicycle:before,
.ion-md-bluetooth:before,
.ion-md-boat:before,
.ion-md-body:before,
.ion-md-bonfire:before,
.ion-md-book:before,
.ion-md-bookmark:before,
.ion-md-bookmarks:before,
.ion-md-bowtie:before,
.ion-md-briefcase:before,
.ion-md-browsers:before,
.ion-md-brush:before,
.ion-md-bug:before,
.ion-md-build:before,
.ion-md-bulb:before,
.ion-md-bus:before,
.ion-md-cafe:before,
.ion-md-calculator:before,
.ion-md-calendar:before,
.ion-md-call:before,
.ion-md-camera:before,
.ion-md-car:before,
.ion-md-card:before,
.ion-md-cart:before,
.ion-md-cash:before,
.ion-md-chatboxes:before,
.ion-md-chatbubbles:before,
.ion-md-checkbox:before,
.ion-md-checkbox-outline:before,
.ion-md-checkmark:before,
.ion-md-checkmark-circle:before,
.ion-md-checkmark-circle-outline:before,
.ion-md-clipboard:before,
.ion-md-clock:before,
.ion-md-close:before,
.ion-md-close-circle:before,
.ion-md-closed-captioning:before,
.ion-md-cloud:before,
.ion-md-cloud-circle:before,
.ion-md-cloud-done:before,
.ion-md-cloud-download:before,
.ion-md-cloud-outline:before,
.ion-md-cloud-upload:before,
.ion-md-cloudy:before,
.ion-md-cloudy-night:before,
.ion-md-code:before,
.ion-md-code-download:before,
.ion-md-code-working:before,
.ion-md-cog:before,
.ion-md-color-fill:before,
.ion-md-color-filter:before,
.ion-md-color-palette:before,
.ion-md-color-wand:before,
.ion-md-compass:before,
.ion-md-construct:before,
.ion-md-contact:before,
.ion-md-contacts:before,
.ion-md-contract:before,
.ion-md-contrast:before,
.ion-md-copy:before,
.ion-md-create:before,
.ion-md-crop:before,
.ion-md-cube:before,
.ion-md-cut:before,
.ion-md-desktop:before,
.ion-md-disc:before,
.ion-md-document:before,
.ion-md-done-all:before,
.ion-md-download:before,
.ion-md-easel:before,
.ion-md-egg:before,
.ion-md-exit:before,
.ion-md-expand:before,
.ion-md-eye:before,
.ion-md-eye-off:before,
.ion-md-fastforward:before,
.ion-md-female:before,
.ion-md-filing:before,
.ion-md-film:before,
.ion-md-finger-print:before,
.ion-md-flag:before,
.ion-md-flame:before,
.ion-md-flash:before,
.ion-md-flask:before,
.ion-md-flower:before,
.ion-md-folder:before,
.ion-md-folder-open:before,
.ion-md-football:before,
.ion-md-funnel:before,
.ion-md-game-controller-a:before,
.ion-md-game-controller-b:before,
.ion-md-git-branch:before,
.ion-md-git-commit:before,
.ion-md-git-compare:before,
.ion-md-git-merge:before,
.ion-md-git-network:before,
.ion-md-git-pull-request:before,
.ion-md-glasses:before,
.ion-md-globe:before,
.ion-md-grid:before,
.ion-md-hammer:before,
.ion-md-hand:before,
.ion-md-happy:before,
.ion-md-headset:before,
.ion-md-heart:before,
.ion-md-heart-outline:before,
.ion-md-help:before,
.ion-md-help-buoy:before,
.ion-md-help-circle:before,
.ion-md-home:before,
.ion-md-ice-cream:before,
.ion-md-image:before,
.ion-md-images:before,
.ion-md-infinite:before,
.ion-md-information:before,
.ion-md-information-circle:before,
.ion-md-ionic:before,
.ion-md-ionitron:before,
.ion-md-jet:before,
.ion-md-key:before,
.ion-md-keypad:before,
.ion-md-laptop:before,
.ion-md-leaf:before,
.ion-md-link:before,
.ion-md-list:before,
.ion-md-list-box:before,
.ion-md-locate:before,
.ion-md-lock:before,
.ion-md-log-in:before,
.ion-md-log-out:before,
.ion-md-magnet:before,
.ion-md-mail:before,
.ion-md-mail-open:before,
.ion-md-male:before,
.ion-md-man:before,
.ion-md-map:before,
.ion-md-medal:before,
.ion-md-medical:before,
.ion-md-medkit:before,
.ion-md-megaphone:before,
.ion-md-menu:before,
.ion-md-mic:before,
.ion-md-mic-off:before,
.ion-md-microphone:before,
.ion-md-moon:before,
.ion-md-more:before,
.ion-md-move:before,
.ion-md-musical-note:before,
.ion-md-musical-notes:before,
.ion-md-navigate:before,
.ion-md-no-smoking:before,
.ion-md-notifications:before,
.ion-md-notifications-off:before,
.ion-md-notifications-outline:before,
.ion-md-nuclear:before,
.ion-md-nutrition:before,
.ion-md-open:before,
.ion-md-options:before,
.ion-md-outlet:before,
.ion-md-paper:before,
.ion-md-paper-plane:before,
.ion-md-partly-sunny:before,
.ion-md-pause:before,
.ion-md-paw:before,
.ion-md-people:before,
.ion-md-person:before,
.ion-md-person-add:before,
.ion-md-phone-landscape:before,
.ion-md-phone-portrait:before,
.ion-md-photos:before,
.ion-md-pie:before,
.ion-md-pin:before,
.ion-md-pint:before,
.ion-md-pizza:before,
.ion-md-plane:before,
.ion-md-planet:before,
.ion-md-play:before,
.ion-md-podium:before,
.ion-md-power:before,
.ion-md-pricetag:before,
.ion-md-pricetags:before,
.ion-md-print:before,
.ion-md-pulse:before,
.ion-md-qr-scanner:before,
.ion-md-quote:before,
.ion-md-radio:before,
.ion-md-radio-button-off:before,
.ion-md-radio-button-on:before,
.ion-md-rainy:before,
.ion-md-recording:before,
.ion-md-redo:before,
.ion-md-refresh:before,
.ion-md-refresh-circle:before,
.ion-md-remove:before,
.ion-md-remove-circle:before,
.ion-md-reorder:before,
.ion-md-repeat:before,
.ion-md-resize:before,
.ion-md-restaurant:before,
.ion-md-return-left:before,
.ion-md-return-right:before,
.ion-md-reverse-camera:before,
.ion-md-rewind:before,
.ion-md-ribbon:before,
.ion-md-rose:before,
.ion-md-sad:before,
.ion-md-school:before,
.ion-md-search:before,
.ion-md-send:before,
.ion-md-settings:before,
.ion-md-share:before,
.ion-md-share-alt:before,
.ion-md-shirt:before,
.ion-md-shuffle:before,
.ion-md-skip-backward:before,
.ion-md-skip-forward:before,
.ion-md-snow:before,
.ion-md-speedometer:before,
.ion-md-square:before,
.ion-md-square-outline:before,
.ion-md-star:before,
.ion-md-star-half:before,
.ion-md-star-outline:before,
.ion-md-stats:before,
.ion-md-stopwatch:before,
.ion-md-subway:before,
.ion-md-sunny:before,
.ion-md-swap:before,
.ion-md-switch:before,
.ion-md-sync:before,
.ion-md-tablet-landscape:before,
.ion-md-tablet-portrait:before,
.ion-md-tennisball:before,
.ion-md-text:before,
.ion-md-thermometer:before,
.ion-md-thumbs-down:before,
.ion-md-thumbs-up:before,
.ion-md-thunderstorm:before,
.ion-md-time:before,
.ion-md-timer:before,
.ion-md-train:before,
.ion-md-transgender:before,
.ion-md-trash:before,
.ion-md-trending-down:before,
.ion-md-trending-up:before,
.ion-md-trophy:before,
.ion-md-umbrella:before,
.ion-md-undo:before,
.ion-md-unlock:before,
.ion-md-videocam:before,
.ion-md-volume-down:before,
.ion-md-volume-mute:before,
.ion-md-volume-off:before,
.ion-md-volume-up:before,
.ion-md-walk:before,
.ion-md-warning:before,
.ion-md-watch:before,
.ion-md-water:before,
.ion-md-wifi:before,
.ion-md-wine:before,
.ion-md-woman:before {
  display: inline-block;
  font-family: "Ionicons";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ion-ios-add:before {
  content: "\f102";
}

.ion-ios-add-circle:before {
  content: "\f101";
}

.ion-ios-add-circle-outline:before {
  content: "\f100";
}

.ion-ios-add-outline:before {
  content: "\f102";
}

.ion-ios-alarm:before {
  content: "\f3c8";
}

.ion-ios-alarm-outline:before {
  content: "\f3c7";
}

.ion-ios-albums:before {
  content: "\f3ca";
}

.ion-ios-albums-outline:before {
  content: "\f3c9";
}

.ion-ios-alert:before {
  content: "\f104";
}

.ion-ios-alert-outline:before {
  content: "\f103";
}

.ion-ios-american-football:before {
  content: "\f106";
}

.ion-ios-american-football-outline:before {
  content: "\f105";
}

.ion-ios-analytics:before {
  content: "\f3ce";
}

.ion-ios-analytics-outline:before {
  content: "\f3cd";
}

.ion-ios-aperture:before {
  content: "\f108";
}

.ion-ios-aperture-outline:before {
  content: "\f107";
}

.ion-ios-apps:before {
  content: "\f10a";
}

.ion-ios-apps-outline:before {
  content: "\f109";
}

.ion-ios-appstore:before {
  content: "\f10c";
}

.ion-ios-appstore-outline:before {
  content: "\f10b";
}

.ion-ios-archive:before {
  content: "\f10e";
}

.ion-ios-archive-outline:before {
  content: "\f10d";
}

.ion-ios-arrow-back:before {
  content: "\f3cf";
}

.ion-ios-arrow-back-outline:before {
  content: "\f3cf";
}

.ion-ios-arrow-down:before {
  content: "\f3d0";
}

.ion-ios-arrow-down-outline:before {
  content: "\f3d0";
}

.ion-ios-arrow-dropdown:before {
  content: "\f110";
}

.ion-ios-arrow-dropdown-circle:before {
  content: "\f10f";
}

.ion-ios-arrow-dropdown-circle-outline:before {
  content: "\f10f";
}

.ion-ios-arrow-dropdown-outline:before {
  content: "\f110";
}

.ion-ios-arrow-dropleft:before {
  content: "\f112";
}

.ion-ios-arrow-dropleft-circle:before {
  content: "\f111";
}

.ion-ios-arrow-dropleft-circle-outline:before {
  content: "\f111";
}

.ion-ios-arrow-dropleft-outline:before {
  content: "\f112";
}

.ion-ios-arrow-dropright:before {
  content: "\f114";
}

.ion-ios-arrow-dropright-circle:before {
  content: "\f113";
}

.ion-ios-arrow-dropright-circle-outline:before {
  content: "\f113";
}

.ion-ios-arrow-dropright-outline:before {
  content: "\f114";
}

.ion-ios-arrow-dropup:before {
  content: "\f116";
}

.ion-ios-arrow-dropup-circle:before {
  content: "\f115";
}

.ion-ios-arrow-dropup-circle-outline:before {
  content: "\f115";
}

.ion-ios-arrow-dropup-outline:before {
  content: "\f116";
}

.ion-ios-arrow-forward:before {
  content: "\f3d1";
}

.ion-ios-arrow-forward-outline:before {
  content: "\f3d1";
}

.ion-ios-arrow-round-back:before {
  content: "\f117";
}

.ion-ios-arrow-round-back-outline:before {
  content: "\f117";
}

.ion-ios-arrow-round-down:before {
  content: "\f118";
}

.ion-ios-arrow-round-down-outline:before {
  content: "\f118";
}

.ion-ios-arrow-round-forward:before {
  content: "\f119";
}

.ion-ios-arrow-round-forward-outline:before {
  content: "\f119";
}

.ion-ios-arrow-round-up:before {
  content: "\f11a";
}

.ion-ios-arrow-round-up-outline:before {
  content: "\f11a";
}

.ion-ios-arrow-up:before {
  content: "\f3d8";
}

.ion-ios-arrow-up-outline:before {
  content: "\f3d8";
}

.ion-ios-at:before {
  content: "\f3da";
}

.ion-ios-at-outline:before {
  content: "\f3d9";
}

.ion-ios-attach:before {
  content: "\f11b";
}

.ion-ios-attach-outline:before {
  content: "\f11b";
}

.ion-ios-backspace:before {
  content: "\f11d";
}

.ion-ios-backspace-outline:before {
  content: "\f11c";
}

.ion-ios-barcode:before {
  content: "\f3dc";
}

.ion-ios-barcode-outline:before {
  content: "\f3db";
}

.ion-ios-baseball:before {
  content: "\f3de";
}

.ion-ios-baseball-outline:before {
  content: "\f3dd";
}

.ion-ios-basket:before {
  content: "\f11f";
}

.ion-ios-basket-outline:before {
  content: "\f11e";
}

.ion-ios-basketball:before {
  content: "\f3e0";
}

.ion-ios-basketball-outline:before {
  content: "\f3df";
}

.ion-ios-battery-charging:before {
  content: "\f120";
}

.ion-ios-battery-charging-outline:before {
  content: "\f120";
}

.ion-ios-battery-dead:before {
  content: "\f121";
}

.ion-ios-battery-dead-outline:before {
  content: "\f121";
}

.ion-ios-battery-full:before {
  content: "\f122";
}

.ion-ios-battery-full-outline:before {
  content: "\f122";
}

.ion-ios-beaker:before {
  content: "\f124";
}

.ion-ios-beaker-outline:before {
  content: "\f123";
}

.ion-ios-beer:before {
  content: "\f126";
}

.ion-ios-beer-outline:before {
  content: "\f125";
}

.ion-ios-bicycle:before {
  content: "\f127";
}

.ion-ios-bicycle-outline:before {
  content: "\f127";
}

.ion-ios-bluetooth:before {
  content: "\f128";
}

.ion-ios-bluetooth-outline:before {
  content: "\f128";
}

.ion-ios-boat:before {
  content: "\f12a";
}

.ion-ios-boat-outline:before {
  content: "\f129";
}

.ion-ios-body:before {
  content: "\f3e4";
}

.ion-ios-body-outline:before {
  content: "\f3e3";
}

.ion-ios-bonfire:before {
  content: "\f12c";
}

.ion-ios-bonfire-outline:before {
  content: "\f12b";
}

.ion-ios-book:before {
  content: "\f3e8";
}

.ion-ios-book-outline:before {
  content: "\f3e7";
}

.ion-ios-bookmark:before {
  content: "\f12e";
}

.ion-ios-bookmark-outline:before {
  content: "\f12d";
}

.ion-ios-bookmarks:before {
  content: "\f3ea";
}

.ion-ios-bookmarks-outline:before {
  content: "\f3e9";
}

.ion-ios-bowtie:before {
  content: "\f130";
}

.ion-ios-bowtie-outline:before {
  content: "\f12f";
}

.ion-ios-briefcase:before {
  content: "\f3ee";
}

.ion-ios-briefcase-outline:before {
  content: "\f3ed";
}

.ion-ios-browsers:before {
  content: "\f3f0";
}

.ion-ios-browsers-outline:before {
  content: "\f3ef";
}

.ion-ios-brush:before {
  content: "\f132";
}

.ion-ios-brush-outline:before {
  content: "\f131";
}

.ion-ios-bug:before {
  content: "\f134";
}

.ion-ios-bug-outline:before {
  content: "\f133";
}

.ion-ios-build:before {
  content: "\f136";
}

.ion-ios-build-outline:before {
  content: "\f135";
}

.ion-ios-bulb:before {
  content: "\f138";
}

.ion-ios-bulb-outline:before {
  content: "\f137";
}

.ion-ios-bus:before {
  content: "\f13a";
}

.ion-ios-bus-outline:before {
  content: "\f139";
}

.ion-ios-cafe:before {
  content: "\f13c";
}

.ion-ios-cafe-outline:before {
  content: "\f13b";
}

.ion-ios-calculator:before {
  content: "\f3f2";
}

.ion-ios-calculator-outline:before {
  content: "\f3f1";
}

.ion-ios-calendar:before {
  content: "\f3f4";
}

.ion-ios-calendar-outline:before {
  content: "\f3f3";
}

.ion-ios-call:before {
  content: "\f13e";
}

.ion-ios-call-outline:before {
  content: "\f13d";
}

.ion-ios-camera:before {
  content: "\f3f6";
}

.ion-ios-camera-outline:before {
  content: "\f3f5";
}

.ion-ios-car:before {
  content: "\f140";
}

.ion-ios-car-outline:before {
  content: "\f13f";
}

.ion-ios-card:before {
  content: "\f142";
}

.ion-ios-card-outline:before {
  content: "\f141";
}

.ion-ios-cart:before {
  content: "\f3f8";
}

.ion-ios-cart-outline:before {
  content: "\f3f7";
}

.ion-ios-cash:before {
  content: "\f144";
}

.ion-ios-cash-outline:before {
  content: "\f143";
}

.ion-ios-chatboxes:before {
  content: "\f3fa";
}

.ion-ios-chatboxes-outline:before {
  content: "\f3f9";
}

.ion-ios-chatbubbles:before {
  content: "\f146";
}

.ion-ios-chatbubbles-outline:before {
  content: "\f145";
}

.ion-ios-checkbox:before {
  content: "\f148";
}

.ion-ios-checkbox-outline:before {
  content: "\f147";
}

.ion-ios-checkmark:before {
  content: "\f3ff";
}

.ion-ios-checkmark-circle:before {
  content: "\f14a";
}

.ion-ios-checkmark-circle-outline:before {
  content: "\f149";
}

.ion-ios-checkmark-outline:before {
  content: "\f3ff";
}

.ion-ios-clipboard:before {
  content: "\f14c";
}

.ion-ios-clipboard-outline:before {
  content: "\f14b";
}

.ion-ios-clock:before {
  content: "\f403";
}

.ion-ios-clock-outline:before {
  content: "\f402";
}

.ion-ios-close:before {
  content: "\f406";
}

.ion-ios-close-circle:before {
  content: "\f14e";
}

.ion-ios-close-circle-outline:before {
  content: "\f14d";
}

.ion-ios-close-outline:before {
  content: "\f406";
}

.ion-ios-closed-captioning:before {
  content: "\f150";
}

.ion-ios-closed-captioning-outline:before {
  content: "\f14f";
}

.ion-ios-cloud:before {
  content: "\f40c";
}

.ion-ios-cloud-circle:before {
  content: "\f152";
}

.ion-ios-cloud-circle-outline:before {
  content: "\f151";
}

.ion-ios-cloud-done:before {
  content: "\f154";
}

.ion-ios-cloud-done-outline:before {
  content: "\f153";
}

.ion-ios-cloud-download:before {
  content: "\f408";
}

.ion-ios-cloud-download-outline:before {
  content: "\f407";
}

.ion-ios-cloud-outline:before {
  content: "\f409";
}

.ion-ios-cloud-upload:before {
  content: "\f40b";
}

.ion-ios-cloud-upload-outline:before {
  content: "\f40a";
}

.ion-ios-cloudy:before {
  content: "\f410";
}

.ion-ios-cloudy-night:before {
  content: "\f40e";
}

.ion-ios-cloudy-night-outline:before {
  content: "\f40d";
}

.ion-ios-cloudy-outline:before {
  content: "\f40f";
}

.ion-ios-code:before {
  content: "\f157";
}

.ion-ios-code-download:before {
  content: "\f155";
}

.ion-ios-code-download-outline:before {
  content: "\f155";
}

.ion-ios-code-outline:before {
  content: "\f157";
}

.ion-ios-code-working:before {
  content: "\f156";
}

.ion-ios-code-working-outline:before {
  content: "\f156";
}

.ion-ios-cog:before {
  content: "\f412";
}

.ion-ios-cog-outline:before {
  content: "\f411";
}

.ion-ios-color-fill:before {
  content: "\f159";
}

.ion-ios-color-fill-outline:before {
  content: "\f158";
}

.ion-ios-color-filter:before {
  content: "\f414";
}

.ion-ios-color-filter-outline:before {
  content: "\f413";
}

.ion-ios-color-palette:before {
  content: "\f15b";
}

.ion-ios-color-palette-outline:before {
  content: "\f15a";
}

.ion-ios-color-wand:before {
  content: "\f416";
}

.ion-ios-color-wand-outline:before {
  content: "\f415";
}

.ion-ios-compass:before {
  content: "\f15d";
}

.ion-ios-compass-outline:before {
  content: "\f15c";
}

.ion-ios-construct:before {
  content: "\f15f";
}

.ion-ios-construct-outline:before {
  content: "\f15e";
}

.ion-ios-contact:before {
  content: "\f41a";
}

.ion-ios-contact-outline:before {
  content: "\f419";
}

.ion-ios-contacts:before {
  content: "\f161";
}

.ion-ios-contacts-outline:before {
  content: "\f160";
}

.ion-ios-contract:before {
  content: "\f162";
}

.ion-ios-contract-outline:before {
  content: "\f162";
}

.ion-ios-contrast:before {
  content: "\f163";
}

.ion-ios-contrast-outline:before {
  content: "\f163";
}

.ion-ios-copy:before {
  content: "\f41c";
}

.ion-ios-copy-outline:before {
  content: "\f41b";
}

.ion-ios-create:before {
  content: "\f165";
}

.ion-ios-create-outline:before {
  content: "\f164";
}

.ion-ios-crop:before {
  content: "\f41e";
}

.ion-ios-crop-outline:before {
  content: "\f166";
}

.ion-ios-cube:before {
  content: "\f168";
}

.ion-ios-cube-outline:before {
  content: "\f167";
}

.ion-ios-cut:before {
  content: "\f16a";
}

.ion-ios-cut-outline:before {
  content: "\f169";
}

.ion-ios-desktop:before {
  content: "\f16c";
}

.ion-ios-desktop-outline:before {
  content: "\f16b";
}

.ion-ios-disc:before {
  content: "\f16e";
}

.ion-ios-disc-outline:before {
  content: "\f16d";
}

.ion-ios-document:before {
  content: "\f170";
}

.ion-ios-document-outline:before {
  content: "\f16f";
}

.ion-ios-done-all:before {
  content: "\f171";
}

.ion-ios-done-all-outline:before {
  content: "\f171";
}

.ion-ios-download:before {
  content: "\f420";
}

.ion-ios-download-outline:before {
  content: "\f41f";
}

.ion-ios-easel:before {
  content: "\f173";
}

.ion-ios-easel-outline:before {
  content: "\f172";
}

.ion-ios-egg:before {
  content: "\f175";
}

.ion-ios-egg-outline:before {
  content: "\f174";
}

.ion-ios-exit:before {
  content: "\f177";
}

.ion-ios-exit-outline:before {
  content: "\f176";
}

.ion-ios-expand:before {
  content: "\f178";
}

.ion-ios-expand-outline:before {
  content: "\f178";
}

.ion-ios-eye:before {
  content: "\f425";
}

.ion-ios-eye-off:before {
  content: "\f17a";
}

.ion-ios-eye-off-outline:before {
  content: "\f179";
}

.ion-ios-eye-outline:before {
  content: "\f424";
}

.ion-ios-fastforward:before {
  content: "\f427";
}

.ion-ios-fastforward-outline:before {
  content: "\f426";
}

.ion-ios-female:before {
  content: "\f17b";
}

.ion-ios-female-outline:before {
  content: "\f17b";
}

.ion-ios-filing:before {
  content: "\f429";
}

.ion-ios-filing-outline:before {
  content: "\f428";
}

.ion-ios-film:before {
  content: "\f42b";
}

.ion-ios-film-outline:before {
  content: "\f42a";
}

.ion-ios-finger-print:before {
  content: "\f17c";
}

.ion-ios-finger-print-outline:before {
  content: "\f17c";
}

.ion-ios-flag:before {
  content: "\f42d";
}

.ion-ios-flag-outline:before {
  content: "\f42c";
}

.ion-ios-flame:before {
  content: "\f42f";
}

.ion-ios-flame-outline:before {
  content: "\f42e";
}

.ion-ios-flash:before {
  content: "\f17e";
}

.ion-ios-flash-outline:before {
  content: "\f17d";
}

.ion-ios-flask:before {
  content: "\f431";
}

.ion-ios-flask-outline:before {
  content: "\f430";
}

.ion-ios-flower:before {
  content: "\f433";
}

.ion-ios-flower-outline:before {
  content: "\f432";
}

.ion-ios-folder:before {
  content: "\f435";
}

.ion-ios-folder-open:before {
  content: "\f180";
}

.ion-ios-folder-open-outline:before {
  content: "\f17f";
}

.ion-ios-folder-outline:before {
  content: "\f434";
}

.ion-ios-football:before {
  content: "\f437";
}

.ion-ios-football-outline:before {
  content: "\f436";
}

.ion-ios-funnel:before {
  content: "\f182";
}

.ion-ios-funnel-outline:before {
  content: "\f181";
}

.ion-ios-game-controller-a:before {
  content: "\f439";
}

.ion-ios-game-controller-a-outline:before {
  content: "\f438";
}

.ion-ios-game-controller-b:before {
  content: "\f43b";
}

.ion-ios-game-controller-b-outline:before {
  content: "\f43a";
}

.ion-ios-git-branch:before {
  content: "\f183";
}

.ion-ios-git-branch-outline:before {
  content: "\f183";
}

.ion-ios-git-commit:before {
  content: "\f184";
}

.ion-ios-git-commit-outline:before {
  content: "\f184";
}

.ion-ios-git-compare:before {
  content: "\f185";
}

.ion-ios-git-compare-outline:before {
  content: "\f185";
}

.ion-ios-git-merge:before {
  content: "\f186";
}

.ion-ios-git-merge-outline:before {
  content: "\f186";
}

.ion-ios-git-network:before {
  content: "\f187";
}

.ion-ios-git-network-outline:before {
  content: "\f187";
}

.ion-ios-git-pull-request:before {
  content: "\f188";
}

.ion-ios-git-pull-request-outline:before {
  content: "\f188";
}

.ion-ios-glasses:before {
  content: "\f43f";
}

.ion-ios-glasses-outline:before {
  content: "\f43e";
}

.ion-ios-globe:before {
  content: "\f18a";
}

.ion-ios-globe-outline:before {
  content: "\f189";
}

.ion-ios-grid:before {
  content: "\f18c";
}

.ion-ios-grid-outline:before {
  content: "\f18b";
}

.ion-ios-hammer:before {
  content: "\f18e";
}

.ion-ios-hammer-outline:before {
  content: "\f18d";
}

.ion-ios-hand:before {
  content: "\f190";
}

.ion-ios-hand-outline:before {
  content: "\f18f";
}

.ion-ios-happy:before {
  content: "\f192";
}

.ion-ios-happy-outline:before {
  content: "\f191";
}

.ion-ios-headset:before {
  content: "\f194";
}

.ion-ios-headset-outline:before {
  content: "\f193";
}

.ion-ios-heart:before {
  content: "\f443";
}

.ion-ios-heart-outline:before {
  content: "\f442";
}

.ion-ios-help:before {
  content: "\f446";
}

.ion-ios-help-buoy:before {
  content: "\f196";
}

.ion-ios-help-buoy-outline:before {
  content: "\f195";
}

.ion-ios-help-circle:before {
  content: "\f198";
}

.ion-ios-help-circle-outline:before {
  content: "\f197";
}

.ion-ios-help-outline:before {
  content: "\f446";
}

.ion-ios-home:before {
  content: "\f448";
}

.ion-ios-home-outline:before {
  content: "\f447";
}

.ion-ios-ice-cream:before {
  content: "\f19a";
}

.ion-ios-ice-cream-outline:before {
  content: "\f199";
}

.ion-ios-image:before {
  content: "\f19c";
}

.ion-ios-image-outline:before {
  content: "\f19b";
}

.ion-ios-images:before {
  content: "\f19e";
}

.ion-ios-images-outline:before {
  content: "\f19d";
}

.ion-ios-infinite:before {
  content: "\f44a";
}

.ion-ios-infinite-outline:before {
  content: "\f449";
}

.ion-ios-information:before {
  content: "\f44d";
}

.ion-ios-information-circle:before {
  content: "\f1a0";
}

.ion-ios-information-circle-outline:before {
  content: "\f19f";
}

.ion-ios-information-outline:before {
  content: "\f44d";
}

.ion-ios-ionic:before {
  content: "\f1a1";
}

.ion-ios-ionic-outline:before {
  content: "\f44e";
}

.ion-ios-ionitron:before {
  content: "\f1a3";
}

.ion-ios-ionitron-outline:before {
  content: "\f1a2";
}

.ion-ios-jet:before {
  content: "\f1a5";
}

.ion-ios-jet-outline:before {
  content: "\f1a4";
}

.ion-ios-key:before {
  content: "\f1a7";
}

.ion-ios-key-outline:before {
  content: "\f1a6";
}

.ion-ios-keypad:before {
  content: "\f450";
}

.ion-ios-keypad-outline:before {
  content: "\f44f";
}

.ion-ios-laptop:before {
  content: "\f1a8";
}

.ion-ios-laptop-outline:before {
  content: "\f1a8";
}

.ion-ios-leaf:before {
  content: "\f1aa";
}

.ion-ios-leaf-outline:before {
  content: "\f1a9";
}

.ion-ios-link:before {
  content: "\f22a";
}

.ion-ios-link-outline:before {
  content: "\f1ca";
}

.ion-ios-list:before {
  content: "\f454";
}

.ion-ios-list-box:before {
  content: "\f1ac";
}

.ion-ios-list-box-outline:before {
  content: "\f1ab";
}

.ion-ios-list-outline:before {
  content: "\f454";
}

.ion-ios-locate:before {
  content: "\f1ae";
}

.ion-ios-locate-outline:before {
  content: "\f1ad";
}

.ion-ios-lock:before {
  content: "\f1b0";
}

.ion-ios-lock-outline:before {
  content: "\f1af";
}

.ion-ios-log-in:before {
  content: "\f1b1";
}

.ion-ios-log-in-outline:before {
  content: "\f1b1";
}

.ion-ios-log-out:before {
  content: "\f1b2";
}

.ion-ios-log-out-outline:before {
  content: "\f1b2";
}

.ion-ios-magnet:before {
  content: "\f1b4";
}

.ion-ios-magnet-outline:before {
  content: "\f1b3";
}

.ion-ios-mail:before {
  content: "\f1b8";
}

.ion-ios-mail-open:before {
  content: "\f1b6";
}

.ion-ios-mail-open-outline:before {
  content: "\f1b5";
}

.ion-ios-mail-outline:before {
  content: "\f1b7";
}

.ion-ios-male:before {
  content: "\f1b9";
}

.ion-ios-male-outline:before {
  content: "\f1b9";
}

.ion-ios-man:before {
  content: "\f1bb";
}

.ion-ios-man-outline:before {
  content: "\f1ba";
}

.ion-ios-map:before {
  content: "\f1bd";
}

.ion-ios-map-outline:before {
  content: "\f1bc";
}

.ion-ios-medal:before {
  content: "\f1bf";
}

.ion-ios-medal-outline:before {
  content: "\f1be";
}

.ion-ios-medical:before {
  content: "\f45c";
}

.ion-ios-medical-outline:before {
  content: "\f45b";
}

.ion-ios-medkit:before {
  content: "\f45e";
}

.ion-ios-medkit-outline:before {
  content: "\f45d";
}

.ion-ios-megaphone:before {
  content: "\f1c1";
}

.ion-ios-megaphone-outline:before {
  content: "\f1c0";
}

.ion-ios-menu:before {
  content: "\f1c3";
}

.ion-ios-menu-outline:before {
  content: "\f1c2";
}

.ion-ios-mic:before {
  content: "\f461";
}

.ion-ios-mic-off:before {
  content: "\f45f";
}

.ion-ios-mic-off-outline:before {
  content: "\f1c4";
}

.ion-ios-mic-outline:before {
  content: "\f460";
}

.ion-ios-microphone:before {
  content: "\f1c6";
}

.ion-ios-microphone-outline:before {
  content: "\f1c5";
}

.ion-ios-moon:before {
  content: "\f468";
}

.ion-ios-moon-outline:before {
  content: "\f467";
}

.ion-ios-more:before {
  content: "\f1c8";
}

.ion-ios-more-outline:before {
  content: "\f1c7";
}

.ion-ios-move:before {
  content: "\f1cb";
}

.ion-ios-move-outline:before {
  content: "\f1cb";
}

.ion-ios-musical-note:before {
  content: "\f46b";
}

.ion-ios-musical-note-outline:before {
  content: "\f1cc";
}

.ion-ios-musical-notes:before {
  content: "\f46c";
}

.ion-ios-musical-notes-outline:before {
  content: "\f1cd";
}

.ion-ios-navigate:before {
  content: "\f46e";
}

.ion-ios-navigate-outline:before {
  content: "\f46d";
}

.ion-ios-no-smoking:before {
  content: "\f1cf";
}

.ion-ios-no-smoking-outline:before {
  content: "\f1ce";
}

.ion-ios-notifications:before {
  content: "\f1d3";
}

.ion-ios-notifications-off:before {
  content: "\f1d1";
}

.ion-ios-notifications-off-outline:before {
  content: "\f1d0";
}

.ion-ios-notifications-outline:before {
  content: "\f1d2";
}

.ion-ios-nuclear:before {
  content: "\f1d5";
}

.ion-ios-nuclear-outline:before {
  content: "\f1d4";
}

.ion-ios-nutrition:before {
  content: "\f470";
}

.ion-ios-nutrition-outline:before {
  content: "\f46f";
}

.ion-ios-open:before {
  content: "\f1d7";
}

.ion-ios-open-outline:before {
  content: "\f1d6";
}

.ion-ios-options:before {
  content: "\f1d9";
}

.ion-ios-options-outline:before {
  content: "\f1d8";
}

.ion-ios-outlet:before {
  content: "\f1db";
}

.ion-ios-outlet-outline:before {
  content: "\f1da";
}

.ion-ios-paper:before {
  content: "\f472";
}

.ion-ios-paper-outline:before {
  content: "\f471";
}

.ion-ios-paper-plane:before {
  content: "\f1dd";
}

.ion-ios-paper-plane-outline:before {
  content: "\f1dc";
}

.ion-ios-partly-sunny:before {
  content: "\f1df";
}

.ion-ios-partly-sunny-outline:before {
  content: "\f1de";
}

.ion-ios-pause:before {
  content: "\f478";
}

.ion-ios-pause-outline:before {
  content: "\f477";
}

.ion-ios-paw:before {
  content: "\f47a";
}

.ion-ios-paw-outline:before {
  content: "\f479";
}

.ion-ios-people:before {
  content: "\f47c";
}

.ion-ios-people-outline:before {
  content: "\f47b";
}

.ion-ios-person:before {
  content: "\f47e";
}

.ion-ios-person-add:before {
  content: "\f1e1";
}

.ion-ios-person-add-outline:before {
  content: "\f1e0";
}

.ion-ios-person-outline:before {
  content: "\f47d";
}

.ion-ios-phone-landscape:before {
  content: "\f1e2";
}

.ion-ios-phone-landscape-outline:before {
  content: "\f1e2";
}

.ion-ios-phone-portrait:before {
  content: "\f1e3";
}

.ion-ios-phone-portrait-outline:before {
  content: "\f1e3";
}

.ion-ios-photos:before {
  content: "\f482";
}

.ion-ios-photos-outline:before {
  content: "\f481";
}

.ion-ios-pie:before {
  content: "\f484";
}

.ion-ios-pie-outline:before {
  content: "\f483";
}

.ion-ios-pin:before {
  content: "\f1e5";
}

.ion-ios-pin-outline:before {
  content: "\f1e4";
}

.ion-ios-pint:before {
  content: "\f486";
}

.ion-ios-pint-outline:before {
  content: "\f485";
}

.ion-ios-pizza:before {
  content: "\f1e7";
}

.ion-ios-pizza-outline:before {
  content: "\f1e6";
}

.ion-ios-plane:before {
  content: "\f1e9";
}

.ion-ios-plane-outline:before {
  content: "\f1e8";
}

.ion-ios-planet:before {
  content: "\f1eb";
}

.ion-ios-planet-outline:before {
  content: "\f1ea";
}

.ion-ios-play:before {
  content: "\f488";
}

.ion-ios-play-outline:before {
  content: "\f487";
}

.ion-ios-podium:before {
  content: "\f1ed";
}

.ion-ios-podium-outline:before {
  content: "\f1ec";
}

.ion-ios-power:before {
  content: "\f1ef";
}

.ion-ios-power-outline:before {
  content: "\f1ee";
}

.ion-ios-pricetag:before {
  content: "\f48d";
}

.ion-ios-pricetag-outline:before {
  content: "\f48c";
}

.ion-ios-pricetags:before {
  content: "\f48f";
}

.ion-ios-pricetags-outline:before {
  content: "\f48e";
}

.ion-ios-print:before {
  content: "\f1f1";
}

.ion-ios-print-outline:before {
  content: "\f1f0";
}

.ion-ios-pulse:before {
  content: "\f493";
}

.ion-ios-pulse-outline:before {
  content: "\f1f2";
}

.ion-ios-qr-scanner:before {
  content: "\f1f3";
}

.ion-ios-qr-scanner-outline:before {
  content: "\f1f3";
}

.ion-ios-quote:before {
  content: "\f1f5";
}

.ion-ios-quote-outline:before {
  content: "\f1f4";
}

.ion-ios-radio:before {
  content: "\f1f9";
}

.ion-ios-radio-button-off:before {
  content: "\f1f6";
}

.ion-ios-radio-button-off-outline:before {
  content: "\f1f6";
}

.ion-ios-radio-button-on:before {
  content: "\f1f7";
}

.ion-ios-radio-button-on-outline:before {
  content: "\f1f7";
}

.ion-ios-radio-outline:before {
  content: "\f1f8";
}

.ion-ios-rainy:before {
  content: "\f495";
}

.ion-ios-rainy-outline:before {
  content: "\f494";
}

.ion-ios-recording:before {
  content: "\f497";
}

.ion-ios-recording-outline:before {
  content: "\f496";
}

.ion-ios-redo:before {
  content: "\f499";
}

.ion-ios-redo-outline:before {
  content: "\f498";
}

.ion-ios-refresh:before {
  content: "\f49c";
}

.ion-ios-refresh-circle:before {
  content: "\f226";
}

.ion-ios-refresh-circle-outline:before {
  content: "\f224";
}

.ion-ios-refresh-outline:before {
  content: "\f49c";
}

.ion-ios-remove:before {
  content: "\f1fc";
}

.ion-ios-remove-circle:before {
  content: "\f1fb";
}

.ion-ios-remove-circle-outline:before {
  content: "\f1fa";
}

.ion-ios-remove-outline:before {
  content: "\f1fc";
}

.ion-ios-reorder:before {
  content: "\f1fd";
}

.ion-ios-reorder-outline:before {
  content: "\f1fd";
}

.ion-ios-repeat:before {
  content: "\f1fe";
}

.ion-ios-repeat-outline:before {
  content: "\f1fe";
}

.ion-ios-resize:before {
  content: "\f1ff";
}

.ion-ios-resize-outline:before {
  content: "\f1ff";
}

.ion-ios-restaurant:before {
  content: "\f201";
}

.ion-ios-restaurant-outline:before {
  content: "\f200";
}

.ion-ios-return-left:before {
  content: "\f202";
}

.ion-ios-return-left-outline:before {
  content: "\f202";
}

.ion-ios-return-right:before {
  content: "\f203";
}

.ion-ios-return-right-outline:before {
  content: "\f203";
}

.ion-ios-reverse-camera:before {
  content: "\f49f";
}

.ion-ios-reverse-camera-outline:before {
  content: "\f49e";
}

.ion-ios-rewind:before {
  content: "\f4a1";
}

.ion-ios-rewind-outline:before {
  content: "\f4a0";
}

.ion-ios-ribbon:before {
  content: "\f205";
}

.ion-ios-ribbon-outline:before {
  content: "\f204";
}

.ion-ios-rose:before {
  content: "\f4a3";
}

.ion-ios-rose-outline:before {
  content: "\f4a2";
}

.ion-ios-sad:before {
  content: "\f207";
}

.ion-ios-sad-outline:before {
  content: "\f206";
}

.ion-ios-school:before {
  content: "\f209";
}

.ion-ios-school-outline:before {
  content: "\f208";
}

.ion-ios-search:before {
  content: "\f4a5";
}

.ion-ios-search-outline:before {
  content: "\f20a";
}

.ion-ios-send:before {
  content: "\f20c";
}

.ion-ios-send-outline:before {
  content: "\f20b";
}

.ion-ios-settings:before {
  content: "\f4a7";
}

.ion-ios-settings-outline:before {
  content: "\f20d";
}

.ion-ios-share:before {
  content: "\f211";
}

.ion-ios-share-alt:before {
  content: "\f20f";
}

.ion-ios-share-alt-outline:before {
  content: "\f20e";
}

.ion-ios-share-outline:before {
  content: "\f210";
}

.ion-ios-shirt:before {
  content: "\f213";
}

.ion-ios-shirt-outline:before {
  content: "\f212";
}

.ion-ios-shuffle:before {
  content: "\f4a9";
}

.ion-ios-shuffle-outline:before {
  content: "\f4a9";
}

.ion-ios-skip-backward:before {
  content: "\f215";
}

.ion-ios-skip-backward-outline:before {
  content: "\f214";
}

.ion-ios-skip-forward:before {
  content: "\f217";
}

.ion-ios-skip-forward-outline:before {
  content: "\f216";
}

.ion-ios-snow:before {
  content: "\f218";
}

.ion-ios-snow-outline:before {
  content: "\f22c";
}

.ion-ios-speedometer:before {
  content: "\f4b0";
}

.ion-ios-speedometer-outline:before {
  content: "\f4af";
}

.ion-ios-square:before {
  content: "\f21a";
}

.ion-ios-square-outline:before {
  content: "\f219";
}

.ion-ios-star:before {
  content: "\f4b3";
}

.ion-ios-star-half:before {
  content: "\f4b1";
}

.ion-ios-star-half-outline:before {
  content: "\f4b1";
}

.ion-ios-star-outline:before {
  content: "\f4b2";
}

.ion-ios-stats:before {
  content: "\f21c";
}

.ion-ios-stats-outline:before {
  content: "\f21b";
}

.ion-ios-stopwatch:before {
  content: "\f4b5";
}

.ion-ios-stopwatch-outline:before {
  content: "\f4b4";
}

.ion-ios-subway:before {
  content: "\f21e";
}

.ion-ios-subway-outline:before {
  content: "\f21d";
}

.ion-ios-sunny:before {
  content: "\f4b7";
}

.ion-ios-sunny-outline:before {
  content: "\f4b6";
}

.ion-ios-swap:before {
  content: "\f21f";
}

.ion-ios-swap-outline:before {
  content: "\f21f";
}

.ion-ios-switch:before {
  content: "\f221";
}

.ion-ios-switch-outline:before {
  content: "\f220";
}

.ion-ios-sync:before {
  content: "\f222";
}

.ion-ios-sync-outline:before {
  content: "\f222";
}

.ion-ios-tablet-landscape:before {
  content: "\f223";
}

.ion-ios-tablet-landscape-outline:before {
  content: "\f223";
}

.ion-ios-tablet-portrait:before {
  content: "\f24e";
}

.ion-ios-tablet-portrait-outline:before {
  content: "\f24e";
}

.ion-ios-tennisball:before {
  content: "\f4bb";
}

.ion-ios-tennisball-outline:before {
  content: "\f4ba";
}

.ion-ios-text:before {
  content: "\f250";
}

.ion-ios-text-outline:before {
  content: "\f24f";
}

.ion-ios-thermometer:before {
  content: "\f252";
}

.ion-ios-thermometer-outline:before {
  content: "\f251";
}

.ion-ios-thumbs-down:before {
  content: "\f254";
}

.ion-ios-thumbs-down-outline:before {
  content: "\f253";
}

.ion-ios-thumbs-up:before {
  content: "\f256";
}

.ion-ios-thumbs-up-outline:before {
  content: "\f255";
}

.ion-ios-thunderstorm:before {
  content: "\f4bd";
}

.ion-ios-thunderstorm-outline:before {
  content: "\f4bc";
}

.ion-ios-time:before {
  content: "\f4bf";
}

.ion-ios-time-outline:before {
  content: "\f4be";
}

.ion-ios-timer:before {
  content: "\f4c1";
}

.ion-ios-timer-outline:before {
  content: "\f4c0";
}

.ion-ios-train:before {
  content: "\f258";
}

.ion-ios-train-outline:before {
  content: "\f257";
}

.ion-ios-transgender:before {
  content: "\f259";
}

.ion-ios-transgender-outline:before {
  content: "\f259";
}

.ion-ios-trash:before {
  content: "\f4c5";
}

.ion-ios-trash-outline:before {
  content: "\f4c4";
}

.ion-ios-trending-down:before {
  content: "\f25a";
}

.ion-ios-trending-down-outline:before {
  content: "\f25a";
}

.ion-ios-trending-up:before {
  content: "\f25b";
}

.ion-ios-trending-up-outline:before {
  content: "\f25b";
}

.ion-ios-trophy:before {
  content: "\f25d";
}

.ion-ios-trophy-outline:before {
  content: "\f25c";
}

.ion-ios-umbrella:before {
  content: "\f25f";
}

.ion-ios-umbrella-outline:before {
  content: "\f25e";
}

.ion-ios-undo:before {
  content: "\f4c7";
}

.ion-ios-undo-outline:before {
  content: "\f4c6";
}

.ion-ios-unlock:before {
  content: "\f261";
}

.ion-ios-unlock-outline:before {
  content: "\f260";
}

.ion-ios-videocam:before {
  content: "\f4cd";
}

.ion-ios-videocam-outline:before {
  content: "\f4cc";
}

.ion-ios-volume-down:before {
  content: "\f262";
}

.ion-ios-volume-down-outline:before {
  content: "\f262";
}

.ion-ios-volume-mute:before {
  content: "\f263";
}

.ion-ios-volume-mute-outline:before {
  content: "\f263";
}

.ion-ios-volume-off:before {
  content: "\f264";
}

.ion-ios-volume-off-outline:before {
  content: "\f264";
}

.ion-ios-volume-up:before {
  content: "\f265";
}

.ion-ios-volume-up-outline:before {
  content: "\f265";
}

.ion-ios-walk:before {
  content: "\f266";
}

.ion-ios-walk-outline:before {
  content: "\f266";
}

.ion-ios-warning:before {
  content: "\f268";
}

.ion-ios-warning-outline:before {
  content: "\f267";
}

.ion-ios-watch:before {
  content: "\f269";
}

.ion-ios-watch-outline:before {
  content: "\f269";
}

.ion-ios-water:before {
  content: "\f26b";
}

.ion-ios-water-outline:before {
  content: "\f26a";
}

.ion-ios-wifi:before {
  content: "\f26d";
}

.ion-ios-wifi-outline:before {
  content: "\f26c";
}

.ion-ios-wine:before {
  content: "\f26f";
}

.ion-ios-wine-outline:before {
  content: "\f26e";
}

.ion-ios-woman:before {
  content: "\f271";
}

.ion-ios-woman-outline:before {
  content: "\f270";
}

.ion-logo-android:before {
  content: "\f225";
}

.ion-logo-angular:before {
  content: "\f227";
}

.ion-logo-apple:before {
  content: "\f229";
}

.ion-logo-bitcoin:before {
  content: "\f22b";
}

.ion-logo-buffer:before {
  content: "\f22d";
}

.ion-logo-chrome:before {
  content: "\f22f";
}

.ion-logo-codepen:before {
  content: "\f230";
}

.ion-logo-css3:before {
  content: "\f231";
}

.ion-logo-designernews:before {
  content: "\f232";
}

.ion-logo-dribbble:before {
  content: "\f233";
}

.ion-logo-dropbox:before {
  content: "\f234";
}

.ion-logo-euro:before {
  content: "\f235";
}

.ion-logo-facebook:before {
  content: "\f236";
}

.ion-logo-foursquare:before {
  content: "\f237";
}

.ion-logo-freebsd-devil:before {
  content: "\f238";
}

.ion-logo-github:before {
  content: "\f239";
}

.ion-logo-google:before {
  content: "\f23a";
}

.ion-logo-googleplus:before {
  content: "\f23b";
}

.ion-logo-hackernews:before {
  content: "\f23c";
}

.ion-logo-html5:before {
  content: "\f23d";
}

.ion-logo-instagram:before {
  content: "\f23e";
}

.ion-logo-javascript:before {
  content: "\f23f";
}

.ion-logo-linkedin:before {
  content: "\f240";
}

.ion-logo-markdown:before {
  content: "\f241";
}

.ion-logo-nodejs:before {
  content: "\f242";
}

.ion-logo-octocat:before {
  content: "\f243";
}

.ion-logo-pinterest:before {
  content: "\f244";
}

.ion-logo-playstation:before {
  content: "\f245";
}

.ion-logo-python:before {
  content: "\f246";
}

.ion-logo-reddit:before {
  content: "\f247";
}

.ion-logo-rss:before {
  content: "\f248";
}

.ion-logo-sass:before {
  content: "\f249";
}

.ion-logo-skype:before {
  content: "\f24a";
}

.ion-logo-snapchat:before {
  content: "\f24b";
}

.ion-logo-steam:before {
  content: "\f24c";
}

.ion-logo-tumblr:before {
  content: "\f24d";
}

.ion-logo-tux:before {
  content: "\f2ae";
}

.ion-logo-twitch:before {
  content: "\f2af";
}

.ion-logo-twitter:before {
  content: "\f2b0";
}

.ion-logo-usd:before {
  content: "\f2b1";
}

.ion-logo-vimeo:before {
  content: "\f2c4";
}

.ion-logo-whatsapp:before {
  content: "\f2c5";
}

.ion-logo-windows:before {
  content: "\f32f";
}

.ion-logo-wordpress:before {
  content: "\f330";
}

.ion-logo-xbox:before {
  content: "\f34c";
}

.ion-logo-yahoo:before {
  content: "\f34d";
}

.ion-logo-yen:before {
  content: "\f34e";
}

.ion-logo-youtube:before {
  content: "\f34f";
}

.ion-md-add:before {
  content: "\f273";
}

.ion-md-add-circle:before {
  content: "\f272";
}

.ion-md-alarm:before {
  content: "\f274";
}

.ion-md-albums:before {
  content: "\f275";
}

.ion-md-alert:before {
  content: "\f276";
}

.ion-md-american-football:before {
  content: "\f277";
}

.ion-md-analytics:before {
  content: "\f278";
}

.ion-md-aperture:before {
  content: "\f279";
}

.ion-md-apps:before {
  content: "\f27a";
}

.ion-md-appstore:before {
  content: "\f27b";
}

.ion-md-archive:before {
  content: "\f27c";
}

.ion-md-arrow-back:before {
  content: "\f27d";
}

.ion-md-arrow-down:before {
  content: "\f27e";
}

.ion-md-arrow-dropdown:before {
  content: "\f280";
}

.ion-md-arrow-dropdown-circle:before {
  content: "\f27f";
}

.ion-md-arrow-dropleft:before {
  content: "\f282";
}

.ion-md-arrow-dropleft-circle:before {
  content: "\f281";
}

.ion-md-arrow-dropright:before {
  content: "\f284";
}

.ion-md-arrow-dropright-circle:before {
  content: "\f283";
}

.ion-md-arrow-dropup:before {
  content: "\f286";
}

.ion-md-arrow-dropup-circle:before {
  content: "\f285";
}

.ion-md-arrow-forward:before {
  content: "\f287";
}

.ion-md-arrow-round-back:before {
  content: "\f288";
}

.ion-md-arrow-round-down:before {
  content: "\f289";
}

.ion-md-arrow-round-forward:before {
  content: "\f28a";
}

.ion-md-arrow-round-up:before {
  content: "\f28b";
}

.ion-md-arrow-up:before {
  content: "\f28c";
}

.ion-md-at:before {
  content: "\f28d";
}

.ion-md-attach:before {
  content: "\f28e";
}

.ion-md-backspace:before {
  content: "\f28f";
}

.ion-md-barcode:before {
  content: "\f290";
}

.ion-md-baseball:before {
  content: "\f291";
}

.ion-md-basket:before {
  content: "\f292";
}

.ion-md-basketball:before {
  content: "\f293";
}

.ion-md-battery-charging:before {
  content: "\f294";
}

.ion-md-battery-dead:before {
  content: "\f295";
}

.ion-md-battery-full:before {
  content: "\f296";
}

.ion-md-beaker:before {
  content: "\f297";
}

.ion-md-beer:before {
  content: "\f298";
}

.ion-md-bicycle:before {
  content: "\f299";
}

.ion-md-bluetooth:before {
  content: "\f29a";
}

.ion-md-boat:before {
  content: "\f29b";
}

.ion-md-body:before {
  content: "\f29c";
}

.ion-md-bonfire:before {
  content: "\f29d";
}

.ion-md-book:before {
  content: "\f29e";
}

.ion-md-bookmark:before {
  content: "\f29f";
}

.ion-md-bookmarks:before {
  content: "\f2a0";
}

.ion-md-bowtie:before {
  content: "\f2a1";
}

.ion-md-briefcase:before {
  content: "\f2a2";
}

.ion-md-browsers:before {
  content: "\f2a3";
}

.ion-md-brush:before {
  content: "\f2a4";
}

.ion-md-bug:before {
  content: "\f2a5";
}

.ion-md-build:before {
  content: "\f2a6";
}

.ion-md-bulb:before {
  content: "\f2a7";
}

.ion-md-bus:before {
  content: "\f2a8";
}

.ion-md-cafe:before {
  content: "\f2a9";
}

.ion-md-calculator:before {
  content: "\f2aa";
}

.ion-md-calendar:before {
  content: "\f2ab";
}

.ion-md-call:before {
  content: "\f2ac";
}

.ion-md-camera:before {
  content: "\f2ad";
}

.ion-md-car:before {
  content: "\f2b2";
}

.ion-md-card:before {
  content: "\f2b3";
}

.ion-md-cart:before {
  content: "\f2b4";
}

.ion-md-cash:before {
  content: "\f2b5";
}

.ion-md-chatboxes:before {
  content: "\f2b6";
}

.ion-md-chatbubbles:before {
  content: "\f2b7";
}

.ion-md-checkbox:before {
  content: "\f2b9";
}

.ion-md-checkbox-outline:before {
  content: "\f2b8";
}

.ion-md-checkmark:before {
  content: "\f2bc";
}

.ion-md-checkmark-circle:before {
  content: "\f2bb";
}

.ion-md-checkmark-circle-outline:before {
  content: "\f2ba";
}

.ion-md-clipboard:before {
  content: "\f2bd";
}

.ion-md-clock:before {
  content: "\f2be";
}

.ion-md-close:before {
  content: "\f2c0";
}

.ion-md-close-circle:before {
  content: "\f2bf";
}

.ion-md-closed-captioning:before {
  content: "\f2c1";
}

.ion-md-cloud:before {
  content: "\f2c9";
}

.ion-md-cloud-circle:before {
  content: "\f2c2";
}

.ion-md-cloud-done:before {
  content: "\f2c3";
}

.ion-md-cloud-download:before {
  content: "\f2c6";
}

.ion-md-cloud-outline:before {
  content: "\f2c7";
}

.ion-md-cloud-upload:before {
  content: "\f2c8";
}

.ion-md-cloudy:before {
  content: "\f2cb";
}

.ion-md-cloudy-night:before {
  content: "\f2ca";
}

.ion-md-code:before {
  content: "\f2ce";
}

.ion-md-code-download:before {
  content: "\f2cc";
}

.ion-md-code-working:before {
  content: "\f2cd";
}

.ion-md-cog:before {
  content: "\f2cf";
}

.ion-md-color-fill:before {
  content: "\f2d0";
}

.ion-md-color-filter:before {
  content: "\f2d1";
}

.ion-md-color-palette:before {
  content: "\f2d2";
}

.ion-md-color-wand:before {
  content: "\f2d3";
}

.ion-md-compass:before {
  content: "\f2d4";
}

.ion-md-construct:before {
  content: "\f2d5";
}

.ion-md-contact:before {
  content: "\f2d6";
}

.ion-md-contacts:before {
  content: "\f2d7";
}

.ion-md-contract:before {
  content: "\f2d8";
}

.ion-md-contrast:before {
  content: "\f2d9";
}

.ion-md-copy:before {
  content: "\f2da";
}

.ion-md-create:before {
  content: "\f2db";
}

.ion-md-crop:before {
  content: "\f2dc";
}

.ion-md-cube:before {
  content: "\f2dd";
}

.ion-md-cut:before {
  content: "\f2de";
}

.ion-md-desktop:before {
  content: "\f2df";
}

.ion-md-disc:before {
  content: "\f2e0";
}

.ion-md-document:before {
  content: "\f2e1";
}

.ion-md-done-all:before {
  content: "\f2e2";
}

.ion-md-download:before {
  content: "\f2e3";
}

.ion-md-easel:before {
  content: "\f2e4";
}

.ion-md-egg:before {
  content: "\f2e5";
}

.ion-md-exit:before {
  content: "\f2e6";
}

.ion-md-expand:before {
  content: "\f2e7";
}

.ion-md-eye:before {
  content: "\f2e9";
}

.ion-md-eye-off:before {
  content: "\f2e8";
}

.ion-md-fastforward:before {
  content: "\f2ea";
}

.ion-md-female:before {
  content: "\f2eb";
}

.ion-md-filing:before {
  content: "\f2ec";
}

.ion-md-film:before {
  content: "\f2ed";
}

.ion-md-finger-print:before {
  content: "\f2ee";
}

.ion-md-flag:before {
  content: "\f2ef";
}

.ion-md-flame:before {
  content: "\f2f0";
}

.ion-md-flash:before {
  content: "\f2f1";
}

.ion-md-flask:before {
  content: "\f2f2";
}

.ion-md-flower:before {
  content: "\f2f3";
}

.ion-md-folder:before {
  content: "\f2f5";
}

.ion-md-folder-open:before {
  content: "\f2f4";
}

.ion-md-football:before {
  content: "\f2f6";
}

.ion-md-funnel:before {
  content: "\f2f7";
}

.ion-md-game-controller-a:before {
  content: "\f2f8";
}

.ion-md-game-controller-b:before {
  content: "\f2f9";
}

.ion-md-git-branch:before {
  content: "\f2fa";
}

.ion-md-git-commit:before {
  content: "\f2fb";
}

.ion-md-git-compare:before {
  content: "\f2fc";
}

.ion-md-git-merge:before {
  content: "\f2fd";
}

.ion-md-git-network:before {
  content: "\f2fe";
}

.ion-md-git-pull-request:before {
  content: "\f2ff";
}

.ion-md-glasses:before {
  content: "\f300";
}

.ion-md-globe:before {
  content: "\f301";
}

.ion-md-grid:before {
  content: "\f302";
}

.ion-md-hammer:before {
  content: "\f303";
}

.ion-md-hand:before {
  content: "\f304";
}

.ion-md-happy:before {
  content: "\f305";
}

.ion-md-headset:before {
  content: "\f306";
}

.ion-md-heart:before {
  content: "\f308";
}

.ion-md-heart-outline:before {
  content: "\f307";
}

.ion-md-help:before {
  content: "\f30b";
}

.ion-md-help-buoy:before {
  content: "\f309";
}

.ion-md-help-circle:before {
  content: "\f30a";
}

.ion-md-home:before {
  content: "\f30c";
}

.ion-md-ice-cream:before {
  content: "\f30d";
}

.ion-md-image:before {
  content: "\f30e";
}

.ion-md-images:before {
  content: "\f30f";
}

.ion-md-infinite:before {
  content: "\f310";
}

.ion-md-information:before {
  content: "\f312";
}

.ion-md-information-circle:before {
  content: "\f311";
}

.ion-md-ionic:before {
  content: "\f313";
}

.ion-md-ionitron:before {
  content: "\f314";
}

.ion-md-jet:before {
  content: "\f315";
}

.ion-md-key:before {
  content: "\f316";
}

.ion-md-keypad:before {
  content: "\f317";
}

.ion-md-laptop:before {
  content: "\f318";
}

.ion-md-leaf:before {
  content: "\f319";
}

.ion-md-link:before {
  content: "\f22e";
}

.ion-md-list:before {
  content: "\f31b";
}

.ion-md-list-box:before {
  content: "\f31a";
}

.ion-md-locate:before {
  content: "\f31c";
}

.ion-md-lock:before {
  content: "\f31d";
}

.ion-md-log-in:before {
  content: "\f31e";
}

.ion-md-log-out:before {
  content: "\f31f";
}

.ion-md-magnet:before {
  content: "\f320";
}

.ion-md-mail:before {
  content: "\f322";
}

.ion-md-mail-open:before {
  content: "\f321";
}

.ion-md-male:before {
  content: "\f323";
}

.ion-md-man:before {
  content: "\f324";
}

.ion-md-map:before {
  content: "\f325";
}

.ion-md-medal:before {
  content: "\f326";
}

.ion-md-medical:before {
  content: "\f327";
}

.ion-md-medkit:before {
  content: "\f328";
}

.ion-md-megaphone:before {
  content: "\f329";
}

.ion-md-menu:before {
  content: "\f32a";
}

.ion-md-mic:before {
  content: "\f32c";
}

.ion-md-mic-off:before {
  content: "\f32b";
}

.ion-md-microphone:before {
  content: "\f32d";
}

.ion-md-moon:before {
  content: "\f32e";
}

.ion-md-more:before {
  content: "\f1c9";
}

.ion-md-move:before {
  content: "\f331";
}

.ion-md-musical-note:before {
  content: "\f332";
}

.ion-md-musical-notes:before {
  content: "\f333";
}

.ion-md-navigate:before {
  content: "\f334";
}

.ion-md-no-smoking:before {
  content: "\f335";
}

.ion-md-notifications:before {
  content: "\f338";
}

.ion-md-notifications-off:before {
  content: "\f336";
}

.ion-md-notifications-outline:before {
  content: "\f337";
}

.ion-md-nuclear:before {
  content: "\f339";
}

.ion-md-nutrition:before {
  content: "\f33a";
}

.ion-md-open:before {
  content: "\f33b";
}

.ion-md-options:before {
  content: "\f33c";
}

.ion-md-outlet:before {
  content: "\f33d";
}

.ion-md-paper:before {
  content: "\f33f";
}

.ion-md-paper-plane:before {
  content: "\f33e";
}

.ion-md-partly-sunny:before {
  content: "\f340";
}

.ion-md-pause:before {
  content: "\f341";
}

.ion-md-paw:before {
  content: "\f342";
}

.ion-md-people:before {
  content: "\f343";
}

.ion-md-person:before {
  content: "\f345";
}

.ion-md-person-add:before {
  content: "\f344";
}

.ion-md-phone-landscape:before {
  content: "\f346";
}

.ion-md-phone-portrait:before {
  content: "\f347";
}

.ion-md-photos:before {
  content: "\f348";
}

.ion-md-pie:before {
  content: "\f349";
}

.ion-md-pin:before {
  content: "\f34a";
}

.ion-md-pint:before {
  content: "\f34b";
}

.ion-md-pizza:before {
  content: "\f354";
}

.ion-md-plane:before {
  content: "\f355";
}

.ion-md-planet:before {
  content: "\f356";
}

.ion-md-play:before {
  content: "\f357";
}

.ion-md-podium:before {
  content: "\f358";
}

.ion-md-power:before {
  content: "\f359";
}

.ion-md-pricetag:before {
  content: "\f35a";
}

.ion-md-pricetags:before {
  content: "\f35b";
}

.ion-md-print:before {
  content: "\f35c";
}

.ion-md-pulse:before {
  content: "\f35d";
}

.ion-md-qr-scanner:before {
  content: "\f35e";
}

.ion-md-quote:before {
  content: "\f35f";
}

.ion-md-radio:before {
  content: "\f362";
}

.ion-md-radio-button-off:before {
  content: "\f360";
}

.ion-md-radio-button-on:before {
  content: "\f361";
}

.ion-md-rainy:before {
  content: "\f363";
}

.ion-md-recording:before {
  content: "\f364";
}

.ion-md-redo:before {
  content: "\f365";
}

.ion-md-refresh:before {
  content: "\f366";
}

.ion-md-refresh-circle:before {
  content: "\f228";
}

.ion-md-remove:before {
  content: "\f368";
}

.ion-md-remove-circle:before {
  content: "\f367";
}

.ion-md-reorder:before {
  content: "\f369";
}

.ion-md-repeat:before {
  content: "\f36a";
}

.ion-md-resize:before {
  content: "\f36b";
}

.ion-md-restaurant:before {
  content: "\f36c";
}

.ion-md-return-left:before {
  content: "\f36d";
}

.ion-md-return-right:before {
  content: "\f36e";
}

.ion-md-reverse-camera:before {
  content: "\f36f";
}

.ion-md-rewind:before {
  content: "\f370";
}

.ion-md-ribbon:before {
  content: "\f371";
}

.ion-md-rose:before {
  content: "\f372";
}

.ion-md-sad:before {
  content: "\f373";
}

.ion-md-school:before {
  content: "\f374";
}

.ion-md-search:before {
  content: "\f375";
}

.ion-md-send:before {
  content: "\f376";
}

.ion-md-settings:before {
  content: "\f377";
}

.ion-md-share:before {
  content: "\f379";
}

.ion-md-share-alt:before {
  content: "\f378";
}

.ion-md-shirt:before {
  content: "\f37a";
}

.ion-md-shuffle:before {
  content: "\f37b";
}

.ion-md-skip-backward:before {
  content: "\f37c";
}

.ion-md-skip-forward:before {
  content: "\f37d";
}

.ion-md-snow:before {
  content: "\f37e";
}

.ion-md-speedometer:before {
  content: "\f37f";
}

.ion-md-square:before {
  content: "\f381";
}

.ion-md-square-outline:before {
  content: "\f380";
}

.ion-md-star:before {
  content: "\f384";
}

.ion-md-star-half:before {
  content: "\f382";
}

.ion-md-star-outline:before {
  content: "\f383";
}

.ion-md-stats:before {
  content: "\f385";
}

.ion-md-stopwatch:before {
  content: "\f386";
}

.ion-md-subway:before {
  content: "\f387";
}

.ion-md-sunny:before {
  content: "\f388";
}

.ion-md-swap:before {
  content: "\f389";
}

.ion-md-switch:before {
  content: "\f38a";
}

.ion-md-sync:before {
  content: "\f38b";
}

.ion-md-tablet-landscape:before {
  content: "\f38c";
}

.ion-md-tablet-portrait:before {
  content: "\f38d";
}

.ion-md-tennisball:before {
  content: "\f38e";
}

.ion-md-text:before {
  content: "\f38f";
}

.ion-md-thermometer:before {
  content: "\f390";
}

.ion-md-thumbs-down:before {
  content: "\f391";
}

.ion-md-thumbs-up:before {
  content: "\f392";
}

.ion-md-thunderstorm:before {
  content: "\f393";
}

.ion-md-time:before {
  content: "\f394";
}

.ion-md-timer:before {
  content: "\f395";
}

.ion-md-train:before {
  content: "\f396";
}

.ion-md-transgender:before {
  content: "\f397";
}

.ion-md-trash:before {
  content: "\f398";
}

.ion-md-trending-down:before {
  content: "\f399";
}

.ion-md-trending-up:before {
  content: "\f39a";
}

.ion-md-trophy:before {
  content: "\f39b";
}

.ion-md-umbrella:before {
  content: "\f39c";
}

.ion-md-undo:before {
  content: "\f39d";
}

.ion-md-unlock:before {
  content: "\f39e";
}

.ion-md-videocam:before {
  content: "\f39f";
}

.ion-md-volume-down:before {
  content: "\f3a0";
}

.ion-md-volume-mute:before {
  content: "\f3a1";
}

.ion-md-volume-off:before {
  content: "\f3a2";
}

.ion-md-volume-up:before {
  content: "\f3a3";
}

.ion-md-walk:before {
  content: "\f3a4";
}

.ion-md-warning:before {
  content: "\f3a5";
}

.ion-md-watch:before {
  content: "\f3a6";
}

.ion-md-water:before {
  content: "\f3a7";
}

.ion-md-wifi:before {
  content: "\f3a8";
}

.ion-md-wine:before {
  content: "\f3a9";
}

.ion-md-woman:before {
  content: "\f3aa";
}

/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/* FONT PATH
 * -------------------------- */
@font-face {
  font-family: 'FontAwesome';
  src: url("../fonts/fontawesome-webfont.eot?v=4.7.0");
  src: url("../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("../fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("../fonts/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("../fonts/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* makes the font 33% larger relative to the icon container */
.fa-lg {
  font-size: 1.3333333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-fw {
  width: 1.2857142857em;
  text-align: center;
}

.fa-ul {
  padding-left: 0;
  margin-left: 2.1428571429em;
  list-style-type: none;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  position: absolute;
  left: -2.1428571429em;
  width: 2.1428571429em;
  top: 0.1428571429em;
  text-align: center;
}

.fa-li.fa-lg {
  left: -1.8571428571em;
}

.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eee;
  border-radius: .1em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left {
  margin-right: .3em;
}

.fa.fa-pull-right {
  margin-left: .3em;
}

/* Deprecated as of 4.4.0 */
.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.fa.pull-left {
  margin-right: .3em;
}

.fa.pull-right {
  margin-left: .3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  transform: scale(1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  -webkit-filter: none;
  filter: none;
}

.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: "";
}

.fa-music:before {
  content: "";
}

.fa-search:before {
  content: "";
}

.fa-envelope-o:before {
  content: "";
}

.fa-heart:before {
  content: "";
}

.fa-star:before {
  content: "";
}

.fa-star-o:before {
  content: "";
}

.fa-user:before {
  content: "";
}

.fa-film:before {
  content: "";
}

.fa-th-large:before {
  content: "";
}

.fa-th:before {
  content: "";
}

.fa-th-list:before {
  content: "";
}

.fa-check:before {
  content: "";
}

.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: "";
}

.fa-search-plus:before {
  content: "";
}

.fa-search-minus:before {
  content: "";
}

.fa-power-off:before {
  content: "";
}

.fa-signal:before {
  content: "";
}

.fa-gear:before,
.fa-cog:before {
  content: "";
}

.fa-trash-o:before {
  content: "";
}

.fa-home:before {
  content: "";
}

.fa-file-o:before {
  content: "";
}

.fa-clock-o:before {
  content: "";
}

.fa-road:before {
  content: "";
}

.fa-download:before {
  content: "";
}

.fa-arrow-circle-o-down:before {
  content: "";
}

.fa-arrow-circle-o-up:before {
  content: "";
}

.fa-inbox:before {
  content: "";
}

.fa-play-circle-o:before {
  content: "";
}

.fa-rotate-right:before,
.fa-repeat:before {
  content: "";
}

.fa-refresh:before {
  content: "";
}

.fa-list-alt:before {
  content: "";
}

.fa-lock:before {
  content: "";
}

.fa-flag:before {
  content: "";
}

.fa-headphones:before {
  content: "";
}

.fa-volume-off:before {
  content: "";
}

.fa-volume-down:before {
  content: "";
}

.fa-volume-up:before {
  content: "";
}

.fa-qrcode:before {
  content: "";
}

.fa-barcode:before {
  content: "";
}

.fa-tag:before {
  content: "";
}

.fa-tags:before {
  content: "";
}

.fa-book:before {
  content: "";
}

.fa-bookmark:before {
  content: "";
}

.fa-print:before {
  content: "";
}

.fa-camera:before {
  content: "";
}

.fa-font:before {
  content: "";
}

.fa-bold:before {
  content: "";
}

.fa-italic:before {
  content: "";
}

.fa-text-height:before {
  content: "";
}

.fa-text-width:before {
  content: "";
}

.fa-align-left:before {
  content: "";
}

.fa-align-center:before {
  content: "";
}

.fa-align-right:before {
  content: "";
}

.fa-align-justify:before {
  content: "";
}

.fa-list:before {
  content: "";
}

.fa-dedent:before,
.fa-outdent:before {
  content: "";
}

.fa-indent:before {
  content: "";
}

.fa-video-camera:before {
  content: "";
}

.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: "";
}

.fa-pencil:before {
  content: "";
}

.fa-map-marker:before {
  content: "";
}

.fa-adjust:before {
  content: "";
}

.fa-tint:before {
  content: "";
}

.fa-edit:before,
.fa-pencil-square-o:before {
  content: "";
}

.fa-share-square-o:before {
  content: "";
}

.fa-check-square-o:before {
  content: "";
}

.fa-arrows:before {
  content: "";
}

.fa-step-backward:before {
  content: "";
}

.fa-fast-backward:before {
  content: "";
}

.fa-backward:before {
  content: "";
}

.fa-play:before {
  content: "";
}

.fa-pause:before {
  content: "";
}

.fa-stop:before {
  content: "";
}

.fa-forward:before {
  content: "";
}

.fa-fast-forward:before {
  content: "";
}

.fa-step-forward:before {
  content: "";
}

.fa-eject:before {
  content: "";
}

.fa-chevron-left:before {
  content: "";
}

.fa-chevron-right:before {
  content: "";
}

.fa-plus-circle:before {
  content: "";
}

.fa-minus-circle:before {
  content: "";
}

.fa-times-circle:before {
  content: "";
}

.fa-check-circle:before {
  content: "";
}

.fa-question-circle:before {
  content: "";
}

.fa-info-circle:before {
  content: "";
}

.fa-crosshairs:before {
  content: "";
}

.fa-times-circle-o:before {
  content: "";
}

.fa-check-circle-o:before {
  content: "";
}

.fa-ban:before {
  content: "";
}

.fa-arrow-left:before {
  content: "";
}

.fa-arrow-right:before {
  content: "";
}

.fa-arrow-up:before {
  content: "";
}

.fa-arrow-down:before {
  content: "";
}

.fa-mail-forward:before,
.fa-share:before {
  content: "";
}

.fa-expand:before {
  content: "";
}

.fa-compress:before {
  content: "";
}

.fa-plus:before {
  content: "";
}

.fa-minus:before {
  content: "";
}

.fa-asterisk:before {
  content: "";
}

.fa-exclamation-circle:before {
  content: "";
}

.fa-gift:before {
  content: "";
}

.fa-leaf:before {
  content: "";
}

.fa-fire:before {
  content: "";
}

.fa-eye:before {
  content: "";
}

.fa-eye-slash:before {
  content: "";
}

.fa-warning:before,
.fa-exclamation-triangle:before {
  content: "";
}

.fa-plane:before {
  content: "";
}

.fa-calendar:before {
  content: "";
}

.fa-random:before {
  content: "";
}

.fa-comment:before {
  content: "";
}

.fa-magnet:before {
  content: "";
}

.fa-chevron-up:before {
  content: "";
}

.fa-chevron-down:before {
  content: "";
}

.fa-retweet:before {
  content: "";
}

.fa-shopping-cart:before {
  content: "";
}

.fa-folder:before {
  content: "";
}

.fa-folder-open:before {
  content: "";
}

.fa-arrows-v:before {
  content: "";
}

.fa-arrows-h:before {
  content: "";
}

.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "";
}

.fa-twitter-square:before {
  content: "";
}

.fa-facebook-square:before {
  content: "";
}

.fa-camera-retro:before {
  content: "";
}

.fa-key:before {
  content: "";
}

.fa-gears:before,
.fa-cogs:before {
  content: "";
}

.fa-comments:before {
  content: "";
}

.fa-thumbs-o-up:before {
  content: "";
}

.fa-thumbs-o-down:before {
  content: "";
}

.fa-star-half:before {
  content: "";
}

.fa-heart-o:before {
  content: "";
}

.fa-sign-out:before {
  content: "";
}

.fa-linkedin-square:before {
  content: "";
}

.fa-thumb-tack:before {
  content: "";
}

.fa-external-link:before {
  content: "";
}

.fa-sign-in:before {
  content: "";
}

.fa-trophy:before {
  content: "";
}

.fa-github-square:before {
  content: "";
}

.fa-upload:before {
  content: "";
}

.fa-lemon-o:before {
  content: "";
}

.fa-phone:before {
  content: "";
}

.fa-square-o:before {
  content: "";
}

.fa-bookmark-o:before {
  content: "";
}

.fa-phone-square:before {
  content: "";
}

.fa-twitter:before {
  content: "";
}

.fa-facebook-f:before,
.fa-facebook:before {
  content: "";
}

.fa-github:before {
  content: "";
}

.fa-unlock:before {
  content: "";
}

.fa-credit-card:before {
  content: "";
}

.fa-feed:before,
.fa-rss:before {
  content: "";
}

.fa-hdd-o:before {
  content: "";
}

.fa-bullhorn:before {
  content: "";
}

.fa-bell:before {
  content: "";
}

.fa-certificate:before {
  content: "";
}

.fa-hand-o-right:before {
  content: "";
}

.fa-hand-o-left:before {
  content: "";
}

.fa-hand-o-up:before {
  content: "";
}

.fa-hand-o-down:before {
  content: "";
}

.fa-arrow-circle-left:before {
  content: "";
}

.fa-arrow-circle-right:before {
  content: "";
}

.fa-arrow-circle-up:before {
  content: "";
}

.fa-arrow-circle-down:before {
  content: "";
}

.fa-globe:before {
  content: "";
}

.fa-wrench:before {
  content: "";
}

.fa-tasks:before {
  content: "";
}

.fa-filter:before {
  content: "";
}

.fa-briefcase:before {
  content: "";
}

.fa-arrows-alt:before {
  content: "";
}

.fa-group:before,
.fa-users:before {
  content: "";
}

.fa-chain:before,
.fa-link:before {
  content: "";
}

.fa-cloud:before {
  content: "";
}

.fa-flask:before {
  content: "";
}

.fa-cut:before,
.fa-scissors:before {
  content: "";
}

.fa-copy:before,
.fa-files-o:before {
  content: "";
}

.fa-paperclip:before {
  content: "";
}

.fa-save:before,
.fa-floppy-o:before {
  content: "";
}

.fa-square:before {
  content: "";
}

.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: "";
}

.fa-list-ul:before {
  content: "";
}

.fa-list-ol:before {
  content: "";
}

.fa-strikethrough:before {
  content: "";
}

.fa-underline:before {
  content: "";
}

.fa-table:before {
  content: "";
}

.fa-magic:before {
  content: "";
}

.fa-truck:before {
  content: "";
}

.fa-pinterest:before {
  content: "";
}

.fa-pinterest-square:before {
  content: "";
}

.fa-google-plus-square:before {
  content: "";
}

.fa-google-plus:before {
  content: "";
}

.fa-money:before {
  content: "";
}

.fa-caret-down:before {
  content: "";
}

.fa-caret-up:before {
  content: "";
}

.fa-caret-left:before {
  content: "";
}

.fa-caret-right:before {
  content: "";
}

.fa-columns:before {
  content: "";
}

.fa-unsorted:before,
.fa-sort:before {
  content: "";
}

.fa-sort-down:before,
.fa-sort-desc:before {
  content: "";
}

.fa-sort-up:before,
.fa-sort-asc:before {
  content: "";
}

.fa-envelope:before {
  content: "";
}

.fa-linkedin:before {
  content: "";
}

.fa-rotate-left:before,
.fa-undo:before {
  content: "";
}

.fa-legal:before,
.fa-gavel:before {
  content: "";
}

.fa-dashboard:before,
.fa-tachometer:before {
  content: "";
}

.fa-comment-o:before {
  content: "";
}

.fa-comments-o:before {
  content: "";
}

.fa-flash:before,
.fa-bolt:before {
  content: "";
}

.fa-sitemap:before {
  content: "";
}

.fa-umbrella:before {
  content: "";
}

.fa-paste:before,
.fa-clipboard:before {
  content: "";
}

.fa-lightbulb-o:before {
  content: "";
}

.fa-exchange:before {
  content: "";
}

.fa-cloud-download:before {
  content: "";
}

.fa-cloud-upload:before {
  content: "";
}

.fa-user-md:before {
  content: "";
}

.fa-stethoscope:before {
  content: "";
}

.fa-suitcase:before {
  content: "";
}

.fa-bell-o:before {
  content: "";
}

.fa-coffee:before {
  content: "";
}

.fa-cutlery:before {
  content: "";
}

.fa-file-text-o:before {
  content: "";
}

.fa-building-o:before {
  content: "";
}

.fa-hospital-o:before {
  content: "";
}

.fa-ambulance:before {
  content: "";
}

.fa-medkit:before {
  content: "";
}

.fa-fighter-jet:before {
  content: "";
}

.fa-beer:before {
  content: "";
}

.fa-h-square:before {
  content: "";
}

.fa-plus-square:before {
  content: "";
}

.fa-angle-double-left:before {
  content: "";
}

.fa-angle-double-right:before {
  content: "";
}

.fa-angle-double-up:before {
  content: "";
}

.fa-angle-double-down:before {
  content: "";
}

.fa-angle-left:before {
  content: "";
}

.fa-angle-right:before {
  content: "";
}

.fa-angle-up:before {
  content: "";
}

.fa-angle-down:before {
  content: "";
}

.fa-desktop:before {
  content: "";
}

.fa-laptop:before {
  content: "";
}

.fa-tablet:before {
  content: "";
}

.fa-mobile-phone:before,
.fa-mobile:before {
  content: "";
}

.fa-circle-o:before {
  content: "";
}

.fa-quote-left:before {
  content: "";
}

.fa-quote-right:before {
  content: "";
}

.fa-spinner:before {
  content: "";
}

.fa-circle:before {
  content: "";
}

.fa-mail-reply:before,
.fa-reply:before {
  content: "";
}

.fa-github-alt:before {
  content: "";
}

.fa-folder-o:before {
  content: "";
}

.fa-folder-open-o:before {
  content: "";
}

.fa-smile-o:before {
  content: "";
}

.fa-frown-o:before {
  content: "";
}

.fa-meh-o:before {
  content: "";
}

.fa-gamepad:before {
  content: "";
}

.fa-keyboard-o:before {
  content: "";
}

.fa-flag-o:before {
  content: "";
}

.fa-flag-checkered:before {
  content: "";
}

.fa-terminal:before {
  content: "";
}

.fa-code:before {
  content: "";
}

.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "";
}

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "";
}

.fa-location-arrow:before {
  content: "";
}

.fa-crop:before {
  content: "";
}

.fa-code-fork:before {
  content: "";
}

.fa-unlink:before,
.fa-chain-broken:before {
  content: "";
}

.fa-question:before {
  content: "";
}

.fa-info:before {
  content: "";
}

.fa-exclamation:before {
  content: "";
}

.fa-superscript:before {
  content: "";
}

.fa-subscript:before {
  content: "";
}

.fa-eraser:before {
  content: "";
}

.fa-puzzle-piece:before {
  content: "";
}

.fa-microphone:before {
  content: "";
}

.fa-microphone-slash:before {
  content: "";
}

.fa-shield:before {
  content: "";
}

.fa-calendar-o:before {
  content: "";
}

.fa-fire-extinguisher:before {
  content: "";
}

.fa-rocket:before {
  content: "";
}

.fa-maxcdn:before {
  content: "";
}

.fa-chevron-circle-left:before {
  content: "";
}

.fa-chevron-circle-right:before {
  content: "";
}

.fa-chevron-circle-up:before {
  content: "";
}

.fa-chevron-circle-down:before {
  content: "";
}

.fa-html5:before {
  content: "";
}

.fa-css3:before {
  content: "";
}

.fa-anchor:before {
  content: "";
}

.fa-unlock-alt:before {
  content: "";
}

.fa-bullseye:before {
  content: "";
}

.fa-ellipsis-h:before {
  content: "";
}

.fa-ellipsis-v:before {
  content: "";
}

.fa-rss-square:before {
  content: "";
}

.fa-play-circle:before {
  content: "";
}

.fa-ticket:before {
  content: "";
}

.fa-minus-square:before {
  content: "";
}

.fa-minus-square-o:before {
  content: "";
}

.fa-level-up:before {
  content: "";
}

.fa-level-down:before {
  content: "";
}

.fa-check-square:before {
  content: "";
}

.fa-pencil-square:before {
  content: "";
}

.fa-external-link-square:before {
  content: "";
}

.fa-share-square:before {
  content: "";
}

.fa-compass:before {
  content: "";
}

.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: "";
}

.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: "";
}

.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: "";
}

.fa-euro:before,
.fa-eur:before {
  content: "";
}

.fa-gbp:before {
  content: "";
}

.fa-dollar:before,
.fa-usd:before {
  content: "";
}

.fa-rupee:before,
.fa-inr:before {
  content: "";
}

.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: "";
}

.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: "";
}

.fa-won:before,
.fa-krw:before {
  content: "";
}

.fa-bitcoin:before,
.fa-btc:before {
  content: "";
}

.fa-file:before {
  content: "";
}

.fa-file-text:before {
  content: "";
}

.fa-sort-alpha-asc:before {
  content: "";
}

.fa-sort-alpha-desc:before {
  content: "";
}

.fa-sort-amount-asc:before {
  content: "";
}

.fa-sort-amount-desc:before {
  content: "";
}

.fa-sort-numeric-asc:before {
  content: "";
}

.fa-sort-numeric-desc:before {
  content: "";
}

.fa-thumbs-up:before {
  content: "";
}

.fa-thumbs-down:before {
  content: "";
}

.fa-youtube-square:before {
  content: "";
}

.fa-youtube:before {
  content: "";
}

.fa-xing:before {
  content: "";
}

.fa-xing-square:before {
  content: "";
}

.fa-youtube-play:before {
  content: "";
}

.fa-dropbox:before {
  content: "";
}

.fa-stack-overflow:before {
  content: "";
}

.fa-instagram:before {
  content: "";
}

.fa-flickr:before {
  content: "";
}

.fa-adn:before {
  content: "";
}

.fa-bitbucket:before {
  content: "";
}

.fa-bitbucket-square:before {
  content: "";
}

.fa-tumblr:before {
  content: "";
}

.fa-tumblr-square:before {
  content: "";
}

.fa-long-arrow-down:before {
  content: "";
}

.fa-long-arrow-up:before {
  content: "";
}

.fa-long-arrow-left:before {
  content: "";
}

.fa-long-arrow-right:before {
  content: "";
}

.fa-apple:before {
  content: "";
}

.fa-windows:before {
  content: "";
}

.fa-android:before {
  content: "";
}

.fa-linux:before {
  content: "";
}

.fa-dribbble:before {
  content: "";
}

.fa-skype:before {
  content: "";
}

.fa-foursquare:before {
  content: "";
}

.fa-trello:before {
  content: "";
}

.fa-female:before {
  content: "";
}

.fa-male:before {
  content: "";
}

.fa-gittip:before,
.fa-gratipay:before {
  content: "";
}

.fa-sun-o:before {
  content: "";
}

.fa-moon-o:before {
  content: "";
}

.fa-archive:before {
  content: "";
}

.fa-bug:before {
  content: "";
}

.fa-vk:before {
  content: "";
}

.fa-weibo:before {
  content: "";
}

.fa-renren:before {
  content: "";
}

.fa-pagelines:before {
  content: "";
}

.fa-stack-exchange:before {
  content: "";
}

.fa-arrow-circle-o-right:before {
  content: "";
}

.fa-arrow-circle-o-left:before {
  content: "";
}

.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: "";
}

.fa-dot-circle-o:before {
  content: "";
}

.fa-wheelchair:before {
  content: "";
}

.fa-vimeo-square:before {
  content: "";
}

.fa-turkish-lira:before,
.fa-try:before {
  content: "";
}

.fa-plus-square-o:before {
  content: "";
}

.fa-space-shuttle:before {
  content: "";
}

.fa-slack:before {
  content: "";
}

.fa-envelope-square:before {
  content: "";
}

.fa-wordpress:before {
  content: "";
}

.fa-openid:before {
  content: "";
}

.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: "";
}

.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: "";
}

.fa-yahoo:before {
  content: "";
}

.fa-google:before {
  content: "";
}

.fa-reddit:before {
  content: "";
}

.fa-reddit-square:before {
  content: "";
}

.fa-stumbleupon-circle:before {
  content: "";
}

.fa-stumbleupon:before {
  content: "";
}

.fa-delicious:before {
  content: "";
}

.fa-digg:before {
  content: "";
}

.fa-pied-piper-pp:before {
  content: "";
}

.fa-pied-piper-alt:before {
  content: "";
}

.fa-drupal:before {
  content: "";
}

.fa-joomla:before {
  content: "";
}

.fa-language:before {
  content: "";
}

.fa-fax:before {
  content: "";
}

.fa-building:before {
  content: "";
}

.fa-child:before {
  content: "";
}

.fa-paw:before {
  content: "";
}

.fa-spoon:before {
  content: "";
}

.fa-cube:before {
  content: "";
}

.fa-cubes:before {
  content: "";
}

.fa-behance:before {
  content: "";
}

.fa-behance-square:before {
  content: "";
}

.fa-steam:before {
  content: "";
}

.fa-steam-square:before {
  content: "";
}

.fa-recycle:before {
  content: "";
}

.fa-automobile:before,
.fa-car:before {
  content: "";
}

.fa-cab:before,
.fa-taxi:before {
  content: "";
}

.fa-tree:before {
  content: "";
}

.fa-spotify:before {
  content: "";
}

.fa-deviantart:before {
  content: "";
}

.fa-soundcloud:before {
  content: "";
}

.fa-database:before {
  content: "";
}

.fa-file-pdf-o:before {
  content: "";
}

.fa-file-word-o:before {
  content: "";
}

.fa-file-excel-o:before {
  content: "";
}

.fa-file-powerpoint-o:before {
  content: "";
}

.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: "";
}

.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: "";
}

.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: "";
}

.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "";
}

.fa-file-code-o:before {
  content: "";
}

.fa-vine:before {
  content: "";
}

.fa-codepen:before {
  content: "";
}

.fa-jsfiddle:before {
  content: "";
}

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: "";
}

.fa-circle-o-notch:before {
  content: "";
}

.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
  content: "";
}

.fa-ge:before,
.fa-empire:before {
  content: "";
}

.fa-git-square:before {
  content: "";
}

.fa-git:before {
  content: "";
}

.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: "";
}

.fa-tencent-weibo:before {
  content: "";
}

.fa-qq:before {
  content: "";
}

.fa-wechat:before,
.fa-weixin:before {
  content: "";
}

.fa-send:before,
.fa-paper-plane:before {
  content: "";
}

.fa-send-o:before,
.fa-paper-plane-o:before {
  content: "";
}

.fa-history:before {
  content: "";
}

.fa-circle-thin:before {
  content: "";
}

.fa-header:before {
  content: "";
}

.fa-paragraph:before {
  content: "";
}

.fa-sliders:before {
  content: "";
}

.fa-share-alt:before {
  content: "";
}

.fa-share-alt-square:before {
  content: "";
}

.fa-bomb:before {
  content: "";
}

.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: "";
}

.fa-tty:before {
  content: "";
}

.fa-binoculars:before {
  content: "";
}

.fa-plug:before {
  content: "";
}

.fa-slideshare:before {
  content: "";
}

.fa-twitch:before {
  content: "";
}

.fa-yelp:before {
  content: "";
}

.fa-newspaper-o:before {
  content: "";
}

.fa-wifi:before {
  content: "";
}

.fa-calculator:before {
  content: "";
}

.fa-paypal:before {
  content: "";
}

.fa-google-wallet:before {
  content: "";
}

.fa-cc-visa:before {
  content: "";
}

.fa-cc-mastercard:before {
  content: "";
}

.fa-cc-discover:before {
  content: "";
}

.fa-cc-amex:before {
  content: "";
}

.fa-cc-paypal:before {
  content: "";
}

.fa-cc-stripe:before {
  content: "";
}

.fa-bell-slash:before {
  content: "";
}

.fa-bell-slash-o:before {
  content: "";
}

.fa-trash:before {
  content: "";
}

.fa-copyright:before {
  content: "";
}

.fa-at:before {
  content: "";
}

.fa-eyedropper:before {
  content: "";
}

.fa-paint-brush:before {
  content: "";
}

.fa-birthday-cake:before {
  content: "";
}

.fa-area-chart:before {
  content: "";
}

.fa-pie-chart:before {
  content: "";
}

.fa-line-chart:before {
  content: "";
}

.fa-lastfm:before {
  content: "";
}

.fa-lastfm-square:before {
  content: "";
}

.fa-toggle-off:before {
  content: "";
}

.fa-toggle-on:before {
  content: "";
}

.fa-bicycle:before {
  content: "";
}

.fa-bus:before {
  content: "";
}

.fa-ioxhost:before {
  content: "";
}

.fa-angellist:before {
  content: "";
}

.fa-cc:before {
  content: "";
}

.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: "";
}

.fa-meanpath:before {
  content: "";
}

.fa-buysellads:before {
  content: "";
}

.fa-connectdevelop:before {
  content: "";
}

.fa-dashcube:before {
  content: "";
}

.fa-forumbee:before {
  content: "";
}

.fa-leanpub:before {
  content: "";
}

.fa-sellsy:before {
  content: "";
}

.fa-shirtsinbulk:before {
  content: "";
}

.fa-simplybuilt:before {
  content: "";
}

.fa-skyatlas:before {
  content: "";
}

.fa-cart-plus:before {
  content: "";
}

.fa-cart-arrow-down:before {
  content: "";
}

.fa-diamond:before {
  content: "";
}

.fa-ship:before {
  content: "";
}

.fa-user-secret:before {
  content: "";
}

.fa-motorcycle:before {
  content: "";
}

.fa-street-view:before {
  content: "";
}

.fa-heartbeat:before {
  content: "";
}

.fa-venus:before {
  content: "";
}

.fa-mars:before {
  content: "";
}

.fa-mercury:before {
  content: "";
}

.fa-intersex:before,
.fa-transgender:before {
  content: "";
}

.fa-transgender-alt:before {
  content: "";
}

.fa-venus-double:before {
  content: "";
}

.fa-mars-double:before {
  content: "";
}

.fa-venus-mars:before {
  content: "";
}

.fa-mars-stroke:before {
  content: "";
}

.fa-mars-stroke-v:before {
  content: "";
}

.fa-mars-stroke-h:before {
  content: "";
}

.fa-neuter:before {
  content: "";
}

.fa-genderless:before {
  content: "";
}

.fa-facebook-official:before {
  content: "";
}

.fa-pinterest-p:before {
  content: "";
}

.fa-whatsapp:before {
  content: "";
}

.fa-server:before {
  content: "";
}

.fa-user-plus:before {
  content: "";
}

.fa-user-times:before {
  content: "";
}

.fa-hotel:before,
.fa-bed:before {
  content: "";
}

.fa-viacoin:before {
  content: "";
}

.fa-train:before {
  content: "";
}

.fa-subway:before {
  content: "";
}

.fa-medium:before {
  content: "";
}

.fa-yc:before,
.fa-y-combinator:before {
  content: "";
}

.fa-optin-monster:before {
  content: "";
}

.fa-opencart:before {
  content: "";
}

.fa-expeditedssl:before {
  content: "";
}

.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
  content: "";
}

.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "";
}

.fa-battery-2:before,
.fa-battery-half:before {
  content: "";
}

.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "";
}

.fa-battery-0:before,
.fa-battery-empty:before {
  content: "";
}

.fa-mouse-pointer:before {
  content: "";
}

.fa-i-cursor:before {
  content: "";
}

.fa-object-group:before {
  content: "";
}

.fa-object-ungroup:before {
  content: "";
}

.fa-sticky-note:before {
  content: "";
}

.fa-sticky-note-o:before {
  content: "";
}

.fa-cc-jcb:before {
  content: "";
}

.fa-cc-diners-club:before {
  content: "";
}

.fa-clone:before {
  content: "";
}

.fa-balance-scale:before {
  content: "";
}

.fa-hourglass-o:before {
  content: "";
}

.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "";
}

.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "";
}

.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "";
}

.fa-hourglass:before {
  content: "";
}

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "";
}

.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: "";
}

.fa-hand-scissors-o:before {
  content: "";
}

.fa-hand-lizard-o:before {
  content: "";
}

.fa-hand-spock-o:before {
  content: "";
}

.fa-hand-pointer-o:before {
  content: "";
}

.fa-hand-peace-o:before {
  content: "";
}

.fa-trademark:before {
  content: "";
}

.fa-registered:before {
  content: "";
}

.fa-creative-commons:before {
  content: "";
}

.fa-gg:before {
  content: "";
}

.fa-gg-circle:before {
  content: "";
}

.fa-tripadvisor:before {
  content: "";
}

.fa-odnoklassniki:before {
  content: "";
}

.fa-odnoklassniki-square:before {
  content: "";
}

.fa-get-pocket:before {
  content: "";
}

.fa-wikipedia-w:before {
  content: "";
}

.fa-safari:before {
  content: "";
}

.fa-chrome:before {
  content: "";
}

.fa-firefox:before {
  content: "";
}

.fa-opera:before {
  content: "";
}

.fa-internet-explorer:before {
  content: "";
}

.fa-tv:before,
.fa-television:before {
  content: "";
}

.fa-contao:before {
  content: "";
}

.fa-500px:before {
  content: "";
}

.fa-amazon:before {
  content: "";
}

.fa-calendar-plus-o:before {
  content: "";
}

.fa-calendar-minus-o:before {
  content: "";
}

.fa-calendar-times-o:before {
  content: "";
}

.fa-calendar-check-o:before {
  content: "";
}

.fa-industry:before {
  content: "";
}

.fa-map-pin:before {
  content: "";
}

.fa-map-signs:before {
  content: "";
}

.fa-map-o:before {
  content: "";
}

.fa-map:before {
  content: "";
}

.fa-commenting:before {
  content: "";
}

.fa-commenting-o:before {
  content: "";
}

.fa-houzz:before {
  content: "";
}

.fa-vimeo:before {
  content: "";
}

.fa-black-tie:before {
  content: "";
}

.fa-fonticons:before {
  content: "";
}

.fa-reddit-alien:before {
  content: "";
}

.fa-edge:before {
  content: "";
}

.fa-credit-card-alt:before {
  content: "";
}

.fa-codiepie:before {
  content: "";
}

.fa-modx:before {
  content: "";
}

.fa-fort-awesome:before {
  content: "";
}

.fa-usb:before {
  content: "";
}

.fa-product-hunt:before {
  content: "";
}

.fa-mixcloud:before {
  content: "";
}

.fa-scribd:before {
  content: "";
}

.fa-pause-circle:before {
  content: "";
}

.fa-pause-circle-o:before {
  content: "";
}

.fa-stop-circle:before {
  content: "";
}

.fa-stop-circle-o:before {
  content: "";
}

.fa-shopping-bag:before {
  content: "";
}

.fa-shopping-basket:before {
  content: "";
}

.fa-hashtag:before {
  content: "";
}

.fa-bluetooth:before {
  content: "";
}

.fa-bluetooth-b:before {
  content: "";
}

.fa-percent:before {
  content: "";
}

.fa-gitlab:before {
  content: "";
}

.fa-wpbeginner:before {
  content: "";
}

.fa-wpforms:before {
  content: "";
}

.fa-envira:before {
  content: "";
}

.fa-universal-access:before {
  content: "";
}

.fa-wheelchair-alt:before {
  content: "";
}

.fa-question-circle-o:before {
  content: "";
}

.fa-blind:before {
  content: "";
}

.fa-audio-description:before {
  content: "";
}

.fa-volume-control-phone:before {
  content: "";
}

.fa-braille:before {
  content: "";
}

.fa-assistive-listening-systems:before {
  content: "";
}

.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
  content: "";
}

.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
  content: "";
}

.fa-glide:before {
  content: "";
}

.fa-glide-g:before {
  content: "";
}

.fa-signing:before,
.fa-sign-language:before {
  content: "";
}

.fa-low-vision:before {
  content: "";
}

.fa-viadeo:before {
  content: "";
}

.fa-viadeo-square:before {
  content: "";
}

.fa-snapchat:before {
  content: "";
}

.fa-snapchat-ghost:before {
  content: "";
}

.fa-snapchat-square:before {
  content: "";
}

.fa-pied-piper:before {
  content: "";
}

.fa-first-order:before {
  content: "";
}

.fa-yoast:before {
  content: "";
}

.fa-themeisle:before {
  content: "";
}

.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: "";
}

.fa-fa:before,
.fa-font-awesome:before {
  content: "";
}

.fa-handshake-o:before {
  content: "";
}

.fa-envelope-open:before {
  content: "";
}

.fa-envelope-open-o:before {
  content: "";
}

.fa-linode:before {
  content: "";
}

.fa-address-book:before {
  content: "";
}

.fa-address-book-o:before {
  content: "";
}

.fa-vcard:before,
.fa-address-card:before {
  content: "";
}

.fa-vcard-o:before,
.fa-address-card-o:before {
  content: "";
}

.fa-user-circle:before {
  content: "";
}

.fa-user-circle-o:before {
  content: "";
}

.fa-user-o:before {
  content: "";
}

.fa-id-badge:before {
  content: "";
}

.fa-drivers-license:before,
.fa-id-card:before {
  content: "";
}

.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: "";
}

.fa-quora:before {
  content: "";
}

.fa-free-code-camp:before {
  content: "";
}

.fa-telegram:before {
  content: "";
}

.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
  content: "";
}

.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: "";
}

.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: "";
}

.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: "";
}

.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: "";
}

.fa-shower:before {
  content: "";
}

.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
  content: "";
}

.fa-podcast:before {
  content: "";
}

.fa-window-maximize:before {
  content: "";
}

.fa-window-minimize:before {
  content: "";
}

.fa-window-restore:before {
  content: "";
}

.fa-times-rectangle:before,
.fa-window-close:before {
  content: "";
}

.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: "";
}

.fa-bandcamp:before {
  content: "";
}

.fa-grav:before {
  content: "";
}

.fa-etsy:before {
  content: "";
}

.fa-imdb:before {
  content: "";
}

.fa-ravelry:before {
  content: "";
}

.fa-eercast:before {
  content: "";
}

.fa-microchip:before {
  content: "";
}

.fa-snowflake-o:before {
  content: "";
}

.fa-superpowers:before {
  content: "";
}

.fa-wpexplorer:before {
  content: "";
}

.fa-meetup:before {
  content: "";
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/*# sourceMappingURL=app.css.map */
