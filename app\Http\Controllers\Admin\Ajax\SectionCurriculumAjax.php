<?php

namespace App\Http\Controllers\Admin\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Request;
use App\CtrSection;
use App\curriculum;
use App\offerings_infos_table;
use Illuminate\Support\Facades\View;
use App\Services\ProgramService;

class SectionCurriculumAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get available curriculum subjects for a section
     */
    public function getAvailableCurricula()
    {
        if (Request::ajax()) {
            $section_id = Input::get('section_id');

            $section = CtrSection::find($section_id);
            if (!$section) {
                return response()->json([
                    'success' => false,
                    'message' => 'Section not found'
                ]);
            }

            $availableCurricula = $section->getAvailableCurricula();

            return view('admin.course_offering.ajax.get_available_curricula', compact('section', 'availableCurricula'))->render();
        }
    }

    /**
     * Get offered curriculum subjects for a section
     */
    public function getOfferedCurricula()
    {
        if (Request::ajax()) {
            $section_id = Input::get('section_id');

            $section = CtrSection::find($section_id);
            if (!$section) {
                return response()->json([
                    'success' => false,
                    'message' => 'Section not found'
                ]);
            }

            $offerings = offerings_infos_table::where('section_name', $section->section_name)
                ->with('curriculum.subject')
                ->get();

            return view('admin.course_offering.ajax.get_offered_curricula', compact('section', 'offerings'))->render();
        }
    }

    /**
     * Search sections with curriculum information
     */
    public function searchSectionsWithCurricula()
    {
        if (Request::ajax()) {
            $program = Input::get('program');
            $level = Input::get('level');
            $section = Input::get('section');
            $college = Input::get('college');

            $query = CtrSection::where('is_active', 1);

            // Apply filters if they are provided
            if (!empty($program)) {
                $query->where('program_code', 'like', "%$program%");
            }

            if (!empty($level)) {
                $query->where('level', 'like', "%$level%");
            }

            if (!empty($section)) {
                $query->where('section_name', 'like', "%$section%");
            }

            if (!empty($college) && Schema::hasColumn('ctr_sections', 'college_code')) {
                $query->where('college_code', $college);
            }

            $sections = $query->get();

            // Add curriculum count information to each section
            foreach ($sections as $section) {
                $section->offered_count = offerings_infos_table::where('section_name', $section->section_name)->count();
                $section->available_count = $section->getAvailableCurricula()->count();

                // Check if college_code property exists
                if (Schema::hasColumn('ctr_sections', 'college_code')) {
                    // Get college name if college_code exists
                    if (!empty($section->college_code)) {
                        $college = \App\College::where('college_code', $section->college_code)->first();
                        $section->college_name = $college ? $college->college_name : $section->college_code;
                    } else {
                        $section->college_name = 'N/A';
                    }
                } else {
                    // If college_code column doesn't exist yet
                    $section->college_code = null;
                    $section->college_name = 'N/A';
                }
            }

            return view('admin.course_offering.ajax.search_sections_with_curricula', compact('sections'));
        }
    }

    /**
     * Add a curriculum subject to a section
     */
    public function addCurriculumToSection()
    {
        if (Request::ajax()) {
            $section_id = Input::get('section_id');
            $curriculum_id = Input::get('curriculum_id');

            $section = CtrSection::find($section_id);
            if (!$section) {
                return response()->json([
                    'success' => false,
                    'message' => 'Section not found'
                ]);
            }

            $curriculum = curriculum::find($curriculum_id);
            if (!$curriculum) {
                return response()->json([
                    'success' => false,
                    'message' => 'Curriculum not found'
                ]);
            }

            // Check if already offered
            $exists = offerings_infos_table::where('section_name', $section->section_name)
                ->where('curriculum_id', $curriculum_id)
                ->exists();

            if ($exists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Curriculum already offered for this section'
                ]);
            }

            // Add to offerings
            $offering = new offerings_infos_table;
            $offering->curriculum_id = $curriculum_id;
            $offering->section_name = $section->section_name;
            $offering->level = $section->level;
            $offering->save();

            return response()->json([
                'success' => true,
                'message' => 'Curriculum added to section successfully',
                'offering_id' => $offering->id
            ]);
        }
    }

    /**
     * Remove a curriculum subject from a section
     */
    public function removeCurriculumFromSection()
    {
        if (Request::ajax()) {
            $offering_id = Input::get('offering_id');

            $offering = offerings_infos_table::find($offering_id);
            if (!$offering) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offering not found'
                ]);
            }

            $offering->delete();

            return response()->json([
                'success' => true,
                'message' => 'Curriculum removed from section successfully'
            ]);
        }
    }

    /**
     * Get curriculum subjects for a program and level
     */
    public function getCurriculaForProgramAndLevel()
    {
        if (Request::ajax()) {
            $program_code = Input::get('program_code');
            $level = Input::get('level');

            if (empty($program_code) || empty($level)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Program code and level are required'
                ]);
            }

            $curricula = curriculum::where('program_code', $program_code)
                ->where('level', $level)
                ->where('is_active', 1)
                ->get();

            return response()->json([
                'success' => true,
                'message' => count($curricula) > 0 ? 'Curricula found' : 'No curricula found',
                'curricula' => $curricula,
                'count' => count($curricula)
            ]);
        }
    }

    /**
     * Edit section (from old section management)
     */
    public function editSection()
    {
        if (Request::ajax()) {
            $section_id = Input::get('section_id');
            $section = CtrSection::find($section_id);

            if (!$section) {
                return '<div class="alert alert-danger">Section not found</div>';
            }

            $colleges = \App\College::where('is_active', 1)->get();

            // Get programs from colleges using ProgramService
            $programService = new ProgramService();
            $programs = $programService->getAllProgramsAsObjects();

            return View::make('admin.course_offering.ajax.edit_section', compact('section', 'programs', 'colleges'))->render();
        }
    }

    /**
     * Get programs by college
     */
    public function getProgramsByCollege()
    {
        if (Request::ajax()) {
            $college_code = Input::get('college_code');

            // Use ProgramService to get programs
            $programService = new ProgramService();

            if (empty($college_code)) {
                // Return all programs if no college is selected
                $programs = $programService->getAllPrograms();
            } else {
                // Get programs for the specified college
                $programs = $programService->getProgramsByCollege($college_code);

                if (empty($programs)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'College not found or has no programs'
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'programs' => $programs
            ]);
        }
    }

    /**
     * Search sections archive
     */
    public function searchSectionsArchive()
    {
        if (Request::ajax()) {
            $program = Input::get('program');
            $level = Input::get('level');
            $section = Input::get('section');
            $college = Input::get('college');

            $query = CtrSection::where('is_active', 0);

            // Apply filters if they are provided
            if (!empty($program)) {
                $query->where('program_code', 'like', "%$program%");
            }

            if (!empty($level)) {
                $query->where('level', 'like', "%$level%");
            }

            if (!empty($section)) {
                $query->where('section_name', 'like', "%$section%");
            }

            if (!empty($college) && Schema::hasColumn('ctr_sections', 'college_code')) {
                $query->where('college_code', $college);
            }

            $sections = $query->get();

            // Add college name to each section
            foreach ($sections as $section) {
                // Check if college_code property exists
                if (Schema::hasColumn('ctr_sections', 'college_code')) {
                    // Get college name if college_code exists
                    if (!empty($section->college_code)) {
                        $college = \App\College::where('college_code', $section->college_code)->first();
                        $section->college_name = $college ? $college->college_name : $section->college_code;
                    } else {
                        $section->college_name = 'N/A';
                    }
                } else {
                    // If college_code column doesn't exist yet
                    $section->college_code = null;
                    $section->college_name = 'N/A';
                }
            }

            return View::make('admin.course_offering.ajax.search_sections_archive', compact('sections'))->render();
        }
    }

    /**
     * Get all programs from all colleges
     */
    public function getAllPrograms()
    {
        if (Request::ajax()) {
            $programService = new ProgramService();
            $programs = $programService->getAllPrograms();

            return response()->json([
                'success' => true,
                'programs' => $programs
            ]);
        }
    }
}
