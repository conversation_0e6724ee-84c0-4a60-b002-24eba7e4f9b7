<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOSS - Curriculum Management')

@section('content')
<section class="content-header">
    <h1>
        Curriculum Management
        <small>Manage curriculum for {{ $college->college_name }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li class="active">Curriculum Management</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-primary" id="curriculum-container">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Management</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/collegeadmin/curriculum/add_curriculum') }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> New Curriculum</a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Program Code</th>
                                            <th>Program Name</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($programs as $program)
                                            <tr>
                                                <td>{{ $program->program_code }}</td>
                                                <td>{{ $program->program_name }}</td>
                                                <td>
                                                    <a href="{{ route('collegeadmin.curriculum.view_curricula', $program->program_code) }}" class="btn btn-primary btn-sm">
                                                        <i class="fa fa-list"></i> View Curricula
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
