<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsActiveColumnToCurricula extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the column already exists
        if (!Schema::hasColumn('curricula', 'is_active')) {
            Schema::table('curricula', function (Blueprint $table) {
                $table->boolean('is_active')->default(1);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Check if the column exists before dropping it
        if (Schema::hasColumn('curricula', 'is_active')) {
            Schema::table('curricula', function (Blueprint $table) {
                $table->dropColumn('is_active');
            });
        }
    }
}
