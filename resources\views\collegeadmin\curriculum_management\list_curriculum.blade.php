<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOSS - Curriculum for ' . $program_code)

@section('content')
<section class="content-header">
    <h1>
        <i class="fa fa-book"></i> Curriculum - {{ $program_code }} ({{ $curriculum_year }}-{{ is_numeric($curriculum_year) ? (int)$curriculum_year + 1 : $curriculum_year }})
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.view_curricula', $program_code) }}">{{ $program_code }}</a></li>
        <li class="active">{{ $curriculum_year }}</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Subjects</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.curriculum.view_curricula', $program_code) }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back to Curriculum Years</a>
                        <a href="{{ route('collegeadmin.curriculum.create') }}?program_code={{ $program_code }}&curriculum_year={{ $curriculum_year }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> Add Subject</a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Lec</th>
                                    <th>Lab</th>
                                    <th>Units</th>
                                    <th>Level</th>
                                    <th>Period</th>
                                    <th>CompLab</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($curriculum as $subject)
                                    <tr>
                                        <td>{{ $subject->control_code }}</td>
                                        <td>{{ $subject->course_name }}</td>
                                        <td>{{ $subject->lec }}</td>
                                        <td>{{ $subject->lab }}</td>
                                        <td>{{ $subject->units }}</td>
                                        <td>{{ $subject->level }}</td>
                                        <td>{{ $subject->period }}</td>
                                        <td>{{ $subject->is_complab ? 'Yes' : 'No' }}</td>
                                        <td>
                                            <a onclick="editCurriculum({{ $subject->id }})" class="btn btn-flat btn-primary btn-sm" title="Edit Subject"><i class="fa fa-pencil"></i></a>
                                            <a onclick="archiveSubject({{ $subject->id }})" class="btn btn-flat btn-danger btn-sm" title="Archive Subject"><i class="fa fa-archive"></i></a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal for editing curriculum subject -->
<div class="modal fade" id="edit-curriculum-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form method="post" action="{{ route('collegeadmin.curriculum.edit_curriculum') }}">
                @csrf
                <input type="hidden" name="curriculum_id" id="edit_curriculum_id">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Edit Curriculum Subject</h4>
                </div>
                <div class="modal-body" id="edit-curriculum-content">
                    <!-- Content will be loaded via AJAX -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function editCurriculum(curriculum_id) {
        $.ajax({
            url: "{{ url('/ajax/collegeadmin/curriculum/edit_modal') }}",
            type: "GET",
            data: {
                curriculum_id: curriculum_id
            },
            success: function(data) {
                $('#edit_curriculum_id').val(curriculum_id);
                $('#edit-curriculum-content').html(data);
                $('#edit-curriculum-modal').modal('show');
            }
        });
    }

    function archiveSubject(curriculum_id) {
        if (confirm('Are you sure you want to archive this subject?')) {
            window.location.href = "{{ url('/collegeadmin/curriculum/archive_subject') }}/" + curriculum_id;
        }
    }
</script>
@endpush
@endsection
