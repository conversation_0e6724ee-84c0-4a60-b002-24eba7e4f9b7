<?php

namespace App\Http\Controllers\Admin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\academic_programs;
use App\curriculum;

class CollegeCurriculumAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('admin');
    }

    // Filter programs by college
    public function filterProgramsByCollege(Request $request)
    {
        $college_code = $request->college_code;
        $programs = \App\ProgramHelper::getProgramsByCollegeCode($college_code);

        return view('admin.curriculum_management.ajax.programs_list', compact('programs'));
    }

    // Get curriculum years for a college
    public function getCurriculumYears(Request $request)
    {
        $college_code = $request->college_code;
        $curriculumYears = curriculum::where('college_code', $college_code)
            ->distinct()
            ->pluck('curriculum_year');

        return response()->json($curriculumYears);
    }

    // Get programs for a college
    public function getCollegePrograms(Request $request)
    {
        $college_code = $request->college_code;
        $programs = \App\ProgramHelper::getProgramsByCollegeCode($college_code);

        return response()->json($programs);
    }
}
