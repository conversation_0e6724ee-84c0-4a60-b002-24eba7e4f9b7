<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateProgramsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->integer('college_id')->unsigned();
            $table->string('program_code');
            $table->string('program_name');

            // Define composite primary key
            $table->primary(['college_id', 'program_code']);

            // Add foreign key constraint
            $table->foreign('college_id')
                  ->references('id')
                  ->on('colleges')
                  ->onDelete('cascade');
        });

        // Migrate data from academic_programs if it exists
        if (Schema::hasTable('academic_programs')) {
            $academicPrograms = DB::table('academic_programs')
                ->whereNotNull('college_code')
                ->whereNotNull('program_code')
                ->get();

            foreach ($academicPrograms as $program) {
                // Find the college ID for this college code
                $college = DB::table('colleges')
                    ->where('college_code', $program->college_code)
                    ->first();

                if ($college) {
                    // Check if this program already exists in the programs table
                    $existingProgram = DB::table('programs')
                        ->where('college_id', $college->id)
                        ->where('program_code', $program->program_code)
                        ->first();

                    if (!$existingProgram) {
                        DB::table('programs')->insert([
                            'college_id' => $college->id,
                            'program_code' => $program->program_code,
                            'program_name' => $program->program_name ?? $program->program_code
                        ]);
                    }
                }
            }
        }

        // Also migrate data from colleges table's program_codes field
        $colleges = DB::table('colleges')->get();
        foreach ($colleges as $college) {
            if (isset($college->program_codes) && !empty($college->program_codes)) {
                $programCodes = explode(',', $college->program_codes);
                $programCodes = array_map('trim', $programCodes);

                foreach ($programCodes as $programCode) {
                    if (!empty($programCode)) {
                        // Check if this program already exists in the programs table
                        $existingProgram = DB::table('programs')
                            ->where('college_id', $college->id)
                            ->where('program_code', $programCode)
                            ->first();

                        if (!$existingProgram) {
                            // Try to find program name in program_names table
                            $programName = DB::table('program_names')
                                ->where('program_code', $programCode)
                                ->value('program_name');

                            DB::table('programs')->insert([
                                'college_id' => $college->id,
                                'program_code' => $programCode,
                                'program_name' => $programName ?? $programCode
                            ]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('programs');
    }
}
