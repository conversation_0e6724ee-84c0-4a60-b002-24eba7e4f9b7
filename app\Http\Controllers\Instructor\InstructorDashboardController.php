<?php

namespace App\Http\Controllers\Instructor;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\CtrRoom;
use App\CtrSection;
use App\instructors_infos;
use App\room_schedules;
use App\User;
use App\curriculum;
use App\offerings_infos_table;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class InstructorDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
    }

    /**
     * Display the instructor dashboard
     */
    public function index()
    {
        // Verify user is an instructor
        if (Auth::user()->accesslevel != 1) {
            return redirect('/401');
        }

        $instructor_id = Auth::user()->id;
        
        // Get instructor information
        $instructor = User::find($instructor_id);
        $instructor_info = instructors_infos::where('instructor_id', $instructor_id)->first();
        
        // Get teaching schedule
        $schedules = $this->getTeachingSchedule($instructor_id);
        
        // Get class attendance summary
        $attendance_summary = $this->getAttendanceSummary($instructor_id);
        
        // Get course offerings for this instructor
        $course_offerings = $this->getCourseOfferings($instructor_id);
        
        return view('instructor.dashboard', compact(
            'instructor',
            'instructor_info',
            'schedules',
            'attendance_summary',
            'course_offerings'
        ));
    }

    /**
     * Get teaching schedule for an instructor
     */
    private function getTeachingSchedule($instructor_id)
    {
        $schedules = room_schedules::where('instructor', $instructor_id)
            ->where('is_active', 1)
            ->get();
            
        $formatted_schedules = [];
        
        foreach ($schedules as $schedule) {
            $offering = offerings_infos_table::find($schedule->offering_id);
            
            if ($offering) {
                $curriculum_details = curriculum::find($offering->curriculum_id);
                
                if ($curriculum_details) {
                    $formatted_schedules[] = [
                        'id' => $schedule->id,
                        'day' => $schedule->day,
                        'time_starts' => $schedule->time_starts,
                        'time_end' => $schedule->time_end,
                        'room' => $schedule->room,
                        'course_code' => $curriculum_details->course_code,
                        'course_name' => $curriculum_details->course_name,
                        'section_name' => $offering->section_name,
                        'level' => $offering->level
                    ];
                }
            }
        }
        
        return $formatted_schedules;
    }

    /**
     * Get attendance summary for an instructor's classes
     */
    private function getAttendanceSummary($instructor_id)
    {
        // Get all courses taught by this instructor
        $schedules = room_schedules::where('instructor', $instructor_id)
            ->where('is_active', 1)
            ->get()
            ->unique('offering_id');
            
        $attendance_summary = [];
        
        foreach ($schedules as $schedule) {
            $offering = offerings_infos_table::find($schedule->offering_id);
            
            if ($offering) {
                $curriculum_details = curriculum::find($offering->curriculum_id);
                
                if ($curriculum_details) {
                    // For now, we'll use placeholder data for attendance
                    // In a real implementation, this would come from an attendance database
                    $attendance_summary[] = [
                        'offering_id' => $schedule->offering_id,
                        'course_code' => $curriculum_details->course_code,
                        'course_name' => $curriculum_details->course_name,
                        'section_name' => $offering->section_name,
                        'total_classes' => rand(10, 30), // Placeholder
                        'total_students' => rand(15, 40), // Placeholder
                        'attendance_rate' => rand(75, 100) // Placeholder percentage
                    ];
                }
            }
        }
        
        return $attendance_summary;
    }

    /**
     * Get course offerings for an instructor
     */
    private function getCourseOfferings($instructor_id)
    {
        $schedules = room_schedules::where('instructor', $instructor_id)
            ->where('is_active', 1)
            ->get()
            ->unique('offering_id');
            
        $offerings = [];
        
        foreach ($schedules as $schedule) {
            $offering = offerings_infos_table::find($schedule->offering_id);
            
            if ($offering) {
                $curriculum_details = curriculum::find($offering->curriculum_id);
                
                if ($curriculum_details) {
                    $offerings[] = [
                        'id' => $offering->id,
                        'course_code' => $curriculum_details->course_code,
                        'course_name' => $curriculum_details->course_name,
                        'section_name' => $offering->section_name,
                        'level' => $offering->level
                    ];
                }
            }
        }
        
        return $offerings;
    }

    /**
     * Export teaching schedule as PDF
     */
    public function exportSchedule()
    {
        $instructor_id = Auth::user()->id;
        $instructor = User::find($instructor_id);
        $schedules = $this->getTeachingSchedule($instructor_id);
        
        $pdf = \PDF::loadView('instructor.reports.teaching_schedule', compact('instructor', 'schedules'));
        return $pdf->download('teaching_schedule.pdf');
    }

    /**
     * Export attendance report as PDF
     */
    public function exportAttendance(Request $request)
    {
        $instructor_id = Auth::user()->id;
        $instructor = User::find($instructor_id);
        $offering_id = $request->input('offering_id');
        
        // Get specific course attendance or all courses if not specified
        if ($offering_id) {
            $attendance_summary = $this->getAttendanceSummaryForCourse($instructor_id, $offering_id);
        } else {
            $attendance_summary = $this->getAttendanceSummary($instructor_id);
        }
        
        $pdf = \PDF::loadView('instructor.reports.attendance_summary', compact('instructor', 'attendance_summary'));
        return $pdf->download('attendance_summary.pdf');
    }

    /**
     * Get attendance summary for a specific course
     */
    private function getAttendanceSummaryForCourse($instructor_id, $offering_id)
    {
        // This would be implemented with real attendance data
        // For now, we'll return a subset of the placeholder data
        $all_attendance = $this->getAttendanceSummary($instructor_id);
        
        return array_filter($all_attendance, function($item) use ($offering_id) {
            return $item['offering_id'] == $offering_id;
        });
    }
}
