<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Attendance Summary Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .instructor-info {
            margin-bottom: 20px;
        }
        .instructor-info p {
            margin: 5px 0;
        }
        .progress-bar {
            background-color: #f2f2f2;
            height: 15px;
            width: 100%;
            position: relative;
        }
        .progress-value {
            background-color: #4CAF50;
            height: 15px;
            position: absolute;
            top: 0;
            left: 0;
        }
        .progress-text {
            position: absolute;
            width: 100%;
            text-align: center;
            font-size: 10px;
            line-height: 15px;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>CLASS ATTENDANCE SUMMARY REPORT</h1>
        <p>{{ date('F d, Y') }}</p>
    </div>

    <div class="instructor-info">
        <p><strong>Instructor:</strong> {{ $instructor->name }} {{ $instructor->lastname }}</p>
        <p><strong>ID:</strong> {{ $instructor->id }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Course Code</th>
                <th>Course Name</th>
                <th>Section</th>
                <th>Total Students</th>
                <th>Total Classes</th>
                <th>Attendance Rate</th>
            </tr>
        </thead>
        <tbody>
            @foreach($attendance_summary as $summary)
            <tr>
                <td>{{ $summary['course_code'] }}</td>
                <td>{{ $summary['course_name'] }}</td>
                <td>{{ $summary['section_name'] }}</td>
                <td>{{ $summary['total_students'] }}</td>
                <td>{{ $summary['total_classes'] }}</td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-value" style="width: {{ $summary['attendance_rate'] }}%;"></div>
                        <div class="progress-text">{{ $summary['attendance_rate'] }}%</div>
                    </div>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Generated on {{ date('Y-m-d H:i:s') }}</p>
        <p>This is an official document of the CLASSMOSS.</p>
    </div>
</body>
</html>
