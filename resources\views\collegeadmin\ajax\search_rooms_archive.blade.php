@if(count($rooms) > 0)
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Room</th>
                    <th>Building</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($rooms as $room)
                    <tr>
                        <td>{{ $room->room }}</td>
                        <td>{{ $room->building }}</td>
                        <td>{{ $room->description }}</td>
                        <td>
                            <a href="{{ route('collegeadmin.room.restore', $room->id) }}" class="btn btn-success btn-sm" onclick="return confirm('Are you sure you want to restore this room?')">
                                <i class="fa fa-undo"></i> Restore
                            </a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info">
        No archived rooms found matching your search criteria.
    </div>
@endif
