@if($availableCurricula->isEmpty())
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> No available curricula found for this section.
    </div>
@else
    <form id="addSelectedForm" action="{{ url('/superadmin/course_offerings/curriculum/section/add_selected', [$section->id]) }}" method="POST">
        {{ csrf_field() }}
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="5%">Select</th>
                        <th width="15%">Course Code</th>
                        <th width="40%">Course Name</th>
                        <th width="10%">Lec</th>
                        <th width="10%">Lab</th>
                        <th width="10%">Units</th>
                        <th width="10%">Period</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($availableCurricula as $curriculum)
                        <tr>
                            <td>
                                <input type="checkbox" name="curriculum_ids[]" value="{{ $curriculum->id }}">
                            </td>
                            <td>{{ $curriculum->course_code }}</td>
                            <td>{{ $curriculum->course_name }}</td>
                            <td>{{ $curriculum->lec }}</td>
                            <td>{{ $curriculum->lab }}</td>
                            <td>{{ $curriculum->units }}</td>
                            <td>{{ $curriculum->period }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </form>
@endif
