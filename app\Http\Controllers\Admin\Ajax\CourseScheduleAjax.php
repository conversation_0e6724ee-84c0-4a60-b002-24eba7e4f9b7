<?php

namespace App\Http\Controllers\Admin\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Request;
use App\TimeBlock;
use App\College;
use App\curriculum;
use App\CtrRoom;
use App\CtrSection;
use App\offerings_infos_table;
use App\room_schedules;
use App\User;
use App\SchedulePriority;

class CourseScheduleAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function get_sections()
    {
        if (Request::ajax()) {
            $college_code = Input::get('college_code');
            $level = Input::get('level');
            $program_code = Input::get('program_code');

            // Log the request parameters for debugging
            \Log::info('Filtering sections with college_code: ' . $college_code . ', level: ' . $level . ', program_code: ' . $program_code);

            // Start with a base query for active sections
            $query = CtrSection::where('is_active', 1);

            // Filter by level if provided
            if ($level) {
                $query->where('level', $level);
            }

            // Filter by program_code if provided
            if ($program_code) {
                $query->where('program_code', $program_code);
            }

            // Create a collection to store all matching sections
            $allSections = collect();

            // Method 1: Direct college_code match in sections table
            if ($college_code) {
                $directMatches = (clone $query)
                    ->where('college_code', $college_code)
                    ->get(['section_name', 'level', 'college_code', 'program_code']);

                $allSections = $allSections->concat($directMatches);
                \Log::info('Direct college_code matches: ' . $directMatches->count());
            }

            // Method 2: Match through program codes in the college
            if ($college_code && $allSections->isEmpty() && !$program_code) {
                $college = College::where('college_code', $college_code)->first();
                if ($college) {
                    $programCodes = $college->getProgramCodes();

                    if (!empty($programCodes)) {
                        $programMatches = (clone $query)
                            ->whereIn('program_code', $programCodes)
                            ->get(['section_name', 'level', 'college_code', 'program_code']);

                        $allSections = $allSections->concat($programMatches);
                        \Log::info('Program code matches: ' . $programMatches->count());
                    }
                }
            }

            // Method 3: Match through curricula and offerings
            if ($college_code && $allSections->isEmpty()) {
                $curriculaQuery = curriculum::where('college_code', $college_code);

                // Add program filter to curricula if provided
                if ($program_code) {
                    $curriculaQuery->where('program_code', $program_code);
                }

                $curricula = $curriculaQuery->pluck('id')->toArray();
                $offerings = offerings_infos_table::whereIn('curriculum_id', $curricula)->pluck('section_name')->toArray();

                if (!empty($offerings)) {
                    $offeringMatches = (clone $query)
                        ->whereIn('section_name', $offerings)
                        ->get(['section_name', 'level', 'college_code', 'program_code']);

                    $allSections = $allSections->concat($offeringMatches);
                    \Log::info('Offering matches: ' . $offeringMatches->count());
                }
            }

            // If no sections found with filters, return all sections matching the base query
            if ($allSections->isEmpty()) {
                $allSections = $query->get(['section_name', 'level', 'college_code', 'program_code']);
                \Log::info('No matches with specific filters, returning filtered sections: ' . $allSections->count());
            }

            // Remove duplicates by section_name
            $sections = $allSections->unique('section_name');
            \Log::info('Final unique sections count: ' . $sections->count());

            // Pass the filter values to the view for additional context
            return view('admin.course_schedule.ajax.get_sections', compact('sections', 'college_code', 'program_code'))->render();
        }
    }

    public function get_courses_offered(Request $request)
    {
        if (Request::ajax()) {
            $section_name = Input::get('section_name');
            $level = Input::get('level');
            $college_code = Input::get('college_code');
            $program_code = Input::get('program_code');

            // Log the request parameters for debugging
            \Log::info('Loading courses for section: ' . $section_name . ', college: ' . $college_code . ', program: ' . $program_code . ', level: ' . $level);

            // Get the section details
            try {
                $section = \App\CtrSection::where('section_name', $section_name)->first();
                if (!$section) {
                    \Log::warning('Section not found: ' . $section_name);
                }
            } catch (\Exception $e) {
                \Log::error('Error retrieving section ' . $section_name . ': ' . $e->getMessage());
                $section = null;
            }

            try {
                // Get courses with eager loaded relationships
                $query = \App\offerings_infos_table::with([
                    'curriculum' => function ($query) {
                        $query->with('college');
                    }
                ])
                    ->where('section_name', $section_name);

                // If level is provided, filter courses by level
                if ($level) {
                    \Log::info('Filtering by level: ' . $level);
                    $query->whereHas('curriculum', function ($q) use ($level) {
                        // Exact match for level
                        $q->where('level', $level);
                    });

                    // Log the SQL query for debugging
                    $sql = $query->toSql();
                    $bindings = $query->getBindings();
                    \Log::info('SQL Query: ' . $sql);
                    \Log::info('Bindings: ' . json_encode($bindings));
                }

                $courses = $query->get();

                \Log::info('Found ' . $courses->count() . ' courses for section: ' . $section_name . ($level ? ' and level: ' . $level : ''));
            } catch (\Exception $e) {
                \Log::error('Error loading courses for section ' . $section_name . ': ' . $e->getMessage());
                $courses = collect([]);
            }

            return view(
                'admin.course_schedule.ajax.get_courses_offered',
                compact('courses', 'section', 'level', 'college_code', 'program_code', 'section_name')
            )->render();
        }
    }

    public function get_rooms_available()
    {
        if (Request::ajax()) {
            $time_start = Input::get('time_start');
            $time_end = Input::get('time_end');
            $offering_id = Input::get('offering_id');
            $section_name = Input::get('section_name');
            $day_type = Input::get('day_type');

            // Check if we have multiple days
            $multiple_days = Input::get('multiple_days');
            $day = Input::get('day'); // Single day

            // Get the curriculum and college information
            $offering = offerings_infos_table::find($offering_id);
            $curriculum = curriculum::find($offering->curriculum_id);
            $college_code = $curriculum->college_code;

            $conflict_schedules = room_schedules::
                join('offerings_infos', 'offerings_infos.id', 'room_schedules.offering_id')
                ->where('offerings_infos.id', $offering_id)
                ->where('room_schedules.day', $day)
                ->where(function ($query) use ($time_start, $time_end) {
                    $query->whereBetween('time_starts', [date("H:i:s", strtotime($time_start)), date("H:i:s", strtotime($time_end))])
                        ->orWhereBetween('time_end', [date("H:i:s", strtotime($time_start)), date("H:i:s", strtotime($time_end))]);
                })
                ->get();

            if (!$conflict_schedules->isEmpty()) {
                $query = "is_active = 1 and";
                foreach ($conflict_schedules as $sched) {
                    $query = $query . " room != '{$sched->room}'";
                }
                $rooms = CtrRoom::whereRaw($query)->get();
            } else {
                // If college_code is provided, prioritize rooms from that college
                if ($college_code) {
                    $rooms = CtrRoom::where('is_active', 1)
                        ->where(function ($query) use ($college_code) {
                            $query->where('college_code', $college_code)
                                ->orWhereNull('college_code');
                        })
                        ->orderByRaw("CASE WHEN college_code = '{$college_code}' THEN 0 ELSE 1 END")
                        ->get();
                } else {
                    $rooms = CtrRoom::where('is_active', 1)->get();
                }
            }

            if ($multiple_days) {
                return view('admin.course_schedule.ajax.get_available_rooms', compact('rooms', 'offering_id', 'multiple_days', 'day_type', 'time_start', 'time_end', 'section_name'));
            } else {
                return view('admin.course_schedule.ajax.get_available_rooms', compact('rooms', 'offering_id', 'day', 'day_type', 'time_start', 'time_end', 'section_name'));
            }
        }
    }

    /**
     * Get time blocks for a specific day type
     */
    public function getTimeBlocks()
    {
        if (Request::ajax()) {
            $dayType = Input::get('day_type');
            $timeBlocks = TimeBlock::getByDayType($dayType);
            return response()->json($timeBlocks);
        }
    }

    /**
     * Get available instructors for a specific time slot
     */
    public function getAvailableInstructors()
    {
        if (Request::ajax()) {
            $day = Input::get('day');
            $timeStart = Input::get('time_start');
            $timeEnd = Input::get('time_end');

            // Get all instructors (users with accesslevel = 1)
            $instructors = User::where('accesslevel', 1)->get();

            $availableInstructors = [];

            foreach ($instructors as $instructor) {
                if (!room_schedules::hasInstructorConflict($day, $timeStart, $timeEnd, $instructor->id)) {
                    $availableInstructors[] = [
                        'id' => $instructor->id,
                        'name' => $instructor->lastname . ', ' . $instructor->name
                    ];
                }
            }

            return response()->json($availableInstructors);
        }
    }

    /**
     * Check for schedule conflicts
     */
    public function checkConflicts()
    {
        if (Request::ajax()) {
            $day = Input::get('day');
            $timeStart = Input::get('time_start');
            $timeEnd = Input::get('time_end');
            $room = Input::get('room');
            $instructorId = Input::get('instructor_id');
            $sectionName = Input::get('section_name');

            $conflicts = room_schedules::getAllConflicts(
                $day,
                $timeStart,
                $timeEnd,
                $room,
                $instructorId,
                $sectionName
            );

            return response()->json([
                'has_conflicts' => !empty($conflicts),
                'conflicts' => $conflicts
            ]);
        }
    }
}





