<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-book"></i>
        Section Curriculum: {{$section->section_name}}
        <small></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/admin/section_curriculum')}}">Section Curriculum</a></li>
        <li class="active">{{$section->section_name}}</li>
      </ol>

      <div class="pull-right">
        <a href="{{url('/admin/section_curriculum')}}" class="btn btn-flat btn-default">
            <i class="fa fa-arrow-left"></i> Back
        </a>
        <a href="{{url('/admin/section_curriculum/add_all',[$section->id])}}" class="btn btn-flat btn-success" onclick="return confirm('Add all available curriculum subjects to this section?')">
            <i class="fa fa-plus-circle"></i> Add All Available Subjects
        </a>
      </div>
      <hr>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Section Information</h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-blue"><i class="fa fa-graduation-cap"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Program</span>
                                    <span class="info-box-number">{{$section->program_code}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-green"><i class="fa fa-level-up"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Level</span>
                                    <span class="info-box-number">{{$section->level}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-yellow"><i class="fa fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Section</span>
                                    <span class="info-box-number">{{$section->section_name}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title">Offered Curriculum Subjects</h3>
                    <div class="box-tools pull-right">
                        <span class="label label-primary">{{count($offerings)}} Subjects</span>
                    </div>
                </div>
                <div class="box-body">
                    @if(count($offerings) > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="offered-table">
                            <thead>
                                <tr>
                                    <th width="15%">Course Code</th>
                                    <th width="35%">Course Name</th>
                                    <th width="10%">Lec</th>
                                    <th width="10%">Lab</th>
                                    <th width="10%">Units</th>
                                    <th width="10%">Period</th>
                                    <th width="10%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($offerings as $offering)
                                <?php $curriculum = \App\curriculum::find($offering->curriculum_id); ?>
                                @if($curriculum)
                                <tr>
                                    <td>{{$curriculum->course_code}}</td>
                                    <td>{{$curriculum->course_name}}</td>
                                    <td>{{$curriculum->lec}}</td>
                                    <td>{{$curriculum->lab}}</td>
                                    <td>{{$curriculum->units}}</td>
                                    <td>{{$curriculum->period}}</td>
                                    <td>
                                        <a href="{{url('/admin/section_curriculum/remove',[$section->id, $offering->id])}}" class="btn btn-flat btn-danger btn-sm" title="Remove Subject" onclick="return confirm('Remove this subject from the section?')">
                                            <i class="fa fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> No curriculum subjects have been offered for this section yet.
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="box box-warning">
                <div class="box-header with-border">
                    <h3 class="box-title">Available Curriculum Subjects</h3>
                    <div class="box-tools pull-right">
                        <span class="label label-warning">{{count($availableCurricula)}} Subjects</span>
                    </div>
                </div>
                <div class="box-body">
                    @if(count($availableCurricula) > 0)
                    <form action="{{url('/admin/section_curriculum/add_selected',[$section->id])}}" method="post">
                        {{csrf_field()}}
                        <div class="form-group">
                            <button type="submit" class="btn btn-flat btn-success btn-block">
                                <i class="fa fa-plus-circle"></i> Add Selected Subjects
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="available-table">
                                <thead>
                                    <tr>
                                        <th width="10%">Select</th>
                                        <th width="20%">Code</th>
                                        <th width="70%">Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($availableCurricula as $curriculum)
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="curriculum_ids[]" value="{{$curriculum->id}}">
                                        </td>
                                        <td>{{$curriculum->course_code}}</td>
                                        <td>{{$curriculum->course_name}}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-flat btn-success btn-block">
                                <i class="fa fa-plus-circle"></i> Add Selected Subjects
                            </button>
                        </div>
                    </form>
                    @else
                    <div class="alert alert-success">
                        <i class="fa fa-check-circle"></i> All available curriculum subjects have been added to this section.
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('footer-script')
<script src='{{ asset('plugins/select2/select2.js') }}'></script>
<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%'
    });

    // Select all checkbox
    $('#select-all').on('click', function() {
        $('input[name="curriculum_ids[]"]').prop('checked', this.checked);
    });
});
</script>
@endsection
