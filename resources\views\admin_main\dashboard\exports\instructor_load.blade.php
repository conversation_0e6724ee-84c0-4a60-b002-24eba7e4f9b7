<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Instructor Load Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin-bottom: 5px;
        }
        .date {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
        }
        .high-load {
            background-color: #f8d7da;
        }
        .medium-load {
            background-color: #fff3cd;
        }
        .normal-load {
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Instructor Teaching Load Report</h1>
        <p>Generated on: {{ $date }}</p>
    </div>
    
    <div class="date">
        <p><strong>Report Summary:</strong> This report shows the teaching load (in hours per week) for all instructors in the system.</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>#</th>
                <th>Instructor Name</th>
                <th>Teaching Hours/Week</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach($loads as $index => $instructor)
                @php
                    $class = '';
                    $status = '';
                    if ($instructor['total_hours'] > 20) {
                        $class = 'high-load';
                        $status = 'High Load';
                    } elseif ($instructor['total_hours'] >= 15) {
                        $class = 'medium-load';
                        $status = 'Medium Load';
                    } else {
                        $class = 'normal-load';
                        $status = 'Normal Load';
                    }
                @endphp
                <tr class="{{ $class }}">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $instructor['instructor_name'] }}</td>
                    <td>{{ $instructor['total_hours'] }}</td>
                    <td>{{ $status }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="footer">
        <p>This is an automatically generated report. For any questions, please contact the system administrator.</p>
    </div>
</body>
</html>
