@if($availableSubjects->isEmpty())
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> No available subjects found for this section.
    </div>
@else
    <form id="addSelectedForm" action="{{ url('/superadmin/course_offerings/curriculum/section/add_selected', [$section->id]) }}" method="POST">
        {{ csrf_field() }}
        <div class="form-group">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Curriculum Year</label>
                        <select id="filter_curriculum_year" class="form-control select2" onchange="filterAvailableSubjects()">
                            <option value="">All Years</option>
                            @php
                                $years = \App\curriculum::where('program_code', $section->program_code)
                                    ->distinct()
                                    ->pluck('curriculum_year')
                                    ->toArray();
                            @endphp
                            @foreach($years as $year)
                                <option value="{{ $year }}" {{ $curriculum_year == $year ? 'selected' : '' }}>{{ $year }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Period</label>
                        <select id="filter_period" class="form-control select2" onchange="filterAvailableSubjects()">
                            <option value="">All Periods</option>
                            @php
                                $periods = \App\curriculum::where('program_code', $section->program_code)
                                    ->distinct()
                                    ->pluck('period')
                                    ->toArray();
                            @endphp
                            @foreach($periods as $p)
                                <option value="{{ $p }}" {{ $period == $p ? 'selected' : '' }}>{{ $p }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" id="selectAll"> Select All
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="5%">Select</th>
                        <th width="15%">Course Code</th>
                        <th width="40%">Course Name</th>
                        <th width="10%">Lec</th>
                        <th width="10%">Lab</th>
                        <th width="10%">Units</th>
                        <th width="10%">Period</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($availableSubjects as $subject)
                        <tr class="subject-row" 
                            data-curriculum-year="{{ $subject->curriculum_year }}" 
                            data-period="{{ $subject->period }}">
                            <td>
                                <input type="checkbox" name="curriculum_ids[]" value="{{ $subject->id }}">
                            </td>
                            <td>{{ $subject->course_code }}</td>
                            <td>{{ $subject->course_name }}</td>
                            <td>{{ $subject->lec }}</td>
                            <td>{{ $subject->lab }}</td>
                            <td>{{ $subject->units }}</td>
                            <td>{{ $subject->period }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </form>
    
    <script>
        $(document).ready(function() {
            // Initialize select2
            $('.select2').select2();
            
            // Handle select all checkbox
            $('#selectAll').on('change', function() {
                // Only check visible rows
                $('tr.subject-row:visible input[name="curriculum_ids[]"]').prop('checked', $(this).prop('checked'));
            });
        });
        
        function filterAvailableSubjects() {
            var curriculumYear = $('#filter_curriculum_year').val();
            var period = $('#filter_period').val();
            
            // Reload the available subjects with filters
            loadAvailableSubjects(curriculumYear, period);
        }
    </script>
@endif
