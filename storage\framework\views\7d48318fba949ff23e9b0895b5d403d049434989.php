<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>



<?php $__env->startSection('main-content'); ?>
<section class="content-header">
    <h1><i class="fa fa-calendar"></i>
        Faculty Schedule
        <small><?php echo e($instructorUser->name); ?> <?php echo e($instructorUser->lastname); ?></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/')); ?>"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="<?php echo e(route('collegeadmin.faculty_loading.index')); ?>">Faculty Loading</a></li>
        <li class="active">Faculty Schedule</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><?php echo e($instructorUser->name); ?> <?php echo e($instructorUser->middlename); ?> <?php echo e($instructorUser->lastname); ?></h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-primary btn-sm load-courses" data-instructor="<?php echo e($instructor); ?>">
                            <i class="fa fa-plus"></i> Course Schedule
                        </button>
                        <button type="button" class="btn btn-success btn-sm" id="print-schedule">
                            <i class="fa fa-print"></i> Print Schedule
                        </button>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 150px;">ID</th>
                                    <td><?php echo e($instructorUser->username ?? $instructorUser->id); ?></td>
                                </tr>
                                <tr>
                                    <th>Name</th>
                                    <td><?php echo e(strtoupper($instructorUser->lastname)); ?>, <?php echo e(strtoupper($instructorUser->name)); ?> <?php echo e(strtoupper($instructorUser->middlename)); ?></td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td><?php echo e($instructorUser->email); ?></td>
                                </tr>
                                <tr>
                                    <th>Faculty Status</th>
                                    <td><?php echo e($info ? $info->employee_type : 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Department</th>
                                    <td><?php echo e($info ? $info->department : 'N/A'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if(Session::has('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(Session::get('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(Session::has('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(Session::get('error')); ?>

                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-8">
                            <div id="calendar"></div>
                        </div>
                        <div class="col-md-4">
                            <div class="box box-info">
                                <div class="box-header with-border">
                                    <h3 class="box-title">Current Teaching Load</h3>
                                </div>
                                <div class="box-body">
                                    <?php if(count($schedules) > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Course</th>
                                                        <th>Schedule</th>
                                                        <th>Room</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td><?php echo e($schedule->course_code); ?></td>
                                                            <td>
                                                                <?php echo e($schedule->sched_day); ?>

                                                                <?php echo e(date('h:i A', strtotime($schedule->sched_from))); ?> -
                                                                <?php echo e(date('h:i A', strtotime($schedule->sched_to))); ?>

                                                            </td>
                                                            <td><?php echo e($schedule->room); ?></td>
                                                            <td>
                                                                <button type="button" class="btn btn-danger btn-xs remove-load" data-schedule="<?php echo e($schedule->id); ?>">
                                                                    <i class="fa fa-trash"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info">
                                            No teaching load assigned yet.
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <a href="<?php echo e(route('collegeadmin.faculty_loading.index')); ?>" class="btn btn-default">Back to List</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for adding courses -->
    <div class="modal fade" id="addCourseModal" tabindex="-1" role="dialog" aria-labelledby="addCourseModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="addCourseModalLabel">Course Schedule Management</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="search-course">Search Course</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search-course" placeholder="Enter course code or name">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" id="search-course-btn">Search</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="available-courses">
                        <p>Search for courses to add to faculty load.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer-script'); ?>
<link rel="stylesheet" href="<?php echo e(asset('plugins/fullcalendar/fullcalendar.min.css')); ?>">
<script src="<?php echo e(asset('plugins/fullcalendar/moment.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/fullcalendar/fullcalendar.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/jQueryUI/jquery-ui.min.js')); ?>"></script>
<style>
    .draggable-schedule {
        cursor: move;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 3px;
    }
    .callout {
        border-left: 5px solid #eee;
        border-radius: 3px;
    }
    .callout-info {
        border-left-color: #5bc0de;
        background-color: #f4f8fa;
    }
    .callout-warning {
        border-left-color: #f0ad4e;
        background-color: #fcf8f2;
    }
    .callout-success {
        border-left-color: #5cb85c;
        background-color: #f3f8f3;
    }
    .callout-danger {
        border-left-color: #d9534f;
        background-color: #fdf7f7;
    }
    .fc-event {
        cursor: pointer;
    }
    .ui-draggable-dragging {
        z-index: 9999 !important;
    }
    .loading-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 10000;
    }
    .conflict-warning {
        background-color: #fcf8e3;
        border: 1px solid #faebcc;
        color: #8a6d3b;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
    }
</style>
<script>
$(function() {
    // Initialize calendar with droppable functionality
    $('#calendar').fullCalendar({
        header: {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek,agendaDay'
        },
        defaultView: 'agendaWeek',
        minTime: '07:00:00',
        maxTime: '22:00:00',
        hiddenDays: [0], // Hide Sunday
        firstDay: 1, // Monday as first day
        height: 500,
        allDaySlot: false,
        columnFormat: 'ddd',
        editable: false,
        droppable: true, // Allow external events to be dropped onto the calendar
        drop: function(date, jsEvent, ui, resourceId) {
            // Called when a draggable item is dropped onto the calendar
            var $originalItem = $(ui.draggable);
            var eventData = $originalItem.data('event');

            if (!eventData) {
                alert('Error: Invalid course data.');
                return;
            }

            var offeringId = eventData.offering;
            var instructorId = '<?php echo e($instructor); ?>';
            var courseTitle = eventData.title;

            // Show loading indicator
            var $loadingIndicator = $('<div class="loading-overlay"><i class="fa fa-spinner fa-spin"></i> Adding course...</div>');
            $('#calendar').append($loadingIndicator);

            // Check for conflicts first
            $.ajax({
                url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/check_conflicts')); ?>",
                type: 'GET',
                data: {
                    instructor_id: instructorId,
                    offering_id: offeringId
                },
                success: function(conflictData) {
                    if (conflictData.hasConflicts) {
                        $loadingIndicator.remove();
                        var conflictMessage = 'Schedule conflict detected with:\n';
                        conflictData.conflicts.forEach(function(conflict) {
                            conflictMessage += '- ' + conflict + '\n';
                        });
                        conflictMessage += '\nDo you want to proceed anyway?';

                        if (confirm(conflictMessage)) {
                            addCourseToLoad(instructorId, offeringId, courseTitle, true);
                        }
                    } else {
                        addCourseToLoad(instructorId, offeringId, courseTitle, false);
                    }
                },
                error: function() {
                    $loadingIndicator.remove();
                    // Proceed without conflict check if the endpoint doesn't exist
                    addCourseToLoad(instructorId, offeringId, courseTitle, false);
                }
            });
        },
        events: [
            <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    // Map day letters to day numbers
                    $dayMap = [
                        'M' => 1, // Monday
                        'T' => 2, // Tuesday
                        'W' => 3, // Wednesday
                        'H' => 4, // Thursday
                        'F' => 5, // Friday
                        'S' => 6, // Saturday
                    ];

                    // Parse schedule days
                    $days = str_split($schedule->sched_day);
                ?>

                <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(isset($dayMap[$day])): ?>
                        {
                            id: '<?php echo e($schedule->id); ?>',
                            title: '<?php echo e($schedule->course_code); ?> (<?php echo e($schedule->room); ?>)',
                            start: '<?php echo e(date("Y-m-d", strtotime("this week +" . ($dayMap[$day] - 1) . " days"))); ?>T<?php echo e($schedule->sched_from); ?>',
                            end: '<?php echo e(date("Y-m-d", strtotime("this week +" . ($dayMap[$day] - 1) . " days"))); ?>T<?php echo e($schedule->sched_to); ?>',
                            backgroundColor: '#3c8dbc',
                            borderColor: '#3c8dbc',
                        },
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        ],
        eventClick: function(calEvent, jsEvent, view) {
            // Handle click on calendar event (for removing)
            if (confirm('Do you want to remove this course from the faculty load?')) {
                var scheduleId = calEvent.id;

                $.ajax({
                    url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/remove_faculty_load')); ?>",
                    type: 'GET',
                    data: { schedule_id: scheduleId },
                    success: function(data) {
                        if (data.success) {
                            alert('Course removed from faculty load successfully!');
                            location.reload(); // Reload the page to update the schedule
                        } else {
                            alert('Error: ' + data.message);
                        }
                    },
                    error: function() {
                        alert('Error removing course from faculty load.');
                    }
                });
            }
        }
    });

    // Helper function to add course to faculty load
    function addCourseToLoad(instructorId, offeringId, courseTitle, override) {
        var $loadingIndicator = $('.loading-overlay');
        if ($loadingIndicator.length === 0) {
            $loadingIndicator = $('<div class="loading-overlay"><i class="fa fa-spinner fa-spin"></i> Adding course...</div>');
            $('#calendar').append($loadingIndicator);
        }

        $.ajax({
            url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/add_faculty_load')); ?>",
            type: 'GET',
            data: {
                instructor_id: instructorId,
                offering_id: offeringId,
                override: override ? 1 : 0
            },
            success: function(data) {
                $loadingIndicator.remove();
                if (data.success) {
                    // Show success message
                    showNotification('success', 'Course "' + courseTitle + '" added to faculty load successfully!');

                    // Refresh the calendar and teaching load table
                    refreshScheduleData();

                    // Close modal if open
                    $('#addCourseModal').modal('hide');
                } else {
                    showNotification('error', 'Error: ' + data.message);
                }
            },
            error: function(xhr, status, error) {
                $loadingIndicator.remove();
                showNotification('error', 'Error adding course to faculty load: ' + error);
            }
        });
    }

    // Helper function to show notifications
    function showNotification(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        var notification = $('<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 10001; min-width: 300px;">' +
            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<i class="fa ' + iconClass + '"></i> ' + message +
            '</div>');

        $('body').append(notification);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Helper function to refresh schedule data
    function refreshScheduleData() {
        // Reload the page to update both calendar and teaching load table
        // In a more sophisticated implementation, we could update just the specific parts
        location.reload();
    }

    // Initialize draggable functionality for search results
    function initDraggableItems() {
        $('.draggable-schedule').each(function() {
            // Initialize draggable
            $(this).draggable({
                zIndex: 999,
                revert: true,
                revertDuration: 0,
                helper: 'clone',
                appendTo: 'body',
                scroll: false,
                start: function(event, ui) {
                    $(this).css('z-index', 1000);
                }
            });

            // Store event data
            var eventObject = JSON.parse($(this).attr('data-event'));
            $(this).data('event', eventObject);
        });
    }

    // Handle click on Add Course button
    $('.load-courses').click(function() {
        var instructorId = $(this).data('instructor');
        $('#addCourseModal').data('instructor', instructorId);
        $('#addCourseModal').modal('show');
    });

    // Handle search button click
    $('#search-course-btn').click(function() {
        var searchTerm = $('#search-course').val();
        var instructorId = $('#addCourseModal').data('instructor');

        if (searchTerm.length < 2) {
            alert('Please enter at least 2 characters to search.');
            return;
        }

        $.ajax({
            url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/search_courses')); ?>",
            type: 'GET',
            data: {
                search: searchTerm,
                instructor_id: instructorId
            },
            success: function(data) {
                $('#available-courses').html(data);
                // Initialize draggable items after loading search results
                initDraggableItems();
            },
            error: function() {
                $('#available-courses').html('<p class="text-danger">Error searching for courses.</p>');
            }
        });
    });

    // Handle Enter key in search box
    $('#search-course').keypress(function(e) {
        if (e.which == 13) {
            $('#search-course-btn').click();
            return false;
        }
    });

    // Handle add course to faculty load (delegated event)
    $(document).on('click', '.add-to-load', function() {
        var instructorId = $('#addCourseModal').data('instructor');
        var offeringId = $(this).data('offering');

        $.ajax({
            url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/add_faculty_load')); ?>",
            type: 'GET',
            data: {
                instructor_id: instructorId,
                offering_id: offeringId
            },
            success: function(data) {
                if (data.success) {
                    alert('Course added to faculty load successfully!');
                    location.reload(); // Reload the page to update the schedule
                } else {
                    alert('Error: ' + data.message);
                }
            },
            error: function() {
                alert('Error adding course to faculty load.');
            }
        });
    });

    // Handle remove course from faculty load
    $('.remove-load').click(function() {
        if (confirm('Are you sure you want to remove this course from the faculty load?')) {
            var scheduleId = $(this).data('schedule');

            $.ajax({
                url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/remove_faculty_load')); ?>",
                type: 'GET',
                data: { schedule_id: scheduleId },
                success: function(data) {
                    if (data.success) {
                        alert('Course removed from faculty load successfully!');
                        location.reload(); // Reload the page to update the schedule
                    } else {
                        alert('Error: ' + data.message);
                    }
                },
                error: function() {
                    alert('Error removing course from faculty load.');
                }
            });
        }
    });

    // Handle print schedule
    $('#print-schedule').click(function() {
        window.print();
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($layout, array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>