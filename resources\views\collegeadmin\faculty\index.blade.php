
<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-users"></i>
        Faculty Management
        <small>{{ isset($college) ? $college->college_name : Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Management</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Members</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.faculty.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Add New Faculty
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    <!-- Search and Filter Section -->
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="department-filter">Filter by Department:</label>
                                <select id="department-filter" class="form-control">
                                    <option value="">All Departments</option>
                                    @php
                                        $departments = $faculty->pluck('instructorInfo.department')->filter()->unique()->sort();
                                    @endphp
                                    @foreach($departments as $department)
                                        <option value="{{ $department }}">{{ $department }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status-filter">Filter by Faculty Status:</label>
                                <select id="status-filter" class="form-control">
                                    <option value="">All Status</option>
                                    @php
                                        $statuses = $faculty->pluck('instructorInfo.employee_type')->filter()->unique()->sort();
                                    @endphp
                                    @foreach($statuses as $status)
                                        <option value="{{ $status }}">{{ $status }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    @if(count($faculty) > 0)
                        <div class="table-responsive">
                            <table id="faculty-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID Number</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>College</th>
                                        <th>Department</th>
                                        <th>Faculty Status</th>
                                        <th>Contact</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($faculty as $member)
                                        @php
                                            $info = $member->instructorInfo;
                                        @endphp
                                        <tr>
                                            <td>{{ $member->username ?? $member->id }}</td>
                                            <td>
                                                <strong>{{ strtoupper($member->lastname) }}, {{ strtoupper($member->name) }}</strong>
                                                @if($member->middlename)
                                                    {{ strtoupper($member->middlename) }}
                                                @endif
                                                @if($member->extensionname)
                                                    {{ strtoupper($member->extensionname) }}
                                                @endif
                                            </td>
                                            <td>{{ $member->email }}</td>
                                            <td>
                                                <span class="label label-primary">{{ $college->college_name ?? $member->college_code }}</span>
                                            </td>
                                            <td>{{ $info ? $info->department : 'N/A' }}</td>
                                            <td>
                                                @if($info && $info->employee_type)
                                                    <span class="label label-success">{{ $info->employee_type }}</span>
                                                @else
                                                    <span class="label label-default">N/A</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($info && $info->cell_no)
                                                    <small><i class="fa fa-phone"></i> {{ $info->cell_no }}</small>
                                                @else
                                                    <small class="text-muted">No contact</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ route('collegeadmin.faculty.show', $member->id) }}" class="btn btn-info btn-xs" title="View Details">
                                                        <i class="fa fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('collegeadmin.faculty.edit', $member->id) }}" class="btn btn-primary btn-xs" title="Edit Faculty">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>
                                                    <a href="{{ route('collegeadmin.faculty_loading.generate_schedule', $member->id) }}" class="btn btn-success btn-xs" title="View Schedule">
                                                        <i class="fa fa-calendar"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-xs" title="Deactivate Faculty" onclick="confirmDeactivate({{ $member->id }})">
                                                        <i class="fa fa-ban"></i>
                                                    </button>
                                                </div>

                                                <!-- Hidden form for deletion -->
                                                <form id="deactivate-form-{{ $member->id }}" action="{{ route('collegeadmin.faculty.destroy', $member->id) }}" method="POST" style="display: none;">
                                                    @csrf
                                                    @method('DELETE')
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No faculty members found for your college.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script src="{{ asset('plugins/datatables/jquery.dataTables.js') }}"></script>
<script src="{{ asset('plugins/datatables/dataTables.bootstrap.js') }}"></script>
<script>
    $(function() {
        // Initialize DataTable
        var table = $('#faculty-table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "pageLength": 25,
            "order": [[ 1, "asc" ]], // Sort by name column
            "columnDefs": [
                { "orderable": false, "targets": 7 } // Disable sorting on Actions column
            ]
        });

        // Department filter
        $('#department-filter').on('change', function() {
            var department = this.value;
            if (department) {
                table.column(4).search('^' + department + '$', true, false).draw();
            } else {
                table.column(4).search('').draw();
            }
        });

        // Status filter
        $('#status-filter').on('change', function() {
            var status = this.value;
            if (status) {
                table.column(5).search(status).draw();
            } else {
                table.column(5).search('').draw();
            }
        });
    });

    // Confirmation dialog for faculty deactivation
    function confirmDeactivate(facultyId) {
        if (confirm('Are you sure you want to deactivate this faculty member? This action will remove their access to the system.')) {
            document.getElementById('deactivate-form-' + facultyId).submit();
        }
    }

    // Show success/error messages with auto-hide
    $(document).ready(function() {
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    });
</script>
@endpush

