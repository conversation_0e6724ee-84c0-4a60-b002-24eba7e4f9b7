<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\CtrRoom;
use App\room_schedules;
use Carbon\Carbon;

class DashboardAjaxController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Get schedule data for the calendar
     */
    public function getScheduleData(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get start and end dates from request
        $start = $request->input('start');
        $end = $request->input('end');
        
        // Get schedules for this college
        $schedules = room_schedules::join('offerings_infos', 'room_schedules.offering_id', '=', 'offerings_infos.id')
            ->join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
            ->where('curricula.college_code', $collegeCode)
            ->where('room_schedules.is_active', 1)
            ->get();
        
        $events = [];
        
        foreach ($schedules as $schedule) {
            // Get schedule details
            $roomName = $schedule->room;
            $courseName = $schedule->course_code ?? 'Unknown Course';
            $instructorName = 'No Instructor';
            
            if ($schedule->instructor) {
                $instructor = \App\User::find($schedule->instructor);
                if ($instructor) {
                    $instructorName = $instructor->name . ' ' . $instructor->lastname;
                }
            }
            
            // Parse schedule days
            $days = str_split($schedule->sched_day);
            
            // Map day numbers to day names
            $dayMap = [
                'M' => 1, // Monday
                'T' => 2, // Tuesday
                'W' => 3, // Wednesday
                'H' => 4, // Thursday
                'F' => 5, // Friday
                'S' => 6, // Saturday
            ];
            
            // Create events for each day in the schedule
            foreach ($days as $day) {
                if (isset($dayMap[$day])) {
                    $dayNumber = $dayMap[$day];
                    
                    // Parse start and end times
                    $startTime = Carbon::parse($schedule->sched_from)->format('H:i:s');
                    $endTime = Carbon::parse($schedule->sched_to)->format('H:i:s');
                    
                    // Calculate the date for this day of the week
                    $date = Carbon::parse($start)->startOfWeek()->addDays($dayNumber - 1)->format('Y-m-d');
                    
                    // Create event
                    $events[] = [
                        'id' => $schedule->id,
                        'title' => $courseName . '<br>' . $roomName . '<br>' . $instructorName,
                        'start' => $date . 'T' . $startTime,
                        'end' => $date . 'T' . $endTime,
                        'backgroundColor' => '#3c8dbc',
                        'borderColor' => '#3c8dbc',
                    ];
                }
            }
        }
        
        return response()->json($events);
    }

    /**
     * Get room availability data
     */
    public function getRoomAvailability()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get rooms for this college
        $rooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->get();
        
        // Get current day of week (1 = Monday, 7 = Sunday)
        $today = Carbon::now()->dayOfWeek;
        if ($today == 0) $today = 7; // Convert Sunday from 0 to 7
        
        // Map day numbers to day letters used in the system
        $dayMap = [
            1 => 'M', // Monday
            2 => 'T', // Tuesday
            3 => 'W', // Wednesday
            4 => 'H', // Thursday
            5 => 'F', // Friday
            6 => 'S', // Saturday
            7 => null, // Sunday (not used)
        ];
        
        $dayLetter = $dayMap[$today];
        
        // Current time
        $currentTime = Carbon::now()->format('H:i:s');
        
        $roomData = [];
        
        foreach ($rooms as $room) {
            // Skip if today is Sunday (no classes)
            if (!$dayLetter) continue;
            
            // Check if room is currently occupied
            $occupied = room_schedules::where('room', $room->room)
                ->where('is_active', 1)
                ->where('sched_day', 'like', '%' . $dayLetter . '%')
                ->where('sched_from', '<=', $currentTime)
                ->where('sched_to', '>=', $currentTime)
                ->first();
            
            $roomData[] = [
                'room' => $room->room,
                'building' => $room->building,
                'status' => $occupied ? 'Occupied' : 'Available',
                'class' => $occupied ? 'danger' : 'success',
            ];
        }
        
        return view('collegeadmin.ajax.room_availability', compact('roomData'));
    }
}
