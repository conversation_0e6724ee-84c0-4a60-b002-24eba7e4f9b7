<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixRoomSchedulesIdAutoIncrement extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, check if the id column exists and is not auto-increment
        $idColumn = DB::select("SHOW COLUMNS FROM room_schedules WHERE Field = 'id'");

        if (!empty($idColumn) && $idColumn[0]->Extra != 'auto_increment') {
            // Drop the primary key if it exists
            Schema::table('room_schedules', function (Blueprint $table) {
                // Check if id is a primary key
                if (Schema::hasColumn('room_schedules', 'id')) {
                    $table->dropPrimary();
                }
            });

            // Modify the id column to be auto-increment
            DB::statement('ALTER TABLE room_schedules MODIFY id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY');
        } else if (empty($idColumn)) {
            // If id column doesn't exist, add it
            Schema::table('room_schedules', function (Blueprint $table) {
                $table->increments('id')->first();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This is a fix for the database structure, so we don't want to undo it
        // But we'll provide a method to revert if needed
        // Schema::table('room_schedules', function (Blueprint $table) {
        //     $table->dropPrimary();
        //     $table->integer('id')->change();
        // });
    }
}
