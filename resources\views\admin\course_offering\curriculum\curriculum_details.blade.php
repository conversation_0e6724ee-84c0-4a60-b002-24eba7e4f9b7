<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-book"></i>
        {{ $program->program_name }} - {{ $curriculum_year }} Curriculum
        <small></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/admin/course_offerings/curriculum')}}">Course Offering Curriculum</a></li>
        <li><a href="{{url('/admin/course_offerings/curriculum/program', [$program->program_code])}}">{{ $program->program_name }}</a></li>
        <li class="active">{{ $curriculum_year }} Curriculum</li>
      </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            @if(Session::has('success'))
                <div class="alert alert-success">
                    {{Session::get('success')}}
                </div>
            @endif
            @if(Session::has('error'))
                <div class="alert alert-danger">
                    {{Session::get('error')}}
                </div>
            @endif
            @if(Session::has('warning'))
                <div class="alert alert-warning">
                    {{Session::get('warning')}}
                </div>
            @endif
            @if(Session::has('info'))
                <div class="alert alert-info">
                    {{Session::get('info')}}
                </div>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">{{ $program->program_name }} - {{ $curriculum_year }} Curriculum</h3>
                </div>
                <div class="box-body">
                    @if($curricula->isEmpty())
                        <div class="alert alert-info">
                            No curriculum subjects found for this program and year.
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Level</th>
                                        <th>Period</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($curricula as $curriculum)
                                        <tr>
                                            <td>{{ $curriculum->course_code }}</td>
                                            <td>{{ $curriculum->course_name }}</td>
                                            <td>{{ $curriculum->level }}</td>
                                            <td>{{ $curriculum->period }}</td>
                                            <td>{{ $curriculum->lec }}</td>
                                            <td>{{ $curriculum->lab }}</td>
                                            <td>{{ $curriculum->units }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<script src="{{asset('plugins/select2/select2.js')}}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
@endsection
