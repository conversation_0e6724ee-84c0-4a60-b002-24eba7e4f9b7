<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Support\Facades\DB;
use PDF;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Helper;
use Illuminate\Support\Facades\Redirect;

class CurriculumController extends Controller
{

        public function __construct()
        {
                $this->middleware('auth');
                $this->middleware('checkIfActivated');
                $this->middleware('admin');
        }

        function index()
        {
                // Get all active colleges
                $colleges = \App\College::where('is_active', 1)->orderBy('college_code')->get();

                // Create an array to store colleges with their programs
                $collegesWithPrograms = [];

                foreach ($colleges as $college) {
                        // Get programs for this college
                        $programsList = $college->getPrograms();

                        // Only include colleges that have programs
                        if (count($programsList) > 0) {
                                $collegesWithPrograms[] = [
                                        'college' => $college,
                                        'programs' => $programsList
                                ];
                        }
                }

                // Get all programs using ProgramHelper
                $programs = \App\ProgramHelper::getAllPrograms();

                return view('/admin/curriculum_management/curriculum', compact('collegesWithPrograms', 'programs', 'colleges'));
        }

        function edit_curriculum(Request $request)
        {
                $curriculum = \App\curriculum::find($request->curriculum_id);
                $curriculum->course_code = $request->course_code;
                $curriculum->course_name = $request->course_name;
                $curriculum->lec = $request->lec;
                $curriculum->lab = $request->lab;
                $curriculum->units = $request->units;
                $curriculum->is_complab = $request->complab;
                $curriculum->update();

                Session::flash('success', 'Successfully modified ' . $request->course_code);
                return redirect(url('/admin/curriculum_management/list_curriculum', array($curriculum->program_code, $curriculum->curriculum_year)));
        }

        public function viewcurricula($program_code, Request $request)
        {
                // Check if we should show archived curricula
                $showArchived = $request->has('show_archived') && $request->show_archived == 1;

                // Get program using ProgramHelper
                $program = \App\ProgramHelper::getProgramByCode($program_code);

                // If not found in academic_programs, try to get from curriculum table
                if (!$program) {
                        $program = \App\curriculum::where('program_code', $program_code)->first();
                }

                // If still not found, create a basic program object with the code
                if (!$program) {
                        // Try to get program name from program_names table
                        $programName = DB::table('program_names')
                                ->where('program_code', $program_code)
                                ->first();

                        // Create a program object with available information
                        $program = (object) [
                                'program_code' => $program_code,
                                'program_name' => $programName ? $programName->program_name : $program_code
                        ];
                }

                // Get curricula based on active status
                $query = \App\curriculum::distinct()
                        ->where('program_code', $program_code);

                if (!$showArchived) {
                        // Only show active curricula
                        $query->where('is_active', 1);
                } else {
                        // Only show archived curricula
                        $query->where('is_active', 0);
                }

                $curricula = $query->get(['curriculum_year']);

                if ($request->ajax()) {
                        return view(
                                '/admin/curriculum_management/ajax/refresh_curriculum',
                                compact('curricula', 'program', 'program_code', 'showArchived')
                        );
                }

                return view(
                        '/admin/curriculum_management/view_curriculums',
                        compact('curricula', 'program', 'program_code', 'showArchived')
                );
        }

        function listcurriculum($program_code, $curriculum_year)
        {

                return view('/admin/curriculum_management/list_curriculum', compact('program_code', 'curriculum_year'));
        }

        function print_curriculum($program_code, $curriculum_year)
        {
                // Get program name from program_names table or use program code as fallback
                $programName = DB::table('program_names')
                        ->where('program_code', $program_code)
                        ->first();
                $programNameStr = $programName ? $programName->program_name : $program_code;

                // Find which college this program belongs to
                $collegeCode = null;
                $colleges = \App\College::all();
                foreach ($colleges as $college) {
                        if ($college->hasProgram($program_code)) {
                                $collegeCode = $college->college_code;
                                break;
                        }
                }

                // Create a program object to match the expected format
                $program = (object) [
                        'program_code' => $program_code,
                        'program_name' => $programNameStr,
                        'college_code' => $collegeCode
                ];

                $pdf = PDF::loadView('reg_college.curriculum_management.print_curriculum', compact('program_code', 'curriculum_year', 'program'));
                $pdf->setPaper('legal', 'portrait');
                return $pdf->stream("curriculum_{$program_code}_{$curriculum_year}.pdf");

        }

        function add_course(Request $request)
        {
                $curricula = new \App\curriculum;
                $curricula->curriculum_year = $request->curriculum_year;
                $curricula->program_code = $request->program_code;

                // Get program name from program_names table or use program code as fallback
                $programName = DB::table('program_names')
                        ->where('program_code', $request->program_code)
                        ->first();
                $curricula->program_name = $programName ? $programName->program_name : $request->program_code;

                // Find which college this program belongs to
                $foundCollege = false;
                if (isset($request->college_code) && !empty($request->college_code)) {
                        // If college_code is directly provided in the form, use it
                        $curricula->college_code = $request->college_code;
                        $foundCollege = true;

                        // Check if program needs to be added to this college
                        $college = \App\College::where('college_code', $request->college_code)->first();
                        if ($college && !$college->hasProgram($request->program_code)) {
                                // Add program to college description
                                $college->addProgram($request->program_code, $curricula->program_name);
                        }
                } else {
                        // Try to find a college that has this program
                        $colleges = \App\College::all();
                        foreach ($colleges as $college) {
                                if ($college->hasProgram($request->program_code)) {
                                        $curricula->college_code = $college->college_code;
                                        $foundCollege = true;
                                        break;
                                }
                        }
                }

                // If no college found, assign to unassigned college
                if (!$foundCollege) {
                        $unassignedCollege = \App\College::firstOrCreate(
                                ['college_code' => 'UNASSIGNED'],
                                [
                                        'college_name' => 'Unassigned College',
                                        'description' => $request->program_code,
                                        'is_active' => 1
                                ]
                        );

                        $curricula->college_code = 'UNASSIGNED';

                        // Add program to unassigned college if not already there
                        if (!$unassignedCollege->hasProgram($request->program_code)) {
                                $unassignedCollege->addProgram($request->program_code, $curricula->program_name);
                        }
                }

                // Save program name if it doesn't exist in program_names table
                if (!$programName && $curricula->program_name != $request->program_code) {
                        \App\ProgramName::updateOrCreate(
                                ['program_code' => $request->program_code],
                                ['program_name' => $curricula->program_name]
                        );
                }

                $curricula->control_code = $request->course_code;
                $curricula->course_code = $request->course_code;
                $curricula->course_name = $request->course_name;
                $curricula->lec = $request->lec;
                $curricula->lab = $request->lab;
                $curricula->units = $request->units;
                $curricula->level = $request->level;
                $curricula->period = $request->period;
                $curricula->percent_tuition = 100;
                $curricula->is_complab = 0;
                $curricula->is_active = 1; // Set as active by default
                $curricula->save();

                Helper::addLogs('User ' . Helper::getName(Auth::user()->idno) . "Added a new Course " . $request->course_code . " to the Curricula of " . $request->program_code);
                Log::info('User ' . Helper::getName(Auth::user()->idno) . "Added a new Course " . $request->course_code . " to the Curricula of " . $request->program_code);
                Session::flash('success', 'Successfully Added a Course to the Curriculum of ' . $request->program_code);
                return redirect(url('/admin/curriculum_management/curriculum'));

        }

        function view_course_curriculum($curriculum_id)
        {

                $curricula = \App\Curriculum::find($curriculum_id);
                return view('reg_college.curriculum_management.display_course_curriculum', compact('curricula'));

        }

        function update_course_curriculum(Request $request, $id)
        {

                $curricula = \App\Curriculum::find($id);
                $curricula->curriculum_year = $request->curriculum_year;
                $curricula->control_code = $request->course_code;
                $curricula->course_code = $request->course_code;
                $curricula->course_name = $request->course_name;
                $curricula->lec = $request->lec;
                $curricula->lab = $request->lab;
                $curricula->units = $request->units;
                $curricula->display_lec = $request->lec;
                $curricula->display_lab = $request->lab;
                $curricula->display_units = $request->units;
                $curricula->level = $request->level;
                $curricula->period = $request->period;
                $curricula->update();

                Helper::addLogs('User ' . Helper::getName(Auth::user()->idno) . "Modifed a Course " . $request->course_code . " to the Curricula");
                Log::info('User ' . Helper::getName(Auth::user()->idno) . "Modifed Course " . $request->course_code . " to the Curricula");
                Session::flash('success', 'Successfully Modifed a Course to the Curriculum');
                return redirect(url('/registrar_college/curriculum_management/curriculum'));

        }

        function prerequisites()
        {
                // prerequisites??
                $lists = \App\Prerequisite::all();
                return view('reg_college.curriculum_management.prerequisites', compact('lists'));

        }

        function post_prerequisites(Request $request)
        {
                // post prerequisites??
                $check_if_exists = \App\Prerequisite::where('Curriculum_year', $request->curriculum_year)
                        ->where('Course_Code', $request->program_code)
                        ->where('Subject_Code', $request->course_code)
                        ->where('Prerequisite', $request->prerequisite)->get();

                if (count($check_if_exists) == 0) {
                        $new = new \App\Prerequisite;
                        $new->Course_Code = $request->program_code;
                        // Get program name from program_names table or use program code as fallback
                        $programName = DB::table('program_names')
                                ->where('program_code', $request->program_code)
                                ->first();
                        $new->Course = $programName ? $programName->program_name : $request->program_code;
                        $new->Subject_Code = $request->course_code;
                        $new->Prerequisite = $request->prerequisite;
                        $new->Curriculum_year = $request->curriculum_year;
                        $new->save();

                        Helper::addLogs(Helper::getName(Auth::user()->idno) . ' added a prerequisite subject');
                        Log::info(Helper::getName(Auth::user()->idno) . ' added a prerequisite subject');
                        Session::flash('success', 'Added Prerequisite');
                        return redirect(url('/registrar_college/curriculum_management/prerequisites'));
                } else {
                        Session::flash('error', 'Prerequisite Already Exists!');
                        return Redirect::back();
                }

        }

        public function archive_curriculum(Request $request)
        {
                // Get parameters from request
                $curriculum_year = $request->curriculum_year;
                $program_code = $request->program_code;

                // Validate required parameters
                if (empty($curriculum_year) || empty($program_code)) {
                        return response()->json([
                                'success' => false,
                                'message' => 'Missing required parameters.'
                        ]);
                }

                // Get all curriculum entries for this program and year
                $curricula = \App\curriculum::where('program_code', $program_code)
                        ->where('curriculum_year', $curriculum_year)
                        ->get();

                if (count($curricula) > 0) {
                        foreach ($curricula as $curriculum) {
                                // Toggle the is_active status
                                $curriculum->is_active = !$curriculum->is_active;
                                $curriculum->save();
                        }

                        $status = $curricula[0]->is_active ? 'restored' : 'archived';
                        $message = "Curriculum for {$program_code} ({$curriculum_year}) has been {$status}.";

                        // Log the action
                        Log::info("User " . Auth::user()->name . " {$status} curriculum for {$program_code} ({$curriculum_year})");

                        return response()->json([
                                'success' => true,
                                'message' => $message
                        ]);
                } else {
                        return response()->json([
                                'success' => false,
                                'message' => 'Curriculum not found.'
                        ]);
                }
        }

        public function archived_subjects()
        {
                // Get all archived curricula
                $archivedCurricula = \App\curriculum::where('is_active', 0)
                        ->orderBy('program_code')
                        ->orderBy('curriculum_year')
                        ->get();

                // Group by program code and curriculum year
                $groupedCurricula = [];
                foreach ($archivedCurricula as $curriculum) {
                        $key = $curriculum->program_code . '-' . $curriculum->curriculum_year;
                        if (!isset($groupedCurricula[$key])) {
                                $groupedCurricula[$key] = [
                                        'program_code' => $curriculum->program_code,
                                        'program_name' => $curriculum->program_name,
                                        'curriculum_year' => $curriculum->curriculum_year,
                                        'college_code' => $curriculum->college_code,
                                        'subjects' => []
                                ];
                        }
                        $groupedCurricula[$key]['subjects'][] = $curriculum;
                }

                return view('/admin/curriculum_management/archived_subjects', compact('groupedCurricula'));
        }

        /**
         * Fix curriculum course names
         * This method updates course names that are in the format "Course: XXX" to more descriptive names
         */
        public function fixCourseNames()
        {
                // Create a mapping of subject codes to descriptive names
                $subjectNames = [
                        // Computer Science/IT Subjects
                        'CSP107' => 'Introduction to Programming',
                        'CSP108' => 'Data Structures and Algorithms',
                        'CSP109' => 'Database Management Systems',
                        'CSA101' => 'Computer Architecture',
                        'CSA102' => 'Operating Systems',
                        'CSP110' => 'Web Development',
                        'CSP111' => 'Software Engineering',
                        'CSA103' => 'Computer Networks',
                        'CSA104' => 'Information Security',
                        'CSA105' => 'Mobile Application Development',
                        'CSE3' => 'Computer Ethics',
                        'CSP112' => 'Advanced Programming',
                        'CSP113' => 'Artificial Intelligence',

                        // Add more mappings as needed for other subjects
                ];

                $updatedCount = 0;
                $createdCount = 0;

                // First, ensure all subjects exist in the subjects table
                foreach ($subjectNames as $code => $name) {
                        $subject = \App\Subject::where('subject_code', $code)->first();

                        if ($subject) {
                                // Update the subject name if it's different
                                if ($subject->subject_name !== $name) {
                                        $subject->subject_name = $name;
                                        $subject->save();
                                        $updatedCount++;
                                }
                        } else {
                                // Create the subject if it doesn't exist
                                \App\Subject::create([
                                        'subject_code' => $code,
                                        'subject_name' => $name,
                                        'lec' => 3, // Default values
                                        'lab' => 0,
                                        'units' => 3,
                                        'is_complab' => 0
                                ]);
                                $createdCount++;
                        }
                }

                // Now update curriculum records
                $fixedCount = 0;

                // Find all curriculum records with course names in the format "Course: XXX"
                $coursePrefixRecords = \App\curriculum::where('course_name', 'like', 'Course: %')->get();

                foreach ($coursePrefixRecords as $curriculum) {
                        // Extract the course code from the name
                        $codeMatch = [];
                        if (preg_match('/Course: (.+)/', $curriculum->course_name, $codeMatch)) {
                                $extractedCode = $codeMatch[1];

                                // Check if we have a mapping for this code
                                if (isset($subjectNames[$extractedCode])) {
                                        $curriculum->course_name = $subjectNames[$extractedCode];

                                        // Also update the subject_id if possible
                                        $subject = \App\Subject::where('subject_code', $extractedCode)->first();
                                        if ($subject) {
                                                $curriculum->subject_id = $subject->id;
                                        }

                                        $curriculum->save();
                                        $fixedCount++;
                                }
                        }
                }

                // Also update any curriculum records that have matching course_code but no proper name
                foreach ($subjectNames as $code => $name) {
                        $curricula = \App\curriculum::where('course_code', $code)
                                ->where(function ($query) {
                                        $query->whereNull('course_name')
                                                ->orWhere('course_name', '')
                                                ->orWhere('course_name', 'like', 'Course: %');
                                })
                                ->get();

                        foreach ($curricula as $curriculum) {
                                $curriculum->course_name = $name;

                                // Also update the subject_id if possible
                                $subject = \App\Subject::where('subject_code', $code)->first();
                                if ($subject) {
                                        $curriculum->subject_id = $subject->id;
                                }

                                $curriculum->save();
                                $fixedCount++;
                        }
                }

                // Set flash message with results
                Session::flash('success', "Fixed {$fixedCount} curriculum course names. Created {$createdCount} new subjects and updated {$updatedCount} existing subjects.");

                // Redirect back to the curriculum management page
                return redirect()->back();
        }

}