<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Subject extends Model
{
    protected $table = 'subjects';

    protected $fillable = [
        'subject_code',
        'subject_name',
        'lec',
        'lab',
        'units',
        'is_complab'
    ];

    /**
     * Get all curricula that use this subject
     */
    public function curricula()
    {
        return $this->hasMany('App\curriculum', 'subject_id');
    }

    /**
     * Find a subject by its code or create it if it doesn't exist
     *
     * @param string $code
     * @param array $data
     * @return Subject
     */
    public static function findOrCreateByCode($code, $data = [])
    {
        $subject = self::where('subject_code', $code)->first();

        if (!$subject && !empty($data)) {
            $subject = self::create([
                'subject_code' => $code,
                'subject_name' => $data['subject_name'] ?? $data['course_name'] ?? '',
                'lec' => $data['lec'] ?? 0,
                'lab' => $data['lab'] ?? 0,
                'units' => $data['units'] ?? 0,
                'is_complab' => $data['is_complab'] ?? 0
            ]);
        }

        return $subject;
    }
}
