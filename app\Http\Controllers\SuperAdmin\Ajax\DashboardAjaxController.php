<?php

namespace App\Http\Controllers\SuperAdmin\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Request;
use App\College;
use App\CtrRoom;
use App\CtrSection;
use App\room_schedules;
use App\User;
use Carbon\Carbon;

class DashboardAjaxController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get filtered schedule data for calendar
     */
    public function getScheduleData()
    {
        if (Request::ajax()) {
            $department = Input::get('department');
            $instructor = Input::get('instructor');
            $room = Input::get('room');
            $course = Input::get('course');
            $section = Input::get('section');
            $view = Input::get('view', 'week');

            $query = room_schedules::where('is_active', 1);

            // Apply filters
            if ($department) {
                $query->where('college_code', $department);
            }

            if ($instructor) {
                $query->where('instructor', $instructor);
            }

            if ($room) {
                $query->where('room', $room);
            }

            if ($section) {
                // Join with offerings to filter by section
                $query->join('offerings_infos', 'room_schedules.offering_id', '=', 'offerings_infos.id')
                    ->where('offerings_infos.section', $section);
            }

            if ($course) {
                // Join with offerings to filter by course
                $query->join('offerings_infos', 'room_schedules.offering_id', '=', 'offerings_infos.id')
                    ->where('offerings_infos.course_code', $course);
            }

            $schedules = $query->get();

            $events = [];
            $now = Carbon::now();

            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);

                // Determine status for color coding
                $status = 'upcoming'; // Default

                if ($now->dayOfWeek == $this->getDayNumber($schedule->day)) {
                    $currentTime = Carbon::now()->format('H:i:s');
                    if ($currentTime >= $schedule->time_starts && $currentTime <= $schedule->time_end) {
                        $status = 'ongoing';
                    } elseif ($currentTime > $schedule->time_end) {
                        $status = 'completed';
                    }
                }

                // Get offering details
                $offering = \App\offerings_infos_table::find($schedule->offering_id);
                $courseCode = $offering ? $offering->course_code : 'Unknown';
                $section = $offering ? $offering->section : 'Unknown';

                // Get instructor name
                $instructorName = 'Not Assigned';
                if ($schedule->instructor) {
                    $instructorUser = User::find($schedule->instructor);
                    if ($instructorUser) {
                        $instructorName = $instructorUser->lastname;
                    }
                }

                $events[] = [
                    'id' => $schedule->id,
                    'title' => $courseCode . ' - ' . $schedule->room . ' - ' . $instructorName,
                    'start' => date('Y-m-d', strtotime('this week ' . $this->getDayName($schedule->day))) . 'T' . $schedule->time_starts,
                    'end' => date('Y-m-d', strtotime('this week ' . $this->getDayName($schedule->day))) . 'T' . $schedule->time_end,
                    'color' => $this->getStatusColor($status),
                    'textColor' => 'white',
                    'status' => $status
                ];
            }

            return response()->json($events);
        }
    }

    /**
     * Get room availability data
     */
    public function getRoomAvailability()
    {
        if (Request::ajax()) {
            $date = Input::get('date', date('Y-m-d'));
            $dayOfWeek = Carbon::parse($date)->format('l');

            $rooms = CtrRoom::where('is_active', 1)->get();
            $roomData = [];

            foreach ($rooms as $room) {
                $schedules = room_schedules::where('room', $room->room)
                    ->where('day', $dayOfWeek)
                    ->where('is_active', 1)
                    ->get();

                $timeSlots = [];
                $conflicts = [];

                foreach ($schedules as $schedule) {
                    $start = Carbon::parse($schedule->time_starts);
                    $end = Carbon::parse($schedule->time_end);

                    // Check for conflicts
                    foreach ($schedules as $otherSchedule) {
                        if ($schedule->id != $otherSchedule->id) {
                            $otherStart = Carbon::parse($otherSchedule->time_starts);
                            $otherEnd = Carbon::parse($otherSchedule->time_end);

                            if (($start <= $otherEnd) && ($end >= $otherStart)) {
                                $conflicts[] = [
                                    'schedule_id' => $schedule->id,
                                    'conflicting_with' => $otherSchedule->id
                                ];
                            }
                        }
                    }

                    $timeSlots[] = [
                        'id' => $schedule->id,
                        'start' => $schedule->time_starts,
                        'end' => $schedule->time_end,
                        'offering_id' => $schedule->offering_id
                    ];
                }

                $roomData[] = [
                    'room' => $room->room,
                    'building' => $room->building,
                    'college_code' => $room->college_code,
                    'time_slots' => $timeSlots,
                    'conflicts' => $conflicts,
                    'is_available' => count($timeSlots) == 0
                ];
            }

            return response()->json($roomData);
        }
    }

    /**
     * Get analytics data
     */
    public function getAnalyticsData()
    {
        if (Request::ajax()) {
            $type = Input::get('type', 'room_utilization');
            $startDate = Input::get('start_date');
            $endDate = Input::get('end_date');

            switch ($type) {
                case 'room_utilization':
                    return $this->getRoomUtilizationData($startDate, $endDate);
                case 'instructor_load':
                    return $this->getInstructorLoadData($startDate, $endDate);
                case 'enrollment_stats':
                    return $this->getEnrollmentStatistics($startDate, $endDate);
                case 'popular_time_slots':
                    return $this->getPopularTimeSlots($startDate, $endDate);
                default:
                    return response()->json(['error' => 'Invalid analytics type']);
            }
        }
    }

    /**
     * Get room utilization data
     */
    private function getRoomUtilizationData($startDate = null, $endDate = null)
    {
        $rooms = CtrRoom::where('is_active', 1)->get();
        $utilization = [];

        foreach ($rooms as $room) {
            $totalHours = 0;
            $query = room_schedules::where('room', $room->room)
                ->where('is_active', 1);

            // Apply date range filter if provided
            if ($startDate && $endDate) {
                $query->whereRaw(
                    "STR_TO_DATE(CONCAT(?, ' ', time_starts), '%Y-%m-%d %H:%i:%s') <= STR_TO_DATE(CONCAT(?, ' ', time_end), '%Y-%m-%d %H:%i:%s')",
                    [$endDate, $startDate]
                );
            }

            $schedules = $query->get();

            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);
                $totalHours += $end->diffInHours($start);
            }

            // Assuming 12 hours per day, 6 days per week = 72 hours max
            $utilizationRate = ($totalHours > 0) ? ($totalHours / 72) * 100 : 0;

            // Get college name
            $collegeName = 'N/A';
            if ($room->college_code) {
                $college = College::where('college_code', $room->college_code)->first();
                if ($college) {
                    $collegeName = $college->college_name;
                }
            }

            $utilization[] = [
                'room' => $room->room,
                'building' => $room->building,
                'college_code' => $room->college_code,
                'college_name' => $collegeName,
                'total_hours' => $totalHours,
                'utilization_rate' => round($utilizationRate, 2)
            ];
        }

        // Sort by utilization rate (descending)
        usort($utilization, function ($a, $b) {
            return $b['utilization_rate'] <=> $a['utilization_rate'];
        });

        return response()->json(array_slice($utilization, 0, 10)); // Return top 10
    }

    /**
     * Get instructor load data
     */
    private function getInstructorLoadData($startDate = null, $endDate = null)
    {
        $instructors = User::where('accesslevel', 1)->get();
        $loads = [];

        foreach ($instructors as $instructor) {
            $totalHours = 0;
            $query = room_schedules::where('instructor', $instructor->id)
                ->where('is_active', 1);

            // Apply date range filter if provided
            if ($startDate && $endDate) {
                $query->whereRaw(
                    "STR_TO_DATE(CONCAT(?, ' ', time_starts), '%Y-%m-%d %H:%i:%s') <= STR_TO_DATE(CONCAT(?, ' ', time_end), '%Y-%m-%d %H:%i:%s')",
                    [$endDate, $startDate]
                );
            }

            $schedules = $query->get();

            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);
                $totalHours += $end->diffInHours($start);
            }

            // Get department
            $department = 'N/A';
            $instructorInfo = instructors_infos::where('id', $instructor->id)->first();
            if ($instructorInfo && $instructorInfo->department) {
                $college = College::where('college_code', $instructorInfo->department)->first();
                if ($college) {
                    $department = $college->college_name;
                }
            }

            $loads[] = [
                'instructor_id' => $instructor->id,
                'instructor_name' => $instructor->name . ' ' . $instructor->lastname,
                'department' => $department,
                'total_hours' => $totalHours
            ];
        }

        // Sort by total hours (descending)
        usort($loads, function ($a, $b) {
            return $b['total_hours'] <=> $a['total_hours'];
        });

        return response()->json(array_slice($loads, 0, 10)); // Return top 10
    }

    /**
     * Get enrollment statistics
     */
    private function getEnrollmentStatistics($startDate = null, $endDate = null)
    {
        $sections = CtrSection::where('is_active', 1)->get();
        $stats = [];

        foreach ($sections as $section) {
            // Get enrollment count (this is a placeholder - adjust based on your actual data structure)
            $enrollmentCount = rand(15, 40); // Replace with actual enrollment count
            $capacity = 40; // Replace with actual capacity

            // Get program/college information
            $programName = 'N/A';
            $collegeCode = $section->college_code;
            $collegeName = 'N/A';

            if ($collegeCode) {
                $college = College::where('college_code', $collegeCode)->first();
                if ($college) {
                    $collegeName = $college->college_name;
                    $programName = $college->description;
                }
            }

            $stats[] = [
                'section' => $section->section_name,
                'program' => $programName,
                'college' => $collegeName,
                'enrollment' => $enrollmentCount,
                'capacity' => $capacity,
                'percentage' => round(($enrollmentCount / $capacity) * 100, 2)
            ];
        }

        // Sort by enrollment percentage (descending)
        usort($stats, function ($a, $b) {
            return $b['percentage'] <=> $a['percentage'];
        });

        return response()->json(array_slice($stats, 0, 10)); // Return top 10
    }

    /**
     * Get popular time slots
     */
    private function getPopularTimeSlots($startDate = null, $endDate = null)
    {
        $timeSlots = [
            '07:00:00-10:00:00' => 0,
            '10:00:00-13:00:00' => 0,
            '13:00:00-16:00:00' => 0,
            '16:00:00-19:00:00' => 0,
            '19:00:00-22:00:00' => 0
        ];

        $query = room_schedules::where('is_active', 1);

        // Apply date range filter if provided
        if ($startDate && $endDate) {
            $query->whereRaw(
                "STR_TO_DATE(CONCAT(?, ' ', time_starts), '%Y-%m-%d %H:%i:%s') <= STR_TO_DATE(CONCAT(?, ' ', time_end), '%Y-%m-%d %H:%i:%s')",
                [$endDate, $startDate]
            );
        }

        $schedules = $query->get();

        // Track days of the week
        $dayDistribution = [
            'Monday' => 0,
            'Tuesday' => 0,
            'Wednesday' => 0,
            'Thursday' => 0,
            'Friday' => 0,
            'Saturday' => 0
        ];

        foreach ($schedules as $schedule) {
            $start = Carbon::parse($schedule->time_starts);

            // Count time slots
            if ($start->between(Carbon::parse('07:00:00'), Carbon::parse('10:00:00'))) {
                $timeSlots['07:00:00-10:00:00']++;
            } elseif ($start->between(Carbon::parse('10:00:00'), Carbon::parse('13:00:00'))) {
                $timeSlots['10:00:00-13:00:00']++;
            } elseif ($start->between(Carbon::parse('13:00:00'), Carbon::parse('16:00:00'))) {
                $timeSlots['13:00:00-16:00:00']++;
            } elseif ($start->between(Carbon::parse('16:00:00'), Carbon::parse('19:00:00'))) {
                $timeSlots['16:00:00-19:00:00']++;
            } elseif ($start->between(Carbon::parse('19:00:00'), Carbon::parse('22:00:00'))) {
                $timeSlots['19:00:00-22:00:00']++;
            }

            // Count days
            if (isset($dayDistribution[$schedule->day])) {
                $dayDistribution[$schedule->day]++;
            }
        }

        // Format time slots
        $timeSlotResult = [];
        foreach ($timeSlots as $slot => $count) {
            $timeSlotResult[] = [
                'time_slot' => $slot,
                'count' => $count
            ];
        }

        // Format days
        $dayResult = [];
        foreach ($dayDistribution as $day => $count) {
            $dayResult[] = [
                'day' => $day,
                'count' => $count
            ];
        }

        // Sort by count (descending)
        usort($timeSlotResult, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });

        usort($dayResult, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });

        return response()->json([
            'time_slots' => $timeSlotResult,
            'days' => $dayResult
        ]);
    }

    /**
     * Helper function to get day name
     */
    private function getDayName($day)
    {
        switch ($day) {
            case 'Monday':
                return 'Monday';
            case 'Tuesday':
                return 'Tuesday';
            case 'Wednesday':
                return 'Wednesday';
            case 'Thursday':
                return 'Thursday';
            case 'Friday':
                return 'Friday';
            case 'Saturday':
                return 'Saturday';
            default:
                return 'Monday';
        }
    }

    /**
     * Helper function to get day number
     */
    private function getDayNumber($day)
    {
        switch ($day) {
            case 'Monday':
                return 1;
            case 'Tuesday':
                return 2;
            case 'Wednesday':
                return 3;
            case 'Thursday':
                return 4;
            case 'Friday':
                return 5;
            case 'Saturday':
                return 6;
            default:
                return 1;
        }
    }

    /**
     * Helper function to get status color
     */
    private function getStatusColor($status)
    {
        switch ($status) {
            case 'ongoing':
                return '#28a745'; // Green
            case 'completed':
                return '#6c757d'; // Gray
            case 'upcoming':
                return '#007bff'; // Blue
            case 'canceled':
                return '#dc3545'; // Red
            default:
                return '#007bff'; // Blue
        }
    }
}
