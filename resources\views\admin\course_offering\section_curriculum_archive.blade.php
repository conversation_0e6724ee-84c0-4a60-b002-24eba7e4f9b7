<?php
use Illuminate\Support\Facades\Schema;

$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-archive"></i>
        Section Curriculum Archive
        <small></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/admin/section_curriculum')}}">Section Curriculum</a></li>
        <li class="active">Archive</li>
      </ol>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    @if(Session::has('success'))
    <div class='col-sm-12'>
        <div class='callout callout-success'>
            {{Session::get('success')}}
        </div>
    </div>
    @endif

    @if(Session::has('error'))
    <div class='col-sm-12'>
        <div class='callout callout-danger'>
            {{Session::get('error')}}
        </div>
    </div>
    @endif

    <div class='row'>
        <div class='col-sm-12'>
            <div class="box box-default">
                <div class="box-header">
                    <h5 class="box-title">Archived Sections</h5>
                    <div class="box-tools pull-right">
                        <a href="{{url('/admin/section_curriculum')}}" class="btn btn-flat btn-primary"><i class="fa fa-arrow-left"></i> Back to Active Sections</a>
                    </div>
                </div>
                <div class="box-body">
                    <!-- Search and Filter Section -->
                    <div class="row" style="margin-bottom: 15px;">
                        <div class="col-md-12">
                            <div class="box box-solid box-default">
                                <div class="box-header with-border">
                                    <h3 class="box-title"><i class="fa fa-search"></i> Search & Filter</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        @if(Schema::hasColumn('ctr_sections', 'college_code'))
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>College</label>
                                                <select class="form-control select2" id="search-college" style="width: 100%;">
                                                    <option value="">All Colleges</option>
                                                    @foreach($colleges as $college)
                                                        <option value="{{ $college->college_code }}">{{ $college->college_code }} - {{ $college->college_name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        @endif
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Program Code</label>
                                                <select class="form-control select2" id="search-program" style="width: 100%;">
                                                    <option value="">All Programs</option>
                                                    @foreach($uniquePrograms as $uniqueProgram)
                                                        <option value="{{ $uniqueProgram }}">{{ $uniqueProgram }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Level</label>
                                                <select class="form-control select2" id="search-level" style="width: 100%;">
                                                    <option value="">All Levels</option>
                                                    @foreach($uniqueLevels as $uniqueLevel)
                                                        <option value="{{ $uniqueLevel }}">{{ $uniqueLevel }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Section Name</label>
                                                <input type="text" class="form-control" id="search-section" placeholder="Search by section name">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <button type="button" id="btn-search" class="btn btn-primary btn-flat"><i class="fa fa-search"></i> Search</button>
                                            <button type="button" id="btn-reset" class="btn btn-default btn-flat"><i class="fa fa-refresh"></i> Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Search and Filter Section -->

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="sections-table">
                            <thead>
                                <tr>
                                    @if(Schema::hasColumn('ctr_sections', 'college_code'))
                                    <th width='15%'>College</th>
                                    @endif
                                    <th width='15%'>Program Code</th>
                                    <th width='15%'>Level</th>
                                    <th width='15%'>Section Name</th>
                                    <th width='15%'>Status</th>
                                    <th width='25%'>Action</th>
                                </tr>
                            </thead>
                            <tbody id="sections-table-body">
                                @if(!$sections->isEmpty())
                                @foreach($sections as $section)
                                <tr>
                                    @if(Schema::hasColumn('ctr_sections', 'college_code'))
                                    <td>{{$section->college_code ?? 'N/A'}}</td>
                                    @endif
                                    <td>{{$section->program_code}}</td>
                                    <td>{{$section->level}}</td>
                                    <td>{{$section->section_name}}</td>
                                    <td>
                                        @if($section->is_active == 1)
                                        <label class='label label-success'>Active</label>
                                        @else
                                        <label class='label label-danger'>Inactive</label>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{url('/admin/section_management/archive',[$section->id])}}" class="btn btn-flat btn-success" title="Restore Section" onclick="return confirm('Do you wish to restore this section?')"><i class="fa fa-undo"></i> Restore</a>
                                    </td>
                                </tr>
                                @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('footer-script')
<script src='{{asset('plugins/select2/select2.js')}}'></script>
<script>
// Initialize Select2 Elements
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Select an option",
        allowClear: true
    });

    // College dropdown change event - filter programs
    $('#search-college').on('change', function() {
        var collegeCode = $(this).val();
        filterProgramsByCollege(collegeCode, '#search-program');
        searchSections();
    });

    // Search button click event
    $('#btn-search').click(function() {
        searchSections();
    });

    // Reset button click event
    $('#btn-reset').click(function() {
        $('#search-college').val('').trigger('change');
        $('#search-program').val('').trigger('change');
        $('#search-level').val('').trigger('change');
        $('#search-section').val('');
        searchSections();
    });

    // Select2 change event
    $('#search-program, #search-level').on('change', function() {
        searchSections();
    });

    // Enter key press event for section input
    $('#search-section').keypress(function(e) {
        if(e.which == 13) { // Enter key
            searchSections();
        }
    });
});

// Function to perform the search
function searchSections() {
    var data = {
        program: $('#search-program').val(),
        level: $('#search-level').val(),
        section: $('#search-section').val()
    };

    // Only add college parameter if the element exists
    if ($('#search-college').length) {
        data.college = $('#search-college').val();
    }

    $.ajax({
        type: "GET",
        url: "/ajax/admin/section_management/search_archive",
        data: data,
        success: function(data) {
            $('#sections-table-body').html(data);
        },
        error: function(xhr, status, error) {
            console.error("Error searching sections:", error);
            toastr.error("An error occurred while searching. Please try again.");
        }
    });
}

// Function to filter programs by college
function filterProgramsByCollege(collegeCode, programSelector) {
    if (!collegeCode) {
        // If no college selected, show all programs
        loadAllPrograms(programSelector);
        return;
    }

    // Show loading indicator
    $(programSelector).prop('disabled', true);

    $.ajax({
        type: "GET",
        url: "/ajax/admin/section_curriculum/get_programs_by_college",
        data: {
            college_code: collegeCode
        },
        success: function(response) {
            if (response.success) {
                // Clear current options
                $(programSelector).empty();

                // Add default option
                $(programSelector).append('<option value="">All Programs</option>');

                // Add programs from response
                $.each(response.programs, function(index, program) {
                    var programName = program.program_name ? program.program_name : 'N/A';
                    $(programSelector).append('<option value="' + program.program_code + '">' +
                        program.program_code + ' - ' + programName + '</option>');
                });

                // Re-enable select
                $(programSelector).prop('disabled', false);

                // Trigger change event to update dependent fields
                $(programSelector).trigger('change');
            } else {
                console.error("Error fetching programs:", response.message);
                toastr.error("An error occurred while fetching programs. Please try again.");
                $(programSelector).prop('disabled', false);
            }
        },
        error: function(xhr, status, error) {
            console.error("Error fetching programs:", error);
            toastr.error("An error occurred while fetching programs. Please try again.");
            $(programSelector).prop('disabled', false);
        }
    });
}

// Function to load all programs
function loadAllPrograms(programSelector) {
    // Show loading indicator
    $(programSelector).prop('disabled', true);

    $.ajax({
        type: "GET",
        url: "/ajax/admin/section_curriculum/get_all_programs",
        success: function(response) {
            if (response.success) {
                // Clear current options
                $(programSelector).empty();

                // Add default option
                $(programSelector).append('<option value="">All Programs</option>');

                // Add programs from response
                $.each(response.programs, function(index, program) {
                    var programName = program.program_name ? program.program_name : 'N/A';
                    $(programSelector).append('<option value="' + program.program_code + '">' +
                        program.program_code + ' - ' + programName + '</option>');
                });

                // Re-enable select
                $(programSelector).prop('disabled', false);

                // Trigger change event to update dependent fields
                $(programSelector).trigger('change');
            } else {
                console.error("Error fetching programs:", response.message);
                toastr.error("An error occurred while fetching programs. Please try again.");
                $(programSelector).prop('disabled', false);
            }
        },
        error: function(xhr, status, error) {
            console.error("Error fetching programs:", error);
            toastr.error("An error occurred while fetching programs. Please try again.");
            $(programSelector).prop('disabled', false);
        }
    });
}
</script>
@endsection
