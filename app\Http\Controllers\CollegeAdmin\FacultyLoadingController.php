<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\User;
use App\room_schedules;

class FacultyLoadingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Display the faculty loading page
     */
    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            \Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get instructors for this college
        $instructors = User::where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->get();

        return view('collegeadmin.faculty_loading.index', compact('instructors', 'college'));
    }

    /**
     * Display the faculty loading interface
     */
    public function faculty_loading()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            \Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get instructors for this college
        $instructors = User::where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->get();

        return view('collegeadmin.faculty_loading.faculty_loading', compact('instructors', 'college'));
    }

    /**
     * Generate schedule for a specific instructor
     */
    public function generateSchedule($instructor)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            \Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Verify the instructor belongs to this college
        $instructorUser = User::where('id', $instructor)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->firstOrFail();

        // Get instructor info
        $info = \App\instructors_infos::where('instructor_id', $instructor)->first();

        // Get schedules for this instructor
        $schedules = room_schedules::distinct()
            ->where('is_active', 1)
            ->where('instructor', $instructor)
            ->get();

        return view('collegeadmin.faculty_loading.generate_schedule', compact('schedules', 'instructor', 'instructorUser', 'info', 'college'));
    }
}
