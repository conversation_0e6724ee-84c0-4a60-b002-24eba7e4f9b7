<?php 
$loads = DB::table('curricula')
        ->join('offerings_infos','curricula.id','offerings_infos.curriculum_id')
        ->join('room_schedules','room_schedules.offering_id','offerings_infos.id')
        ->where('room_schedules.instructor',$instructor)
        ->get();
$offering = \App\offerings_infos::find($offering_id);
$curriculum = \App\curriculum::find($offering->curriculum_id);
?>

<div class="modal fade" id="modalunits" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Override Units</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="callout callout-info">
                            <div align="center">
                                <h5>Instructor Type: {{$type}}</h5>
                                <h5>Maximum Units: {{$units}}</h5>
                                <h5>Current Units: {{$loads->sum('units')}}</h5>
                                <h5>Units to be Added: {{$curriculum->units}}</h5>
                                <h5>Total Units: {{$loads->sum('units') + $curriculum->units}}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-flat btn-success" onclick="overridebtn(1)">Override</button>
                <button type="button" class="btn btn-flat btn-danger" onclick="overridebtn(0)">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
function overridebtn(override){
    var array = {};
    array['instructor'] = "{{$instructor}}";
    array['offering_id'] = "{{$offering_id}}";
    array['override'] = override;
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/faculty_loading/override_add",
        data: array,
        success: function(data){
            displaycourses('{{$level}}',"{{$instructor}}");
            getCurrentLoad("{{$instructor}}",'{{$level}}');
            $('#modalunits').modal('toggle');
        }, error: function(xhr){
            if(xhr.status == 500){
                toastr.error('Conflict in Schedule Found!!','Message!');
            }else{
                toastr.error('Something Went Wrong!','Message!');
            }
        }
    })
}
</script>
