<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Schema;
use App\CtrSection;
use App\curriculum;
use App\offerings_infos_table;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\ProgramService;

class SectionCurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('admin');
    }

    /**
     * Show the section curriculum management page
     * This replaces the old section_management page
     */
    public function index()
    {
        $sections = CtrSection::where('is_active', 1)->get();
        $colleges = \App\College::where('is_active', 1)->get();

        // Get programs from colleges using the ProgramService
        $programService = new ProgramService();
        $programs = $programService->getAllProgramsAsObjects();

        // Get unique program codes and levels for dropdowns
        $uniquePrograms = CtrSection::where('is_active', 1)->distinct()->pluck('program_code')->toArray();
        $uniqueLevels = CtrSection::where('is_active', 1)->distinct()->pluck('level')->toArray();

        // Check if college_code column exists in the table
        $uniqueColleges = [];
        if (Schema::hasColumn('ctr_sections', 'college_code')) {
            $uniqueColleges = CtrSection::where('is_active', 1)->whereNotNull('college_code')->distinct()->pluck('college_code')->toArray();
        }

        // Add curriculum count information to each section
        foreach ($sections as $section) {
            $section->offered_count = offerings_infos_table::where('section_name', $section->section_name)->count();
            $section->available_count = $section->getAvailableCurricula()->count();

            // Check if college_code property exists
            if (Schema::hasColumn('ctr_sections', 'college_code')) {
                // Get college name if college_code exists
                if (!empty($section->college_code)) {
                    $college = \App\College::where('college_code', $section->college_code)->first();
                    $section->college_name = $college ? $college->college_name : $section->college_code;
                } else {
                    $section->college_name = 'N/A';
                }
            } else {
                // If college_code column doesn't exist yet
                $section->college_code = null;
                $section->college_name = 'N/A';
            }
        }

        return view('admin.course_offering.section_curriculum', compact('sections', 'programs', 'colleges', 'uniquePrograms', 'uniqueLevels', 'uniqueColleges'));
    }

    /**
     * Show archived sections
     */
    public function archive()
    {
        $sections = CtrSection::where('is_active', 0)->get();
        $colleges = \App\College::all();

        // Get unique program codes and levels for dropdowns
        $uniquePrograms = CtrSection::where('is_active', 0)->distinct()->pluck('program_code')->toArray();
        $uniqueLevels = CtrSection::where('is_active', 0)->distinct()->pluck('level')->toArray();

        // Check if college_code column exists in the table
        $uniqueColleges = [];
        if (Schema::hasColumn('ctr_sections', 'college_code')) {
            $uniqueColleges = CtrSection::where('is_active', 0)->whereNotNull('college_code')->distinct()->pluck('college_code')->toArray();
        }

        // Add college name to each section
        foreach ($sections as $section) {
            // Check if college_code property exists
            if (Schema::hasColumn('ctr_sections', 'college_code')) {
                // Get college name if college_code exists
                if (!empty($section->college_code)) {
                    $college = \App\College::where('college_code', $section->college_code)->first();
                    $section->college_name = $college ? $college->college_name : $section->college_code;
                } else {
                    $section->college_name = 'N/A';
                }
            } else {
                // If college_code column doesn't exist yet
                $section->college_code = null;
                $section->college_name = 'N/A';
            }
        }

        return view('admin.course_offering.section_curriculum_archive', compact('sections', 'colleges', 'uniquePrograms', 'uniqueLevels', 'uniqueColleges'));
    }

    /**
     * Archive or restore a section
     */
    public function archiveSection($section_id)
    {
        $archive = CtrSection::find($section_id);
        if (!$archive) {
            Session::flash('error', 'Section not found');
            return redirect(url('/admin/section_curriculum'));
        }

        switch ($archive->is_active) {
            case 1: // Currently active, move to archive
                $archive->is_active = 0;
                $archive->update();
                Session::flash('error', "Section {$archive->section_name} has been moved to the archive section.");
                break;

            case 0: // Currently archived, restore
                $archive->is_active = 1;
                $archive->update();
                Session::flash('success', "Section {$archive->section_name} has been restored.");
                break;

            default:
                Session::flash('error', 'Invalid section status');
        }

        return redirect(url('/admin/section_curriculum'));
    }

    /**
     * Update an existing section
     */
    public function updateSection(Request $request)
    {
        // Validate input
        $request->validate([
            'section_id' => 'required|integer|exists:ctr_sections,id',
            'program_code' => 'required|string|max:20',
            'level' => 'required|string|max:20',
            'section_name' => 'required|string|max:50',
            'college_code' => 'nullable|string|max:20',
        ]);

        // Find the section
        $section = CtrSection::find($request->section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect(url('/admin/section_curriculum'));
        }

        // Check if another section with the same details already exists
        $check_exists = CtrSection::where('program_code', $request->program_code)
            ->where('level', $request->level)
            ->where('section_name', $request->section_name)
            ->where('id', '!=', $request->section_id)
            ->first();

        if ($check_exists) {
            Session::flash('error', "Section '{$request->section_name}' already exists for this program and level!");
            return redirect(url('/admin/section_curriculum'));
        }

        // Update the section
        $section->program_code = $request->program_code;
        $section->level = $request->level;
        $section->section_name = $request->section_name;
        $section->college_code = $request->college_code;
        $section->update();

        Session::flash('success', "Section '{$request->section_name}' successfully updated!");
        return redirect(url('/admin/section_curriculum'));
    }

    /**
     * Show curriculum subjects for a specific section
     */
    public function viewSectionCurriculum($section_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect(url('/admin/section_curriculum'));
        }

        // Get offered curricula
        $offerings = offerings_infos_table::where('section_name', $section->section_name)->get();

        // Get available curricula that can be added
        $availableCurricula = $section->getAvailableCurricula();

        return view('admin.course_offering.section_curriculum_view', compact('section', 'offerings', 'availableCurricula'));
    }

    /**
     * Add all available curriculum subjects to a section
     */
    public function addAllCurricula($section_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect(url('/admin/section_curriculum'));
        }

        $addedCount = $section->addAllCurriculaToOfferings();

        if ($addedCount > 0) {
            Session::flash('success', "Successfully added {$addedCount} curriculum subjects to {$section->section_name}");
            Log::info("User " . Auth::user()->name . " added {$addedCount} curriculum subjects to section {$section->section_name}");
        } else {
            Session::flash('info', "No new curriculum subjects to add to {$section->section_name}");
        }

        return redirect(url('/admin/section_curriculum/view', [$section_id]));
    }

    /**
     * Add specific curriculum subjects to a section
     */
    public function addSelectedCurricula(Request $request, $section_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect(url('/admin/section_curriculum'));
        }

        // Validate the request
        $this->validate($request, [
            'curriculum_ids' => 'required|array',
            'curriculum_ids.*' => 'required|integer|exists:curricula,id'
        ]);

        $addedCount = 0;
        foreach ($request->curriculum_ids as $curriculum_id) {
            $added = offerings_infos_table::addToOfferings(
                $curriculum_id,
                $section->section_name,
                $section->level
            );

            if ($added) {
                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            Session::flash('success', "Successfully added {$addedCount} curriculum subjects to {$section->section_name}");
            Log::info("User " . Auth::user()->name . " added {$addedCount} selected curriculum subjects to section {$section->section_name}");
        } else {
            Session::flash('info', "No curriculum subjects were added to {$section->section_name}");
        }

        return redirect(url('/admin/section_curriculum/view', [$section_id]));
    }

    /**
     * Remove a curriculum subject from a section
     */
    public function removeCurriculum($section_id, $offering_id)
    {
        $section = CtrSection::find($section_id);
        if (!$section) {
            Session::flash('error', 'Section not found');
            return redirect(url('/admin/section_curriculum'));
        }

        $offering = offerings_infos_table::find($offering_id);
        if (!$offering || $offering->section_name !== $section->section_name) {
            Session::flash('error', 'Invalid offering');
            return redirect(url('/admin/section_curriculum/view', [$section_id]));
        }

        // Get curriculum details for logging
        $curriculum = curriculum::find($offering->curriculum_id);
        $curriculum_name = $curriculum ? $curriculum->course_name : 'Unknown';

        // Delete the offering
        $offering->delete();

        Session::flash('success', "Successfully removed {$curriculum_name} from {$section->section_name}");
        Log::info("User " . Auth::user()->name . " removed curriculum subject {$curriculum_name} from section {$section->section_name}");

        return redirect(url('/admin/section_curriculum/view', [$section_id]));
    }

    /**
     * Create a new section with curriculum subjects
     */
    public function createSectionWithCurriculum(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'program_code' => 'required|string|max:20',
            'level' => 'required|string|max:20',
            'section_name' => 'required|string|max:50',
            'college_code' => 'nullable|string|max:20',
            'add_curriculum' => 'required|in:yes,no'
        ]);

        // Check if section already exists
        $check_exists = CtrSection::where('program_code', $request->program_code)
            ->where('level', $request->level)
            ->where('section_name', $request->section_name)
            ->first();

        if ($check_exists) {
            Session::flash('error', "Section '{$request->section_name}' already exists for this program and level!");
            return redirect(url('/admin/section_curriculum'));
        }

        // If college code is not provided, try to find it from the program using ProgramService
        $college_code = $request->college_code;
        if (empty($college_code)) {
            $programService = new ProgramService();
            $college_code = $programService->getCollegeCodeForProgram($request->program_code);
        }

        // Create new section
        $new_section = new CtrSection;
        $new_section->program_code = $request->program_code;
        $new_section->level = $request->level;
        $new_section->section_name = $request->section_name;
        $new_section->college_code = $college_code;
        $new_section->is_active = 1;
        $new_section->save();

        // Add curriculum subjects if requested
        if ($request->add_curriculum === 'yes') {
            $addedCount = $new_section->addAllCurriculaToOfferings();

            if ($addedCount > 0) {
                Session::flash('success', "Section '{$request->section_name}' successfully created with {$addedCount} curriculum subjects!");
                Log::info("User " . Auth::user()->name . " created section {$request->section_name} with {$addedCount} curriculum subjects");
            } else {
                Session::flash('success', "Section '{$request->section_name}' successfully created, but no curriculum subjects were available to add.");
                Log::info("User " . Auth::user()->name . " created section {$request->section_name} without curriculum subjects");
            }
        } else {
            Session::flash('success', "Section '{$request->section_name}' successfully created!");
            Log::info("User " . Auth::user()->name . " created section {$request->section_name}");
        }

        return redirect(url('/admin/section_curriculum'));
    }
}
