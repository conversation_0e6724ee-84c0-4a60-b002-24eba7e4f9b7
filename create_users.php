<?php
// <PERSON>ript to manually create default users
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

echo "=== Creating Default CLASSMOS Users ===\n\n";

try {
    // Check if superadmin already exists
    $existingSuperAdmin = DB::table('users')->where('username', 'superadmin')->first();

    if (!$existingSuperAdmin) {
        // Create SuperAdmin user
        DB::table('users')->insert([
            'username' => 'superadmin',
            'name' => 'Super',
            'middlename' => ' ',
            'lastname' => 'Admin',
            'accesslevel' => 100,
            'email' => '<EMAIL>',
            'password' => Hash::make('password1234'),
            'is_first_login' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✅ Created SuperAdmin user\n";
    } else {
        echo "ℹ️  SuperAdmin user already exists\n";
    }

    // Check if admin already exists
    $existingAdmin = DB::table('users')->where('username', 'admin')->first();

    if (!$existingAdmin) {
        // Create Admin user
        DB::table('users')->insert([
            'username' => 'admin',
            'name' => 'Admin',
            'middlename' => ' ',
            'lastname' => 'Admin',
            'accesslevel' => 0,
            'email' => '<EMAIL>',
            'password' => Hash::make('password1234'),
            'is_first_login' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✅ Created Admin user\n";
    } else {
        echo "ℹ️  Admin user already exists\n";
    }

    echo "\n=== User Creation Complete ===\n";
    echo "You can now login with:\n\n";
    echo "SuperAdmin:\n";
    echo "  Username: superadmin\n";
    echo "  Password: password1234\n\n";
    echo "Admin:\n";
    echo "  Username: admin\n";
    echo "  Password: password1234\n\n";

    // Verify users were created
    $users = DB::table('users')->select('username', 'name', 'lastname', 'accesslevel')->get();
    echo "Total users in database: " . count($users) . "\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>