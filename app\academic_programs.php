<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class academic_programs extends Model
{
    protected $table = 'academic_programs';

    protected $fillable = [
        'academic_type',
        'program_code',
        'program_name',
        'college_code',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'college_code' => 'string',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function ($program) {
            // Ensure college_code is set when creating a new program
            if (empty($program->college_code)) {
                // Try to find a college with this program in the programs table
                $existingProgram = \App\Program::where('program_code', $program->program_code)->first();

                if ($existingProgram) {
                    $college = \App\College::find($existingProgram->college_id);
                    if ($college) {
                        $program->college_code = $college->college_code;
                    }
                } else {
                    // Try to find a college with this program in its courses_array
                    $colleges = \App\College::all();

                    foreach ($colleges as $college) {
                        if (in_array($program->program_code, $college->courses_array)) {
                            $program->college_code = $college->college_code;
                            break;
                        }
                    }
                }

                // If still no college_code, assign to default college
                if (empty($program->college_code)) {
                    $defaultCollege = \App\College::firstOrCreate(
                        ['college_code' => 'UNASSIGNED'],
                        [
                            'college_name' => 'Unassigned College',
                            'is_active' => 1
                        ]
                    );

                    $program->college_code = $defaultCollege->college_code;

                    // Add program to the programs table
                    $defaultCollege->addProgram($program->program_code, $program->program_name);
                }
            }
        });

        static::saved(function ($program) {
            // Sync with programs table
            if ($program->college_code) {
                $college = \App\College::where('college_code', $program->college_code)->first();
                if ($college) {
                    $college->addProgram($program->program_code, $program->program_name);
                }
            }
        });
    }

    // Relationship with college
    public function college()
    {
        return $this->belongsTo('App\College', 'college_code', 'college_code');
    }

    // Relationship with curricula
    public function curricula()
    {
        return $this->hasMany('App\curriculum', 'program_code', 'program_code');
    }

    // Sync with college program_codes and programs table
    public function syncWithCollege()
    {
        if ($this->college_code) {
            $college = $this->college;
            if ($college) {
                // Add to programs table
                $college->addProgram($this->program_code, $this->program_name);
            }
        }

        return $this;
    }
}
