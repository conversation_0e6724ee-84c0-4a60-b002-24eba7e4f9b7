<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\User;

class FacultyLoadingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    /**
     * Display the faculty loading page
     */
    public function faculty_loading()
    {
        $instructors = User::where('accesslevel', 1)->get();
        return view('superadmin.faculty_loading.faculty_loading', compact('instructors'));
    }

    /**
     * Generate schedule for a specific instructor
     */
    public function generate_schedule($instructor)
    {
        $schedules = \App\room_schedules::distinct()
            ->where('is_active', 1)
            ->where('instructor', $instructor)
            ->get();

        return view('superadmin.faculty_loading.generate_schedule', compact('schedules', 'instructor'));
    }
}
