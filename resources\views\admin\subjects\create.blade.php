<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('header')
<!-- Content Header (Page header) -->
<section class="content-header">
    <h1>
        Add New Subject
        <small>Create a new subject in the system</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li><a href="{{ route('admin.subjects.index') }}"> Subjects</a></li>
        <li class="active">Add New Subject</li>
    </ol>
</section>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">Subject Information</h3>
            </div>
            <form role="form" method="POST" action="{{ route('admin.subjects.store') }}">
                @csrf
                <div class="box-body">
                    @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="subject_code">Subject Code</label>
                                <input type="text" class="form-control" id="subject_code" name="subject_code" placeholder="Enter subject code" value="{{ old('subject_code') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="subject_name">Subject Name</label>
                                <input type="text" class="form-control" id="subject_name" name="subject_name" placeholder="Enter subject name" value="{{ old('subject_name') }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="lec">Lecture Units</label>
                                <input type="number" class="form-control" id="lec" name="lec" placeholder="Enter lecture units" value="{{ old('lec', 0) }}" min="0" step="0.5" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="lab">Laboratory Units</label>
                                <input type="number" class="form-control" id="lab" name="lab" placeholder="Enter laboratory units" value="{{ old('lab', 0) }}" min="0" step="0.5" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="units">Total Units</label>
                                <input type="number" class="form-control" id="units" name="units" placeholder="Enter total units" value="{{ old('units', 0) }}" min="0" step="0.5" required>
                                <small class="text-muted">Auto-calculated from Lecture + Laboratory</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="is_complab" value="1" {{ old('is_complab') ? 'checked' : '' }}>
                                Is Computer Laboratory Subject
                            </label>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <button type="submit" class="btn btn-primary">Save Subject</button>
                    <a href="{{ route('admin.subjects.index') }}" class="btn btn-default">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Auto-calculate total units when lec or lab changes
        $('#lec, #lab').on('change', function() {
            var lec = parseFloat($('#lec').val()) || 0;
            var lab = parseFloat($('#lab').val()) || 0;
            $('#units').val((lec + lab).toFixed(1));
        });
    });
</script>
@endsection
