<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTimeBlocksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('time_blocks', function (Blueprint $table) {
            $table->increments('id');
            $table->string('day_type'); // MWF, TTh, Saturday, or Individual (M,T,W,Th,F,S)
            $table->string('day')->nullable(); // For individual days: M, T, W, Th, F, S
            $table->time('start_time');
            $table->time('end_time');
            $table->string('display_text'); // For display purposes (e.g., "8:00-9:00 AM")
            $table->boolean('is_active')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('time_blocks');
    }
}
