<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}

?>
@extends($layout)

@section('main-content')
<link rel="stylesheet" href="{{ asset ('plugins/timepicker/bootstrap-timepicker.min.css')}}">
<link rel="stylesheet" href="{{ asset ('plugins/fullcalendar/fullcalendar.css')}}">
<section class="content-header">
      <div class="row">
        <div class="col-md-6">
          <h1><i class="fa fa-bullhorn"></i>
            Course Scheduling
            <small>
                @if(isset($curricula->course_code))
                    {{ $curricula->course_code }}
                @elseif(isset($curricula->control_code))
                    {{ $curricula->control_code }}
                @else
                    {{ $curricula->subject_code }}
                @endif
                - {{ $section_name }}
            </small>
          </h1>
        </div>
        <div class="col-md-6 text-right" style="padding-top: 10px;">
          <a href="{{ route('collegeadmin.course_scheduling.index') }}" class="btn btn-default btn-flat">
            <i class="fa fa-arrow-left"></i> Back to Course Scheduling
          </a>
        </div>
      </div>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.course_scheduling.index') }}">Course Scheduling</a></li>
        <li class="active">
            @if(isset($curricula->course_code))
                {{ $curricula->course_code }}
            @elseif(isset($curricula->control_code))
                {{ $curricula->control_code }}
            @else
                {{ $curricula->subject_code }}
            @endif
            - {{ $section_name }}
        </li>
      </ol>
</section>

<div class="container-fluid" style="margin-top: 15px;">
    @if(Session::has('success'))
    <div class='col-sm-12'>
        <div class='callout callout-success'>
            {{Session::get('success')}}
        </div>
    </div>
    @endif

    @if(Session::has('error'))
    <div class='col-sm-12'>
        <div class='callout callout-danger'>
            {{Session::get('error')}}
        </div>
    </div>
    @endif

    <div class="row">
        <div class="col-sm-4">
             <div class="box box-solid box-default">
                <div class="box-header  bg-navy-active">
                    <h5 class="box-title">Inactive Schedules</h5>
                </div>
                <div class="box-body">
                    @if(!$inactive->isEmpty())
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Schedule</th>
                                    <th>Attach</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($inactive as $schedule)
                                <tr>
                                    <td>{{$schedule->day}} {{date('g:iA',strtotime($schedule->time_starts))}}-{{date('g:iA',strtotime($schedule->time_end))}}</td>
                                    <td><a href="{{ route('collegeadmin.course_scheduling.attach_schedule', [$schedule->id, $offering_id]) }}" class="btn btn-flat btn-block btn-success"><i class="fa fa-plus-circle"></i></a></td>
                                    <td><a href="{{ route('collegeadmin.course_scheduling.remove_schedule', $schedule->id) }}" onclick="return confirm('Do you wish to continue?')" class="btn btn-flat btn-block btn-danger"><i class="fa fa-times"></i></a></td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="callout callout-warning">
                        <div align="center"><h5>No Inactive Schedule Available!</h5></div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-sm-8">
            <div class="box box-default">
                <div class="box-header">
                    <h5 class="box-title">Schedule</h5>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Day Type</label>
                                <select class="form-control" id="day_type" onchange="updateDayOptions()">
                                    <option value="Individual">Individual Days</option>
                                    <option value="MWF">MWF</option>
                                    <option value="TTh">TTh</option>
                                    <option value="Saturday">Saturday</option>
                                    <option value="Sunday">Sunday</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Days</label>
                                <div id="day-checkboxes">
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="M"> Monday</label>
                                    </div>
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="T"> Tuesday</label>
                                    </div>
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="W"> Wednesday</label>
                                    </div>
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="Th"> Thursday</label>
                                    </div>
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="F"> Friday</label>
                                    </div>
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="Sa"> Saturday</label>
                                    </div>
                                    <div class="checkbox">
                                        <label><input type="checkbox" name="days[]" value="Su"> Sunday</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Time Start</label>
                                <div class="bootstrap-timepicker">
                                    <div class="input-group">
                                        <input type="text" class="form-control timepicker" id="time_start">

                                        <div class="input-group-addon">
                                            <i class="fa fa-clock-o"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Time End</label>
                                <div class="bootstrap-timepicker">
                                    <div class="input-group">
                                        <input type="text" class="form-control timepicker" id="time_end">

                                        <div class="input-group-addon">
                                            <i class="fa fa-clock-o"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div id="conflict-messages" class="alert alert-warning" style="display: none;">
                                <h4><i class="icon fa fa-warning"></i> Conflicts Detected!</h4>
                                <p>The following conflicts were found with your selected schedule:</p>
                                <ul id="conflict-list"></ul>
                                <p class="text-muted"><small>Note: Conflicts are automatically checked when you select days and times. You can still proceed with scheduling despite conflicts.</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button onclick="addschedule()" class="btn btn-flat btn-success"><i class="fa fa-plus-circle"></i> Add Schedule</button>
                            <a href="{{ route('collegeadmin.course_scheduling.index') }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back</a>
                        </div>
                    </div>
                </div>
                <div class="box-footer no-padding">
                    <div class="col-sm-12">
                        <div id="calendar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="myModal" class="modal fade" role="dialog">
    <div id='displayroom'>
    </div>
</div>
@endsection

@section('footer-script')
<script src="{{asset('plugins/timepicker/bootstrap-timepicker.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('/plugins/moment/moment.js') }}"></script>
<script src="{{asset('plugins/fullcalendar/fullcalendar.js')}}"></script>
<script>
$(document).ready(function() {
    $('.select2').select2();

    // Initialize day type dropdown
    $('#day_type').on('change', function() {
        updateDayOptions();
        // Check conflicts after day options are updated
        setTimeout(checkConflicts, 300); // Small delay to ensure checkboxes are updated
    });

    // Initialize time blocks
    $('#time_start, #time_end').timepicker({
        showInputs: false,
        minuteStep: 5,
        showMeridian: false,
        defaultTime: false,
        showSeconds: true,
        secondStep: 1
    });

    // Add event listeners for time changes
    $('#time_start, #time_end').on('changeTime.timepicker', function() {
        // Only check conflicts if both times are selected
        if ($('#time_start').val() && $('#time_end').val()) {
            // Check if start and end times are the same
            if ($('#time_start').val() === $('#time_end').val()) {
                // Clear previous conflicts
                $('#conflict-list').html('<li><strong>Invalid Time Range:</strong> Start time and end time cannot be the same.</li>');
                $('#conflict-messages').show();

                // Disable the Add Schedule button
                $('button.btn-success').prop('disabled', true);
            } else {
                // Enable the Add Schedule button
                $('button.btn-success').prop('disabled', false);
                checkConflicts();
            }
        }
    });

    // Add event listeners for day checkbox changes
    $(document).on('change', 'input[name="days[]"]', function() {
        // Only check conflicts if at least one day is selected
        if ($('input[name="days[]"]:checked').length > 0 && $('#time_start').val() && $('#time_end').val()) {
            checkConflicts();
        } else {
            // Hide conflict messages if no days selected
            $('#conflict-messages').hide();
        }
    });

    // Initial update of day options
    updateDayOptions();
});

$('.timepicker').timepicker({
    showInputs: false,
    showSeconds: true,
    secondStep: 1
});

$('#time_start').on('change',function(){
    if("{{$is_complab}}" == 1){
        $('#time_end').val(moment(this.value, "hh:mm:ss TT").add(3, 'hours').format("hh:mm:ss A"));
        // Check conflicts after end time is automatically set
        setTimeout(checkConflicts, 300);
    }
});

// Function to update day options based on day type selection
function updateDayOptions() {
    var dayType = $('#day_type').val();

    // Uncheck all checkboxes first
    $('input[name="days[]"]').prop('checked', false);

    // Show all checkboxes by default
    $('#day-checkboxes .checkbox').show();

    if (dayType === 'MWF') {
        // For MWF, only show M, W, F options
        $('#day-checkboxes .checkbox').hide();
        $('input[name="days[]"][value="M"]').parent().parent().show();
        $('input[name="days[]"][value="W"]').parent().parent().show();
        $('input[name="days[]"][value="F"]').parent().parent().show();

        // Auto-check the MWF days
        $('input[name="days[]"][value="M"]').prop('checked', true);
        $('input[name="days[]"][value="W"]').prop('checked', true);
        $('input[name="days[]"][value="F"]').prop('checked', true);

        // Get time blocks for MWF
        getTimeBlocks('MWF');
    } else if (dayType === 'TTh') {
        // For TTh, only show T, Th options
        $('#day-checkboxes .checkbox').hide();
        $('input[name="days[]"][value="T"]').parent().parent().show();
        $('input[name="days[]"][value="Th"]').parent().parent().show();

        // Auto-check the TTh days
        $('input[name="days[]"][value="T"]').prop('checked', true);
        $('input[name="days[]"][value="Th"]').prop('checked', true);

        // Get time blocks for TTh
        getTimeBlocks('TTh');
    } else if (dayType === 'Saturday') {
        // For Saturday, only show Sa option
        $('#day-checkboxes .checkbox').hide();
        $('input[name="days[]"][value="Sa"]').parent().parent().show();

        // Auto-check Saturday
        $('input[name="days[]"][value="Sa"]').prop('checked', true);

        // Get time blocks for Saturday
        getTimeBlocks('Saturday');
    } else if (dayType === 'Sunday') {
        // For Sunday, only show Su option
        $('#day-checkboxes .checkbox').hide();
        $('input[name="days[]"][value="Su"]').parent().parent().show();

        // Auto-check Sunday
        $('input[name="days[]"][value="Su"]').prop('checked', true);

        // Get time blocks for Sunday
        getTimeBlocks('Sunday');
    }
    // For Individual, all checkboxes are already shown and unchecked
}

// Function to get time blocks for a specific day type
function getTimeBlocks(dayType) {
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/course_scheduling/get_time_blocks",
        data: { day_type: dayType },
        success: function(data) {
            // If we have predefined time blocks, we could populate dropdowns here
            console.log("Time blocks for " + dayType + ":", data);
        }
    });
}

// Function to check for scheduling conflicts
function checkConflicts() {
    var selectedDays = [];
    $('input[name="days[]"]:checked').each(function() {
        selectedDays.push($(this).val());
    });

    var timeStart = $('#time_start').val();
    var timeEnd = $('#time_end').val();

    if (selectedDays.length === 0 || !timeStart || !timeEnd) {
        return;
    }

    // Check if start and end times are the same
    if (timeStart === timeEnd) {
        // Add to conflict list
        $('#conflict-list').append('<li><strong>Invalid Time Range:</strong> Start time and end time cannot be the same.</li>');
        $('#conflict-messages').show();
        return;
    }

    // Clear previous conflicts
    $('#conflict-list').empty();
    $('#conflict-messages').hide();

    // Check conflicts for each selected day
    var conflictsFound = false;

    // Create a promise for each day's conflict check
    var promises = [];

    selectedDays.forEach(function(day) {
        var array = {};
        array['day'] = day;
        array['time_start'] = timeStart;
        array['time_end'] = timeEnd;
        array['room'] = ''; // We'll check all rooms
        array['instructor_id'] = ''; // We'll check all instructors
        array['section_name'] = "{{$section_name}}";

        var promise = $.ajax({
            type: "GET",
            url: "/ajax/collegeadmin/course_scheduling/check_conflicts",
            data: array
        });

        promises.push(promise);
    });

    // Wait for all conflict checks to complete
    $.when.apply($, promises).done(function() {
        var hasConflicts = false;

        // Process each response
        for (var i = 0; i < arguments.length; i++) {
            var response = arguments[i][0]; // Get the response data

            if (response.has_conflicts) {
                hasConflicts = true;
                // Add conflicts to the list
                $.each(response.conflicts, function(index, conflict) {
                    $('#conflict-list').append('<li>' + conflict.message + '</li>');
                });
            }
        }

        if (hasConflicts) {
            $('#conflict-messages').show();
        } else {
            // No conflicts
            $('#conflict-messages').hide();
            // Show a success message that automatically disappears after 3 seconds
            var successMessage = $('<div class="alert alert-success" id="no-conflicts-message">' +
                '<h4><i class="icon fa fa-check"></i> No Conflicts!</h4>' +
                '<p>Your selected schedule has no conflicts. You can proceed with adding the schedule.</p>' +
                '</div>');

            // Remove any existing success message
            $('#no-conflicts-message').remove();

            // Add the new message before the Add Schedule button
            successMessage.insertBefore($('button.btn-success').parent().parent());

            // Automatically hide the message after 5 seconds
            setTimeout(function() {
                successMessage.fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }
    });
}

function addschedule() {
    var selectedDays = [];
    $('input[name="days[]"]:checked').each(function() {
        selectedDays.push($(this).val());
    });

    var timeStart = $('#time_start').val();
    var timeEnd = $('#time_end').val();
    var dayType = $('#day_type').val();

    if (selectedDays.length === 0) {
        toastr.warning('Please select at least one day of the week!');
        return;
    }

    if (!timeStart || !timeEnd) {
        toastr.warning('Please select start and end times!');
        return;
    }

    // Check if start and end times are the same
    if (timeStart === timeEnd) {
        toastr.error('Start time and end time cannot be the same!');

        // Add to conflict list
        $('#conflict-list').html('<li><strong>Invalid Time Range:</strong> Start time and end time cannot be the same.</li>');
        $('#conflict-messages').show();
        return;
    }

    // Clear previous conflicts
    $('#conflict-list').empty();
    $('#conflict-messages').hide();

    // Check conflicts for each selected day
    var promises = [];
    var dayArrays = [];

    selectedDays.forEach(function(day) {
        var array = {};
        array['day'] = day;
        array['day_type'] = dayType;
        array['time_start'] = timeStart;
        array['time_end'] = timeEnd;
        array['offering_id'] = "{{$offering_id}}";
        array['section_name'] = "{{$section_name}}";

        dayArrays.push(array);

        var promise = $.ajax({
            type: "GET",
            url: "/ajax/collegeadmin/course_scheduling/check_conflicts",
            data: array
        });

        promises.push(promise);
    });

    // Wait for all conflict checks to complete
    $.when.apply($, promises).done(function() {
        var hasConflicts = false;
        var conflicts = [];

        // Process each response
        for (var i = 0; i < arguments.length; i++) {
            var response;

            // Handle single or multiple responses correctly
            if (promises.length === 1) {
                response = arguments[0];
            } else {
                response = arguments[i][0]; // Get the response data
            }

            if (response.has_conflicts) {
                hasConflicts = true;
                // Add conflicts to the list
                $.each(response.conflicts, function(index, conflict) {
                    conflicts.push(conflict);
                    $('#conflict-list').append('<li>' + conflict.message + '</li>');
                });
            }
        }

        if (hasConflicts) {
            $('#conflict-messages').show();

            // Ask user if they want to proceed anyway
            if (confirm('Conflicts detected! Do you still want to proceed?')) {
                // Proceed to get available rooms for all selected days
                getAvailableRoomsForMultipleDays(dayArrays);
            }
        } else {
            // No conflicts, proceed
            $('#conflict-messages').hide();
            getAvailableRoomsForMultipleDays(dayArrays);
        }
    });
}

function getAvailableRooms(array) {
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/course_scheduling/get_rooms_available",
        data: array,
        success: function(data) {
            $('#displayroom').html(data).fadeIn();
            $('#myModal').modal('show');
        }
    });
}

function getAvailableRoomsForMultipleDays(dayArrays) {
    // If there's only one day, use the original function
    if (dayArrays.length === 1) {
        getAvailableRooms(dayArrays[0]);
        return;
    }

    // For multiple days, we need to show a combined view
    // First, get available rooms for the first day
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/course_scheduling/get_rooms_available",
        data: dayArrays[0],
        success: function(data) {
            // Show the modal with the first day's rooms
            $('#displayroom').html(data).fadeIn();

            // Add a note about multiple days being scheduled
            var daysText = dayArrays.map(function(array) {
                return getDayName(array.day);
            }).join(', ');

            $('#displayroom .box-header h3').append(' <small>(Scheduling for: ' + daysText + ')</small>');

            // Add a hidden input with all the days
            var daysInput = '<input type="hidden" name="multiple_days" value="' +
                dayArrays.map(function(array) { return array.day; }).join(',') + '">';
            $('#displayroom form').append(daysInput);

            // Show the modal
            $('#myModal').modal('show');
        }
    });
}

function getDayName(dayCode) {
    switch(dayCode) {
        case 'M': return 'Monday';
        case 'T': return 'Tuesday';
        case 'W': return 'Wednesday';
        case 'Th': return 'Thursday';
        case 'F': return 'Friday';
        case 'Sa': return 'Saturday';
        case 'Su': return 'Sunday';
        default: return dayCode;
    }
}

/**
 * Remove a batch of schedules based on day type
 * @param {number} scheduleId - The ID of the clicked schedule
 * @param {number} offeringId - The offering ID
 * @param {string} dayType - The day type (MWF, TTh, Saturday, Sunday)
 */
function removeBatchSchedules(scheduleId, offeringId, dayType) {
    // Show loading message
    toastr.info('Processing batch removal...');

    // Call the backend to remove all related schedules
    $.ajax({
        type: "GET",
        url: "/collegeadmin/course_scheduling/remove_batch_schedules",
        data: {
            schedule_id: scheduleId,
            offering_id: offeringId,
            day_type: dayType
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                // Reload the page to show updated calendar
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            } else {
                toastr.error(response.message || 'An error occurred while removing schedules');
            }
        },
        error: function() {
            toastr.error('An error occurred while removing schedules');
        }
    });
}

$('#calendar').fullCalendar({
    firstDay: 1,
    columnFormat: 'ddd',
    defaultView: 'agendaWeek',
    hiddenDays: [0],
    minTime: '07:00:00',
    maxTime: '22:00:00',
    header: false,
    //// uncomment this line to hide the all-day slot
    allDaySlot: false,
    eventSources: [<?php echo "$get_schedule"?>],
     eventRender: function(event, element) {
        element.find('div.fc-title').html(element.find('div.fc-title').text()) ;
     },
    eventClick: function(event){
        // Get the day of the clicked event
        var eventDay = event.day;

        // Ask if user wants to remove just this schedule or the batch
        var options = ['Remove this schedule only', 'Remove all related schedules (MWF/TTh/Saturday/Sunday)'];
        var selectedOption = confirm('Do you want to remove just this schedule or all related schedules?\n\nClick OK to remove all related schedules (MWF/TTh/Saturday/Sunday).\nClick Cancel to remove only this schedule.');

        if (selectedOption) {
            // Remove all related schedules
            removeBatchSchedules(event.id, event.offering_id, event.day_type);
        } else {
            // Remove just this schedule
            if (confirm('Are you sure you want to remove this schedule?')) {
                window.open('/collegeadmin/course_scheduling/remove_schedule/'+event.id,'_self');
            }
        }
    }
 });
</script>
@endsection