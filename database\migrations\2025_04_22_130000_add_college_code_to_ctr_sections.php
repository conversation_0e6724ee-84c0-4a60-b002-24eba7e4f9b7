<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCollegeCodeToCtrSections extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Skip this migration as it's already been applied
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ctr_sections', function (Blueprint $table) {
            if (Schema::hasColumn('ctr_sections', 'college_code')) {
                $table->dropForeign(['college_code']);
                $table->dropColumn('college_code');
            }
        });
    }
}
