<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-folder-open"></i>
        View Curricula
        <small>{{ $programCode }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li class="active">View Curricula</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Years for {{ $programCode }}</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.curriculum.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Add New Curriculum
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($curriculumYears) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Curriculum Year</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($curriculumYears as $year)
                                        <tr>
                                            <td>{{ $year }}</td>
                                            <td>
                                                <a href="{{ route('collegeadmin.curriculum.list_curriculum', [$programCode, $year]) }}" class="btn btn-info btn-sm">
                                                    <i class="fa fa-list"></i> View Subjects
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No curriculum years found for this program.
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.curriculum.index') }}" class="btn btn-default">Back to Programs</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
