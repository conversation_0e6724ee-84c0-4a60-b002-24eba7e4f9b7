<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<link rel="stylesheet" href="{{ asset ('plugins/toastr/toastr.css')}}">
<link rel="stylesheet" href="{{ asset ('plugins/fullcalendar/fullcalendar.css')}}">
<link rel="stylesheet" href="{{ asset ('plugins/datatables/dataTables.bootstrap.css')}}">
<link rel="stylesheet" href="{{ asset ('plugins/datatables/jquery.dataTables.css')}}">
<section class="content-header">
      <h1><i class="fa fa-calendar "></i>
        Faculty Loading
        <small>CLASSMOS</small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Loading</li>
      </ol>
</section>


<div class="container-fluid" style="margin-top: 15px;">
    <div class="box box-default">
        <div class="box-header">
            <h5 class="box-title">Search by Instructor</h5>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group" id="displaylevel">
                        <label>Level</label>
                        <select class="select2 form-control" id="level">
                            <option>Please Select</option>
                            <option value="1st Year">1st Year</option>
                            <option value="2nd Year">2nd Year</option>
                            <option value="3rd Year">3rd Year</option>
                            <option value="4th Year">4th Year</option>
                        </select>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group" id="displayinstructor">
                        <label>Instructor</label>
                        <select class="select2 form-control" id="instructor">
                            <option>Please Select</option>
                            @foreach($instructors as $instructor)
                            <option value="{{$instructor->id}}" data-college="{{$instructor->college_code}}">{{strtoupper($instructor->name)}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group" id="displayprogram">
                        <label>Program</label>
                        <select class="select2 form-control" id="program">
                            <option>Please Select</option>
                        </select>
                    </div>
                </div>
                <div class="col-sm-3" id="displaysearch">
                    <label>Search</label>
                    <button class='btn btn-flat btn-primary btn-block' onclick='displaycourses(level.value,instructor.value,program.value)'>Search</button>
                </div>
            </div>
        </div>
    </div>
    <div class='row'>
        <div class='col-sm-5' id='displaycourses'></div>
        <div class='col-sm-7' id='displaycalendar'></div>
    </div>
</div>

<div id="displaygetunitsloaded"></div>
@endsection

@section('footer-script')
<script src='{{asset('plugins/select2/select2.js')}}'></script>
<script type="text/javascript" src="{{ asset('/plugins/moment/moment.js') }}"></script>
<script src="{{asset('plugins/fullcalendar/fullcalendar.js')}}"></script>
<script src="{{asset('plugins/jQueryUI/jquery-ui.js')}}"></script>
<script src="{{asset('plugins/datatables/jquery.dataTables.js')}}"></script>
<script src="{{asset('plugins/datatables/dataTables.bootstrap.js')}}"></script>
<script src="{{asset('js/faculty-loading-drag-drop.js')}}"></script>
<script>
$(document).ready(function(){
    $('.draggable').data('duration', '03:00');
    // Hide instructor, program, and search button initially
    $('#displayinstructor').hide();
    $('#displayprogram').hide();
    $('#displaysearch').hide();

    // Initialize select2
    $('.select2').select2({
        width: '100%',
        placeholder: 'Please Select'
    });

    // Show instructor dropdown when level is selected
    $('#level').on('change', function(){
        $('#displayinstructor').fadeIn();
    });

    // Show program dropdown when instructor is selected and load programs
    $('#instructor').on('change', function(){
        var instructorId = $(this).val();
        var collegeCode = $('option:selected', this).data('college');

        if (instructorId && collegeCode) {
            // Load programs for this college
            loadProgramsByCollege(collegeCode);
            $('#displayprogram').fadeIn();
        } else {
            // Clear and hide program dropdown if no instructor selected
            $('#program').empty().append('<option>Please Select</option>');
            $('#displayprogram').hide();
            $('#displaysearch').hide();
        }
    });

    // Show search button when program is selected
    $('#program').on('change', function(){
        $('#displaysearch').fadeIn();
    });
});

// Load programs by college code
function loadProgramsByCollege(collegeCode) {
    $.ajax({
        type: "GET",
        url: "/ajax/superadmin/faculty_loading/get_programs_by_college",
        data: { college_code: collegeCode },
        success: function(data) {
            // Clear existing options
            $('#program').empty();

            // Add default option
            $('#program').append('<option value="">All Programs</option>');

            // Add program options
            $.each(data, function(index, program) {
                $('#program').append('<option value="' + program.program_code + '">' +
                    program.program_name + '</option>');
            });

            // Refresh select2
            $('#program').trigger('change');
        },
        error: function() {
            console.error('Failed to load programs for college: ' + collegeCode);
            // Add default option in case of error
            $('#program').empty().append('<option value="">All Programs</option>');
        }
    });
}

// Display available courses for loading
function displaycourses(level, instructor, program){
    var array = {};
    array['level'] = level;
    array['instructor'] = instructor;
    array['program_code'] = program;

    $.ajax({
        type: "GET",
        url: "/ajax/superadmin/faculty_loading/courses_to_load",
        data: array,
        success: function(data){
            $('#displaycourses').html(data).fadeIn();
            init_events($('.draggable div.callout'));
            getCurrentLoad(instructor, level, program);
        }
    });
}

// Search for courses
function search(event, value, level){
   var array = {};
   array['value'] = value;
   array['level'] = level;
   array['program_code'] = $('#program').val();

   $.ajax({
       type: "GET",
       url: "/ajax/superadmin/faculty_loading/search_courses",
       data: array,
       success: function(data){
           $('#searchcourse').html(data).fadeIn();
           init_events($('.draggable div.callout'));
       }
   });
}

// Get current faculty load
function getCurrentLoad(instructor, level, program){
    var array = {};
    array['instructor'] = instructor;
    array['level'] = level;
    array['program_code'] = program;

    $.ajax({
        type: "GET",
        url: "/ajax/superadmin/faculty_loading/current_load",
        data: array,
        success: function(data){
            $('#displaycalendar').html(data).fadeIn();
        }
    });
}

// Initialize draggable events
function init_events(ele) {
    ele.each(function () {
      var eventObject = {
        title: $(this).attr("data-object")
      }
      $(this).data('eventObject', eventObject);
      $(this).draggable({
        zIndex        : 1070,
        revert        : true,
        revertDuration: 0
      })
    })
}
</script>
@endsection
