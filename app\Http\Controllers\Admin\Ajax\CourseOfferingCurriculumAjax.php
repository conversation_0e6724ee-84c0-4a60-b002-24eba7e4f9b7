<?php

namespace App\Http\Controllers\Admin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request as RequestFacade;
use Illuminate\Support\Facades\View;
use App\CtrSection;
use App\curriculum;
use App\offerings_infos_table;
use App\College;
use App\Services\ProgramService;

class CourseOfferingCurriculumAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get programs for a college
     */
    public function getProgramsByCollege()
    {
        if (RequestFacade::ajax()) {
            $college_code = Input::get('college_code');

            if (!$college_code) {
                return response()->json([]);
            }

            $programService = new ProgramService();
            $programs = $programService->getProgramsForCollege($college_code);

            return response()->json($programs);
        }
    }

    /**
     * Get all programs
     */
    public function getAllPrograms()
    {
        if (RequestFacade::ajax()) {
            $programService = new ProgramService();
            $programs = $programService->getAllProgramsAsObjects();

            return response()->json($programs);
        }
    }

    /**
     * Get sections for a program
     */
    public function getSectionsByProgram()
    {
        if (RequestFacade::ajax()) {
            $program_code = Input::get('program_code');

            if (!$program_code) {
                return response()->json([]);
            }

            $sections = CtrSection::where('program_code', $program_code)
                ->where('is_active', 1)
                ->get();

            return response()->json($sections);
        }
    }

    /**
     * Get curriculum years for a program
     */
    public function getCurriculumYearsByProgram()
    {
        if (RequestFacade::ajax()) {
            $program_code = Input::get('program_code');

            if (!$program_code) {
                return response()->json([]);
            }

            $curriculum_years = curriculum::distinct()
                ->where('program_code', $program_code)
                ->pluck('curriculum_year')
                ->toArray();

            return response()->json($curriculum_years);
        }
    }

    /**
     * Get curriculum details for a program and year
     */
    public function getCurriculumDetails()
    {
        if (RequestFacade::ajax()) {
            $program_code = Input::get('program_code');
            $curriculum_year = Input::get('curriculum_year');

            if (!$program_code || !$curriculum_year) {
                return response()->json([]);
            }

            $curricula = curriculum::where('program_code', $program_code)
                ->where('curriculum_year', $curriculum_year)
                ->get();

            return view('admin.course_offering.curriculum.ajax.curriculum_details', compact('curricula'))->render();
        }
    }

    /**
     * Get offerings for a section
     */
    public function getSectionOfferings()
    {
        if (RequestFacade::ajax()) {
            $section_name = Input::get('section_name');

            if (!$section_name) {
                return response()->json([]);
            }

            $offerings = offerings_infos_table::where('section_name', $section_name)
                ->get();

            return view('admin.course_offering.curriculum.ajax.section_offerings', compact('offerings', 'section_name'))->render();
        }
    }

    /**
     * Get available curricula for a section
     */
    public function getAvailableCurricula()
    {
        // Accept both AJAX and non-AJAX requests for testing purposes
        $section_id = Input::get('section_id');

        // Log the request for debugging
        \Log::info('getAvailableCurricula called with section_id: ' . $section_id);

        $section = CtrSection::find($section_id);
        if (!$section) {
            \Log::error('Section not found with ID: ' . $section_id);
            return response()->json([
                'success' => false,
                'message' => 'Section not found'
            ]);
        }

        \Log::info('Section found: ' . $section->section_name . ', Program: ' . $section->program_code . ', Level: ' . $section->level);

        // Get all curricula for this program and level
        $level_map = [
            '1st Year' => '1st Year',
            '2nd Year' => '2nd Year',
            '3rd Year' => '3rd Year',
            '4th Year' => '4th Year',
        ];

        $level = $level_map[$section->level] ?? $section->level;
        \Log::info('Level mapped: ' . $level);

        // Get all curricula for this program
        $curricula = curriculum::where('program_code', $section->program_code)
            ->where('level', $level)
            ->where('is_active', 1)
            ->get();

        \Log::info('Found ' . $curricula->count() . ' curricula for program ' . $section->program_code . ' and level ' . $level);

        // Get IDs of curricula already offered for this section
        $offered_ids = offerings_infos_table::where('section_name', $section->section_name)
            ->pluck('curriculum_id')
            ->toArray();

        \Log::info('Found ' . count($offered_ids) . ' already offered curricula for section ' . $section->section_name);

        // Filter out curricula already offered
        $availableCurricula = $curricula->filter(function ($curriculum) use ($offered_ids) {
            return !in_array($curriculum->id, $offered_ids);
        });

        \Log::info('After filtering, ' . $availableCurricula->count() . ' curricula are available to add');

        // Check if the view exists
        $viewPath = 'admin.course_offering.curriculum.ajax.available_curricula';
        if (!\View::exists($viewPath)) {
            \Log::error('View not found: ' . $viewPath);
            return response()->json([
                'success' => false,
                'message' => 'View not found: ' . $viewPath
            ]);
        }

        // If this is an AJAX request, return just the rendered view
        if (RequestFacade::ajax()) {
            return view($viewPath, compact('section', 'availableCurricula'))->render();
        }

        // For non-AJAX requests (for testing), return a complete page
        return view($viewPath, compact('section', 'availableCurricula'));
    }

    /**
     * Get available subjects for a section with curriculum integration
     */
    public function getAvailableSubjects()
    {
        if (RequestFacade::ajax()) {
            $section_id = Input::get('section_id');
            $curriculum_year = Input::get('curriculum_year');
            $period = Input::get('period');

            \Log::info('getAvailableSubjects called with section_id: ' . $section_id . ', curriculum_year: ' . $curriculum_year . ', period: ' . $period);

            $section = CtrSection::find($section_id);
            if (!$section) {
                \Log::error('Section not found with ID: ' . $section_id);
                return response()->json([
                    'success' => false,
                    'message' => 'Section not found'
                ]);
            }

            // Get all offered curricula for this section
            $offered = offerings_infos_table::where('section_name', $section->section_name)
                ->pluck('curriculum_id')
                ->toArray();

            // Get all available curricula for this program, level, and period
            $query = curriculum::where('program_code', $section->program_code)
                ->where('is_active', 1);

            // Apply curriculum year filter if provided
            if ($curriculum_year) {
                $query->where('curriculum_year', $curriculum_year);
            }

            // Apply level filter based on section level
            $level_map = [
                '1st Year' => '1st Year',
                '2nd Year' => '2nd Year',
                '3rd Year' => '3rd Year',
                '4th Year' => '4th Year',
            ];
            $level = $level_map[$section->level] ?? $section->level;
            if ($level) {
                $query->where('level', $level);
            }

            // Apply period filter if provided
            if ($period) {
                $query->where('period', $period);
            }

            // Get all curricula that match the criteria
            $availableSubjects = $query->whereNotIn('id', $offered)->get();

            \Log::info('Found ' . $availableSubjects->count() . ' available subjects for section ' . $section->section_name);

            return view('admin.course_offering.curriculum.ajax.available_subjects', compact('section', 'availableSubjects', 'curriculum_year', 'period'))->render();
        }
    }
}
