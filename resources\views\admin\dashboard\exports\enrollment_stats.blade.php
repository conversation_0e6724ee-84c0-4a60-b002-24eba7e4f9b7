<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Enrollment Statistics Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin-bottom: 5px;
        }
        .date {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
        }
        .over-capacity {
            background-color: #f8d7da;
        }
        .near-capacity {
            background-color: #fff3cd;
        }
        .under-capacity {
            background-color: #d4edda;
        }
        .progress-bar {
            width: 100%;
            background-color: #e9ecef;
            height: 15px;
            position: relative;
        }
        .progress {
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Enrollment Statistics Report</h1>
        <p>Generated on: {{ $date }}</p>
    </div>
    
    <div class="date">
        <p><strong>Report Summary:</strong> This report shows the enrollment statistics for all active sections in the system, comparing current enrollment against maximum capacity.</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>#</th>
                <th>Section</th>
                <th>Enrollment</th>
                <th>Capacity</th>
                <th>Percentage</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach($stats as $index => $section)
                @php
                    $class = '';
                    $status = '';
                    if ($section['percentage'] > 100) {
                        $class = 'over-capacity';
                        $status = 'Over Capacity';
                    } elseif ($section['percentage'] >= 80) {
                        $class = 'near-capacity';
                        $status = 'Near Capacity';
                    } else {
                        $class = 'under-capacity';
                        $status = 'Under Capacity';
                    }
                @endphp
                <tr class="{{ $class }}">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $section['section'] }}</td>
                    <td>{{ $section['enrollment'] }}</td>
                    <td>{{ $section['capacity'] }}</td>
                    <td>{{ $section['percentage'] }}%</td>
                    <td>{{ $status }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="footer">
        <p>This is an automatically generated report. For any questions, please contact the system administrator.</p>
    </div>
</body>
</html>
