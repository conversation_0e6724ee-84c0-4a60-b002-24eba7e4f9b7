<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class DropAcademicProgramsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, backup the academic_programs table data
        if (Schema::hasTable('academic_programs')) {
            $academicPrograms = DB::table('academic_programs')->get();
            
            // Store the backup in a JSON file
            $backupPath = storage_path('app/backups');
            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
            }
            
            file_put_contents(
                storage_path('app/backups/academic_programs_backup_' . date('Y_m_d_His') . '.json'),
                json_encode($academicPrograms, JSON_PRETTY_PRINT)
            );
            
            // Drop the table
            Schema::dropIfExists('academic_programs');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Recreate the academic_programs table
        if (!Schema::hasTable('academic_programs')) {
            Schema::create('academic_programs', function (Blueprint $table) {
                $table->increments('id');
                $table->string('academic_type')->nullable();
                $table->string('department')->nullable();
                $table->string('program_code')->nullable();
                $table->string('program_name')->nullable();
                $table->string('college_code', 20)->nullable();
                $table->string('level')->nullable();
                $table->string('strand')->nullable();
                $table->string('strand_name')->nullable();
                $table->timestamps();
                
                $table->foreign('college_code')->references('college_code')->on('colleges')->onDelete('set null');
            });
            
            // Restore from the latest backup if available
            $backupPath = storage_path('app/backups');
            $backupFiles = glob($backupPath . '/academic_programs_backup_*.json');
            
            if (!empty($backupFiles)) {
                // Get the latest backup file
                $latestBackup = end($backupFiles);
                
                // Restore data from the backup
                $backupData = json_decode(file_get_contents($latestBackup));
                
                foreach ($backupData as $program) {
                    $program = (array) $program;
                    
                    // Remove the id field to let the database auto-increment
                    unset($program['id']);
                    
                    DB::table('academic_programs')->insert($program);
                }
            }
        }
    }
}
