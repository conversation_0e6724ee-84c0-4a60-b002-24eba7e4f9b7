<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\CtrRoom;
use App\College;
use Illuminate\Support\Facades\Session;

class RoomController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Display a listing of rooms for the college
     */
    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get rooms for this college
        $rooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->get();
        
        // Get college information
        $college = College::where('college_code', $collegeCode)->first();
        
        // Get unique room numbers and buildings for dropdowns
        $uniqueRooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->distinct()
            ->pluck('room')
            ->toArray();
            
        $uniqueBuildings = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->distinct()
            ->pluck('building')
            ->toArray();
        
        return view('collegeadmin.room.index', compact('rooms', 'college', 'uniqueRooms', 'uniqueBuildings'));
    }

    /**
     * Store a newly created room
     */
    public function store(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Validate the request
        $request->validate([
            'room' => 'required|string|max:20',
            'building' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
        ]);
        
        // Check if room already exists
        $checkExists = CtrRoom::where('room', $request->room)
            ->where('building', $request->building)
            ->first();
            
        if ($checkExists) {
            Session::flash('error', "Room '{$request->room}' in building '{$request->building}' already exists!");
            return redirect()->route('collegeadmin.room.index');
        }
        
        // Create new room
        $newRoom = new CtrRoom();
        $newRoom->room = $request->room;
        $newRoom->building = $request->building;
        $newRoom->college_code = $collegeCode;
        $newRoom->description = $request->description ?? '';
        $newRoom->is_active = 1;
        $newRoom->save();
        
        Session::flash('success', "Room '{$request->room}' in building '{$request->building}' successfully created!");
        return redirect()->route('collegeadmin.room.index');
    }

    /**
     * Show the form for editing the specified room
     */
    public function edit($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get the room
        $room = CtrRoom::where('id', $id)
            ->where('college_code', $collegeCode)
            ->firstOrFail();
        
        return view('collegeadmin.room.edit', compact('room'));
    }

    /**
     * Update the specified room
     */
    public function update(Request $request, $id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get the room
        $room = CtrRoom::where('id', $id)
            ->where('college_code', $collegeCode)
            ->firstOrFail();
        
        // Validate the request
        $request->validate([
            'room' => 'required|string|max:20',
            'building' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
        ]);
        
        // Check if room already exists (excluding this room)
        $checkExists = CtrRoom::where('room', $request->room)
            ->where('building', $request->building)
            ->where('id', '!=', $id)
            ->first();
            
        if ($checkExists) {
            Session::flash('error', "Room '{$request->room}' in building '{$request->building}' already exists!");
            return redirect()->route('collegeadmin.room.index');
        }
        
        // Update the room
        $room->room = $request->room;
        $room->building = $request->building;
        $room->description = $request->description ?? '';
        $room->save();
        
        Session::flash('success', "Room '{$request->room}' in building '{$request->building}' successfully updated!");
        return redirect()->route('collegeadmin.room.index');
    }

    /**
     * Archive the specified room
     */
    public function archive($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get the room
        $room = CtrRoom::where('id', $id)
            ->where('college_code', $collegeCode)
            ->firstOrFail();
        
        // Archive the room
        $room->is_active = 0;
        $room->save();
        
        Session::flash('success', "Room '{$room->room}' in building '{$room->building}' successfully archived!");
        return redirect()->route('collegeadmin.room.index');
    }

    /**
     * Display a listing of archived rooms
     */
    public function archived()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get archived rooms for this college
        $rooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 0)
            ->get();
        
        return view('collegeadmin.room.archived', compact('rooms'));
    }

    /**
     * Restore an archived room
     */
    public function restore($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get the room
        $room = CtrRoom::where('id', $id)
            ->where('college_code', $collegeCode)
            ->where('is_active', 0)
            ->firstOrFail();
        
        // Restore the room
        $room->is_active = 1;
        $room->save();
        
        Session::flash('success', "Room '{$room->room}' in building '{$room->building}' successfully restored!");
        return redirect()->route('collegeadmin.room.index');
    }
}
