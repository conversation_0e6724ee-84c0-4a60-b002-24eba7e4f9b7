<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddCollegeCodeToCtrSectionsTableManual extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Skip this migration as it's already been applied
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Check if the column exists before dropping it
        if (Schema::hasColumn('ctr_sections', 'college_code')) {
            // Drop college_code column using raw SQL
            DB::statement('ALTER TABLE ctr_sections DROP COLUMN college_code');
        }
    }
}
