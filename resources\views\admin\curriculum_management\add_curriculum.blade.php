<?php
$layout = '';

if (Auth::user()->is_first_login == 1) {
    $layout = 'layouts.first_login';
} else {
    if (Auth::user()->accesslevel == 100) {
        $layout = 'layouts.superadmin';
    } elseif (Auth::user()->accesslevel == 1) {
        $layout = 'layouts.instructor';
    } elseif (Auth::user()->accesslevel == 0) {
        $layout = 'layouts.admin';
    }
}

?>
<?php
$programs = \App\academic_programs::distinct()
    ->orderBy('program_code')
    ->get(['program_code', 'program_name']); ?>
@extends($layout)

@section('title', 'AdminLTE')

@section('content_header')
    <h1>Dashboard</h1>
@stop
@section('main-content')
    <link rel='stylesheet' href='{{ asset('plugins/select2/select2.css') }}'>
    <section class="content-header">
        <h1><i class="fa fa-bullhorn"></i>
            Add Curriculum
            <small></small>
        </h1>

        <ol class="breadcrumb">
            <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
            <li><a href="#"> Curriculum Management</a></li>
            <li class="active"><a>Add Curriculum</a></li>
        </ol>

        <div class="pull-right">
            <a href="{{ url('/admin/curriculum_management/curriculum') }}" class="btn btn-flat btn-default">
                <i class="fa fa-arrow-left"></i> Back
            </a>
        </div>
        <hr>
    </section>

    <section class="content container-fluid">

        <div class="box box-default">

            <form action="{{ url('admin/curriculum_management/add_curriculum/save_changes') }}" method="post">
                {{ csrf_field() }}



                <div class="box-header"><i></i>
                    <h5 class='box-title'></h5>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dynamic_field">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th width="15%">College Code</th>
                                    <th width="20%">Program Course</th>
                                    <th>Curriculum Year</th>
                                    <th width="15%">Period</th>
                                    <th width="12%">Level</th>
                                    <th width="10%">Subject Code</th>
                                    <th width="20%">Subject Description</th>
                                    <th width="15%">Lec</th>
                                    <th width="15%">Lab</th>
                                    <th width="15%">Units</th>
                                    <th width="15%">Complab</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <button type="button" class="add btn btn-flat btn-primary" title="Add Empty Row">
                                            <i class="fa fa-plus-circle"></i>
                                        </button>
                                        <button type="button" class="btn btn-flat btn-info" onclick="quickAdd()" title="Quick Add (Duplicate Last Row)">
                                            <i class="fa fa-clone"></i>
                                        </button>
                                    </td>

                                    <td>
                                        <select class="form-control select2" style="width: 100%" id="college1" name="college_code[]">
                                            <option value="">Select College</option>
                                            @foreach ($colleges as $college)
                                                <option value="{{ $college->college_code }}">{{ $college->college_code }}</option>
                                            @endforeach
                                        </select>
                                    </td>

                                    <td>
                                        <select class="form-control select2" style="width: 100%" id="program1" name="program_code[]" data-college-input="college1">
                                            <option value="">Select Program</option>
                                            @foreach ($uniquePrograms as $program)
                                                <option value="{{ $program->program_code }}" data-college="{{ $program->college_code }}">
                                                    {{ $program->program_code }} 
                                                    @if($program->college_code)
                                                        ({{ $program->college_code }})
                                                    @endif
                                                </option>
                                            @endforeach
                                        </select>
                                        <small class="text-muted">Programs filtered by college</small>
                                        <!-- Hidden input to ensure college_code is always passed -->
                                        <input type="hidden" id="hidden_college1" name="hidden_college_code[]" value="">
                                    </td>

                                    <td><input type="text" class="form-control" style="width: 10rem" name="curriculum_year[]" id="c_year1">
                                    </td>


                                    <td>
                                        <select class="select2 form-control" style="width: 100%" id="period1" name="period[]">
                                            <option value="1st Semester">1st Semester</option>
                                            <option value="2nd Semester">2nd Semester</option>
                                            <option value="Mid-year">Mid Year</option>


                                        </select>
                                    </td>


                                    <td>
                                        <select class="select2 form-control" style="width: 100%" id="level1" name="level[]">
                                            <option value="1st Year">1st Year</option>
                                            <option value="2nd Year">2nd Year</option>
                                            <option value="3rd Year">3rd Year</option>
                                            <option value="4th Year">4th Year</option>
                                            <option value="5th Year">5th Year</option>

                                        </select>
                                    </td>



                                    <td><input type="text" class="form-control" style="width: 8rem" name="course_code[]" id="code1"></td>
                                    <td><input type="text" class="form-control" style="width: 20rem" name="course_name[]" id="name1"></td>
                                    <td><input type="text" class="form-control" style="width: 4rem" name="lec[]" id="lec1"></td>
                                    <td><input type="text" class="form-control" style="width: 4rem" name="lab[]" id="lab1"></td>
                                    <td><input type="text" class="form-control" style="width: 4rem" name="units[]" id="units1"></td>
                                    <td align="center"><select class='form-control' style="width: rem" name='complab[]' id='complab1'>
                                            <option value='0'>No</option>
                                            <option value='1'>Yes</option>
                                        </select></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="box-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_to_offerings">Add to Course Offerings:</label>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="add_to_offerings" id="add_to_offerings_yes" value="yes">
                                        Yes - Automatically add subjects to course offerings for existing sections
                                    </label>
                                </div>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="add_to_offerings" id="add_to_offerings_no" value="no" checked>
                                        No - Only save curriculum subjects
                                    </label>
                                </div>
                                <small class="text-muted" id="sections_info">Checking for available sections...</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="pull-right">
                                <button onclick="submit()" class="btn btn-flat btn-success"><i class="fa fa-check-circle"></i> Save
                                    Changes</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <script></script>
@endsection

@section('footer-script')
    <script src='{{ asset('plugins/select2/select2.js') }}'></script>
    <script src="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.js"></script>
    <script>
        // Store programs by college for filtering
        var programsByCollege = @json($programsByCollege);

        $(document).ready(function() {
            $('.select2').select2({
                width: '100%'
            });

            // Initialize the program dropdown based on the selected college
            $('#college1').on('change', function() {
                var collegeCode = $(this).val();
                updateProgramDropdown('#program1', collegeCode);
                // Update hidden college input
                $('#hidden_college1').val(collegeCode);
            });

            // Update hidden college input when program changes
            $('#program1').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var collegeCode = selectedOption.data('college');
                if (collegeCode) {
                    $('#hidden_college1').val(collegeCode);
                } else {
                    // If no college in program, use the selected college
                    var selectedCollege = $('#college1').val();
                    if (selectedCollege) {
                        $('#hidden_college1').val(selectedCollege);
                    }
                }
            });
        });

        // Function to update program dropdown based on selected college
        function updateProgramDropdown(programSelector, collegeCode) {
            var $programDropdown = $(programSelector);
            $programDropdown.empty();

            // Add default option
            $programDropdown.append('<option value="">Select Program</option>');

            // If a college is selected and programs exist for that college
            if (collegeCode && programsByCollege[collegeCode]) {
                // Add programs for the selected college
                $.each(programsByCollege[collegeCode], function(index, program) {
                    $programDropdown.append('<option value="' + program.program_code + '" data-college="' + collegeCode + '">' +
                        program.program_code + ' - ' + program.program_name + ' (' + collegeCode + ')</option>');
                });
            } else {
                // If no college selected or no programs for that college, add all programs
                @foreach ($uniquePrograms as $program)
                    $programDropdown.append('<option value="{{ $program->program_code }}" data-college="{{ $program->college_code }}">' +
                        '{{ $program->program_code }} - {{ $program->program_name }}' +
                        '@if($program->college_code) ({{ $program->college_code }}) @endif' +
                        '</option>');
                @endforeach
            }

            // Refresh select2
            $programDropdown.trigger('change');
        }

        var no = 1;
        $('.add').on('click', function(e) {
            if ($("#c_year" + no).val() == "" || $("#code" + no).val() == "" || $("#name" + no).val() == "" || $(
                    "#lec" + no).val() == "" || $("#lab" + no).val() == "" || $("#units" + no).val() == "") {
                toastr.warning("Please Fill-up Required Fields ");
            } else {
                no++;
                var collegeOptions = "";
                @foreach ($colleges as $college)
                    collegeOptions += "<option value='{{ $college->college_code }}'>{{ $college->college_code }} - {{ $college->college_name }}</option>";
                @endforeach

                $('#dynamic_field').append("<tr id='row" + no + "'>\n\
                                        <td><button class='btn btn-flat btn-danger remove' id='" + no + "'><i class='fa fa-close'></i></button></td>\n\
                                        <td><select class='form-control select2-college' style='width: 100%' name='college_code[]' id='college" + no + "'><option value=''>Select College</option>" + collegeOptions + "</select></td>\n\
                                        <td><select class='form-control select2' style='width: 100%' name='program_code[]' id='program" + no + "' data-college-input='college" + no + "'><option value=''>Select Program</option></select><input type='hidden' id='hidden_college" + no + "' name='hidden_college_code[]' value=''></td>\n\
                                        <td><input type='text' name='curriculum_year[]' class='form-control' style='width: 100%' id='c_year" +
                    no + "'></td>\n\
                                        <td><select class='form-control select2' style='width: 100%' name='period[]' id='period" + no + "'><option value='1st Semester'>1st Semester</option><option value='2nd Semester'>2nd Semester</option><option value='1st Trimester'>1st Trimester</option><option value='2nd Trimester'>2nd Trimester</option><option value='3rd Trimester'>3rd Trimester</option><option value='Summer'>Summer</option></select></td>\n\
                                        <td><select class='form-control select2' style='width: 100%' name='level[]' id='level" + no + "'><option value='1st Year'>1st Year</option><option value='2nd Year'>2nd Year</option><option value='3rd Year'>3rd Year</option><option value='Mid Year'>Mid Year</option><option value='4th Year'>4th Year</option><option value='5th Year'>5th Year</option></select></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='course_code[]' id='code" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='course_name[]' id='name" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='lec[]' id='lec" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='lab[]' id='lab" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='units[]' id='units" + no + "'></td>\n\
                                        <td align='center'><select class='form-control' style='width: 100%' id='complab" + no + "' name='complab[]'><option value='0'>No</option><option value='1'>Yes</option></select></td>\n\
                                    </tr>");
                $('.select2, .select2-college').select2({
                    width: '100%'
                });

                // Add change event handler for the college dropdown
                $('#college' + no).on('change', function() {
                    var collegeCode = $(this).val();
                    var programId = '#program' + no;
                    updateProgramDropdown(programId, collegeCode);
                    // Update hidden college input
                    $('#hidden_college' + no).val(collegeCode);
                });

                // Add change event handler for the program dropdown
                $('#program' + no).on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var collegeCode = selectedOption.data('college');
                    var hiddenId = '#hidden_college' + no;
                    if (collegeCode) {
                        $(hiddenId).val(collegeCode);
                    } else {
                        // If no college in program, use the selected college
                        var selectedCollege = $('#college' + no).val();
                        if (selectedCollege) {
                            $(hiddenId).val(selectedCollege);
                        }
                    }
                });
            }
            e.preventDefault();
            return false;
        })

        $('#dynamic_field').on('click', '.remove', function(e) {
            var button_id = $(this).attr("id");
            $("#row" + button_id + "").remove();
            i--;
            e.preventDefault();
            return false;
        });

        // Check for available sections when program or level changes
        function checkAvailableSections() {
            // Get the program and level from the first row (or any active row)
            const programCode = $('#program1').val();
            const level = $('#level1').val();

            if (!programCode || !level) {
                $('#sections_info').text('Please select a program and level to check for available sections');
                return;
            }

            // Show loading message
            $('#sections_info').text('Checking for available sections...');

            // Make AJAX request to check for sections
            $.ajax({
                url: '{{ url("/ajax/admin/course_offerings/get_program_sections") }}',
                type: 'GET',
                data: {
                    program_code: programCode,
                    level: level
                },
                success: function(response) {
                    if (response.success) {
                        if (response.sections.length > 0) {
                            $('#sections_info').html('<span class="text-success"><i class="fa fa-check-circle"></i> ' +
                                response.sections.length + ' section(s) found for this program and level. Subjects can be added to course offerings.</span>');
                            $('#add_to_offerings_yes').prop('disabled', false);
                        } else {
                            $('#sections_info').html('<span class="text-warning"><i class="fa fa-exclamation-circle"></i> ' +
                                'No sections found for this program and level. Create sections first to add to course offerings.</span>');
                            $('#add_to_offerings_yes').prop('disabled', true);
                            $('#add_to_offerings_no').prop('checked', true);
                        }
                    } else {
                        $('#sections_info').html('<span class="text-danger"><i class="fa fa-times-circle"></i> ' +
                            'Error checking for sections: ' + response.message + '</span>');
                        $('#add_to_offerings_yes').prop('disabled', true);
                        $('#add_to_offerings_no').prop('checked', true);
                    }
                },
                error: function() {
                    $('#sections_info').html('<span class="text-danger"><i class="fa fa-times-circle"></i> ' +
                        'Error connecting to server. Please try again.</span>');
                    $('#add_to_offerings_yes').prop('disabled', true);
                    $('#add_to_offerings_no').prop('checked', true);
                }
            });
        }

        // Add event listeners for program and level changes
        $(document).ready(function() {
            // Initial check for sections
            setTimeout(checkAvailableSections, 1000); // Delay to ensure dropdowns are initialized

            // Check when program or level changes
            $('#program1, #level1').on('change', function() {
                checkAvailableSections();
            });
        });

        function quickAdd() {
            if (no < 1) return; // Ensure there's at least one row

            // Get values from the last row
            const lastRow = {
                college: $(`#college${no}`).val(),
                program: $(`#program${no}`).val(),
                curriculum_year: $(`#c_year${no}`).val(),
                period: $(`#period${no}`).val(),
                level: $(`#level${no}`).val(),
                code: $(`#code${no}`).val(),
                name: $(`#name${no}`).val(),
                lec: $(`#lec${no}`).val(),
                lab: $(`#lab${no}`).val(),
                units: $(`#units${no}`).val(),
                complab: $(`#complab${no}`).val()
            };

            // Validate required fields
            if (!lastRow.curriculum_year || !lastRow.code || !lastRow.name ||
                !lastRow.lec || !lastRow.lab || !lastRow.units) {
                toastr.warning("Please fill up the last row first");
                return;
            }

            // Increment the code if it ends with a number
            const codeMatch = lastRow.code.match(/(\D+)(\d+)$/);
            const newCode = codeMatch
                ? `${codeMatch[1]}${parseInt(codeMatch[2]) + 1}`
                : lastRow.code;

            no++;
            // Add new row with incremented values
            $('#dynamic_field').append(`
                <tr id='row${no}'>
                    <td><button class='btn btn-flat btn-danger remove' id='${no}'><i class='fa fa-close'></i></button></td>
                    <td><select class='form-control select2-college' style='width: 100%' name='college_code[]' id='college${no}'>
                        <option value='${lastRow.college}'>${lastRow.college}</option></select></td>
                    <td><select class='form-control select2' style='width: 100%' name='program_code[]' id='program${no}' data-college-input='college${no}'>
                        <option value='${lastRow.program}'>${lastRow.program}</option></select>
                        <input type='hidden' id='hidden_college${no}' name='hidden_college_code[]' value='${lastRow.college}'></td>
                    <td><input type='text' name='curriculum_year[]' class='form-control' style='width: 100%' id='c_year${no}' value='${lastRow.curriculum_year}'></td>
                    <td><select class='form-control' name='period[]' id='period${no}'><option value='${lastRow.period}'>${lastRow.period}</option></select></td>
                    <td><select class='form-control' name='level[]' id='level${no}'><option value='${lastRow.level}'>${lastRow.level}</option></select></td>
                    <td><input type='text' class='form-control' style='width: 100%' name='course_code[]' id='code${no}' value='${newCode}'></td>
                    <td><input type='text' class='form-control' style='width: 100%' name='course_name[]' id='name${no}' value='${lastRow.name}'></td>
                    <td><input type='text' class='form-control' style='width: 100%' name='lec[]' id='lec${no}' value='${lastRow.lec}'></td>
                    <td><input type='text' class='form-control' style='width: 100%' name='lab[]' id='lab${no}' value='${lastRow.lab}'></td>
                    <td><input type='text' class='form-control' style='width: 100%' name='units[]' id='units${no}' value='${lastRow.units}'></td>
                    <td align='center'><select class='form-control' style='width: 100%' id='complab${no}' name='complab[]'>
                        <option value='${lastRow.complab}'>${lastRow.complab === '1' ? 'Yes' : 'No'}</option></select></td>
                </tr>
            `);

            // Initialize select2 for the new row
            $(`#college${no}, #program${no}, #period${no}, #level${no}`).select2({
                width: '100%'
            });

            // Add change event handler for the college dropdown
            $(`#college${no}`).on('change', function() {
                var collegeCode = $(this).val();
                var programId = `#program${no}`;
                updateProgramDropdown(programId, collegeCode);
                $(`#hidden_college${no}`).val(collegeCode);
            });
        }
    </script>
@endsection



