<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-tasks"></i>
        Faculty Loading
        <small>{{ isset($college) ? $college->college_name : Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Loading</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Members</h3>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($instructors) > 0)
                        <div class="table-responsive">
                            <table id="faculty-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Faculty Status</th>
                                        <th>Department</th>
                                        <th>Current Load</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($instructors as $instructor)
                                        @php
                                            $info = $instructor->instructorInfo;
                                        @endphp
                                        <tr>
                                            <td>{{ $instructor->username ?? $instructor->id }}</td>
                                            <td>{{ strtoupper($instructor->lastname) }}, {{ strtoupper($instructor->name) }} {{ strtoupper($instructor->middlename) }}</td>
                                            <td>{{ $instructor->email }}</td>
                                            <td>{{ $info ? $info->employee_type : 'N/A' }}</td>
                                            <td>{{ $info ? $info->department : 'N/A' }}</td>
                                            <td>
                                                <span class="instructor-load" data-instructor="{{ $instructor->id }}">Loading...</span>
                                            </td>
                                            <td>
                                                <a href="{{ route('collegeadmin.faculty_loading.generate_schedule', $instructor->id) }}" class="btn btn-info btn-sm">
                                                    <i class="fa fa-calendar"></i> View Schedule
                                                </a>
                                                <a href="{{ route('collegeadmin.course_scheduling.index') }}" class="btn btn-primary btn-sm">
                                                    <i class="fa fa-calendar"></i> Course Schedule
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No faculty members found for your college.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for adding courses -->
    <div class="modal fade" id="addCourseModal" tabindex="-1" role="dialog" aria-labelledby="addCourseModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="addCourseModalLabel">Add Course to Faculty Load</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="search-course">Search Course</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search-course" placeholder="Enter course code or name">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" id="search-course-btn">Search</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="available-courses">
                        <p>Search for courses to add to faculty load.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script src="{{ asset('plugins/datatables/jquery.dataTables.js') }}"></script>
<script src="{{ asset('plugins/datatables/dataTables.bootstrap.js') }}"></script>
<script>
$(function() {
    // Initialize DataTables
    $('#faculty-table').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false
    });
    // Load current load for each instructor
    $('.instructor-load').each(function() {
        var instructorId = $(this).data('instructor');
        var element = $(this);

        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/current_load') }}",
            type: 'GET',
            data: { instructor_id: instructorId, count_only: true },
            success: function(data) {
                element.html(data);
            },
            error: function() {
                element.html('<span class="text-danger">Error</span>');
            }
        });
    });

    // Handle click on Add Course button
    $('.load-courses').click(function() {
        var instructorId = $(this).data('instructor');
        $('#addCourseModal').data('instructor', instructorId);
        $('#addCourseModal').modal('show');
    });

    // Handle search button click
    $('#search-course-btn').click(function() {
        var searchTerm = $('#search-course').val();
        var instructorId = $('#addCourseModal').data('instructor');

        if (searchTerm.length < 2) {
            alert('Please enter at least 2 characters to search.');
            return;
        }

        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/search_courses') }}",
            type: 'GET',
            data: {
                search: searchTerm,
                instructor_id: instructorId
            },
            success: function(data) {
                $('#available-courses').html(data);
            },
            error: function() {
                $('#available-courses').html('<p class="text-danger">Error searching for courses.</p>');
            }
        });
    });

    // Handle Enter key in search box
    $('#search-course').keypress(function(e) {
        if (e.which == 13) {
            $('#search-course-btn').click();
            return false;
        }
    });

    // Handle add course to faculty load (delegated event)
    $(document).on('click', '.add-to-load', function() {
        var instructorId = $('#addCourseModal').data('instructor');
        var offeringId = $(this).data('offering');

        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/add_faculty_load') }}",
            type: 'GET',
            data: {
                instructor_id: instructorId,
                offering_id: offeringId
            },
            success: function(data) {
                if (data.success) {
                    alert('Course added to faculty load successfully!');
                    // Refresh the current load count
                    $('.instructor-load[data-instructor="' + instructorId + '"]').html('Updating...');
                    $.ajax({
                        url: "{{ url('/ajax/collegeadmin/faculty_loading/current_load') }}",
                        type: 'GET',
                        data: { instructor_id: instructorId, count_only: true },
                        success: function(data) {
                            $('.instructor-load[data-instructor="' + instructorId + '"]').html(data);
                        }
                    });
                    // Refresh the search results
                    $('#search-course-btn').click();
                } else {
                    alert('Error: ' + data.message);
                }
            },
            error: function() {
                alert('Error adding course to faculty load.');
            }
        });
    });
});
</script>
@endsection
