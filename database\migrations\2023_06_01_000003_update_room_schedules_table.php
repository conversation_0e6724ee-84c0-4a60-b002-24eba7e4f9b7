<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateRoomSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('room_schedules', function (Blueprint $table) {
            $table->string('college_code', 20)->nullable()->after('room');
            $table->integer('priority_level')->default(0)->after('college_code');
            $table->string('day_type')->nullable()->after('day'); // MWF, TTh, Saturday, or Individual
            
            $table->foreign('college_code')
                ->references('college_code')->on('colleges')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('room_schedules', function (Blueprint $table) {
            $table->dropForeign(['college_code']);
            $table->dropColumn('college_code');
            $table->dropColumn('priority_level');
            $table->dropColumn('day_type');
        });
    }
}
