<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SchedulePriority extends Model
{
    protected $table = 'schedule_priorities';

    protected $fillable = [
        'curriculum_id',
        'priority_level',
        'description',
        'is_active'
    ];

    /**
     * Get the curriculum that owns the priority
     */
    public function curriculum()
    {
        return $this->belongsTo('App\curriculum', 'curriculum_id');
    }

    /**
     * Get priority level for a curriculum
     *
     * @param int $curriculumId
     * @return int
     */
    public static function getPriorityLevel($curriculumId)
    {
        $priority = self::where('curriculum_id', $curriculumId)
            ->where('is_active', 1)
            ->first();

        return $priority ? $priority->priority_level : 0;
    }

    /**
     * Set priority level for a curriculum
     *
     * @param int $curriculumId
     * @param int $priorityLevel
     * @param string $description
     * @return SchedulePriority
     */
    public static function setPriority($curriculumId, $priorityLevel, $description = null)
    {
        $priority = self::firstOrNew(['curriculum_id' => $curriculumId]);
        $priority->priority_level = $priorityLevel;
        $priority->description = $description;
        $priority->is_active = 1;
        $priority->save();

        return $priority;
    }
}
