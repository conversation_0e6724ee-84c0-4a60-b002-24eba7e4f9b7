<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-file-text"></i>
        Faculty Reports
        <small>{{ Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Reports</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Teaching Load</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-success btn-sm" id="print-report">
                            <i class="fa fa-print"></i> Print Report
                        </button>
                    </div>
                </div>
                <div class="box-body">
                    @if(count($faculty) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="faculty-report-table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Teaching Load</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($faculty as $member)
                                        <tr>
                                            <td>{{ $member->name }} {{ $member->middlename }} {{ $member->lastname }}</td>
                                            <td>{{ $member->email }}</td>
                                            <td>
                                                <span class="faculty-load" data-faculty="{{ $member->id }}">Loading...</span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-info btn-sm view-schedule" data-faculty="{{ $member->id }}">
                                                    <i class="fa fa-calendar"></i> View Schedule
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No faculty members found for your college.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for viewing faculty schedule -->
    <div class="modal fade" id="facultyScheduleModal" tabindex="-1" role="dialog" aria-labelledby="facultyScheduleModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="facultyScheduleModalLabel">Faculty Schedule</h4>
                </div>
                <div class="modal-body">
                    <div id="faculty-schedule">
                        <p>Loading schedule...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="print-schedule">
                        <i class="fa fa-print"></i> Print Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
$(function() {
    // Load faculty teaching load
    $('.faculty-load').each(function() {
        var facultyId = $(this).data('faculty');
        var element = $(this);
        
        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/current_load') }}",
            type: 'GET',
            data: { instructor_id: facultyId, count_only: true },
            success: function(data) {
                element.html(data);
            },
            error: function() {
                element.html('<span class="text-danger">Error</span>');
            }
        });
    });
    
    // Handle view schedule button click
    $('.view-schedule').click(function() {
        var facultyId = $(this).data('faculty');
        
        // Get faculty name
        var facultyName = $(this).closest('tr').find('td:first').text();
        $('#facultyScheduleModalLabel').text('Schedule for ' + facultyName);
        
        // Show modal
        $('#facultyScheduleModal').modal('show');
        
        // Load faculty schedule
        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/current_load') }}",
            type: 'GET',
            data: { instructor_id: facultyId },
            success: function(data) {
                $('#faculty-schedule').html(data);
            },
            error: function() {
                $('#faculty-schedule').html('<p class="text-danger">Error loading schedule.</p>');
            }
        });
    });
    
    // Handle print report button click
    $('#print-report').click(function() {
        window.print();
    });
    
    // Handle print schedule button click
    $('#print-schedule').click(function() {
        var content = $('#faculty-schedule').html();
        var title = $('#facultyScheduleModalLabel').text();
        
        var printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>' + title + '</title>');
        printWindow.document.write('<link rel="stylesheet" href="{{ asset('/css/bootstrap.min.css') }}" type="text/css" />');
        printWindow.document.write('</head><body>');
        printWindow.document.write('<h1>' + title + '</h1>');
        printWindow.document.write(content);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.print();
    });
});
</script>
@endsection
