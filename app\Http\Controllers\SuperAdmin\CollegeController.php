<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use Auth;
use Session;
use Validator;

class CollegeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    public function index()
    {
        $colleges = College::where('is_active', 1)->get();
        return view('super_admin.college.index', compact('colleges'));
    }

    public function archive()
    {
        $colleges = College::where('is_active', 0)->get();
        return view('super_admin.college.archive', compact('colleges'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'college_code' => 'required|unique:colleges,college_code|max:20',
            'college_name' => 'required|max:100',
            'program_code' => 'nullable',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Create the college
        $college = new College();
        $college->college_code = strtoupper($request->college_code);
        $college->college_name = $request->college_name;
        $college->is_active = 1;
        $college->save();

        // Add programs to programs table
        if (is_array($request->program_code)) {
            foreach ($request->program_code as $programCode) {
                if (!empty($programCode)) {
                    $college->addProgram($programCode);
                }
            }
        } else if (!empty($request->program_code)) {
            $college->addProgram($request->program_code);
        }

        Session::flash('success', 'College has been added successfully!');
        return redirect('/superadmin/college');
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'college_code' => 'required|max:20|unique:colleges,college_code,' . $request->college_id,
            'college_name' => 'required|max:100',
            'program_code' => 'nullable',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json(['errors' => $validator->errors()], 422);
            } else {
                return redirect()->back()->withErrors($validator)->withInput();
            }
        }

        $college = College::find($request->college_id);
        $college->college_code = strtoupper($request->college_code);
        $college->college_name = $request->college_name;
        $college->save();

        // Get current programs for this college
        $currentPrograms = \App\Program::where('college_id', $college->id)
            ->pluck('program_code')
            ->toArray();

        // Get new programs from request
        $newPrograms = is_array($request->program_code) ? $request->program_code : [$request->program_code];
        $newPrograms = array_filter($newPrograms); // Remove empty values

        // Add new programs
        foreach ($newPrograms as $programCode) {
            if (!in_array($programCode, $currentPrograms)) {
                $college->addProgram($programCode);
            }
        }

        // Remove programs that are no longer associated with this college
        foreach ($currentPrograms as $programCode) {
            if (!in_array($programCode, $newPrograms)) {
                $college->removeProgram($programCode);
            }
        }

        if ($request->ajax()) {
            return response()->json(['success' => true, 'message' => 'College has been updated successfully!']);
        } else {
            Session::flash('success', 'College has been updated successfully!');
            return redirect('/superadmin/college');
        }
    }

    public function archive_college($college_id)
    {
        $college = College::find($college_id);

        if ($college->is_active == 1) {
            $college->is_active = 0;
            $message = 'College has been archived successfully!';
        } else {
            $college->is_active = 1;
            $message = 'College has been restored successfully!';
        }

        $college->save();

        Session::flash('success', $message);

        if ($college->is_active == 1) {
            return redirect('/superadmin/college');
        } else {
            return redirect('/superadmin/college/archive');
        }
    }
}
