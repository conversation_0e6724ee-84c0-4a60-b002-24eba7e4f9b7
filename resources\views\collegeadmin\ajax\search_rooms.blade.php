@if(count($rooms) > 0)
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Room</th>
                    <th>Building</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($rooms as $room)
                    <tr>
                        <td>{{ $room->room }}</td>
                        <td>{{ $room->building }}</td>
                        <td>{{ $room->description }}</td>
                        <td>
                            <a href="{{ route('collegeadmin.room.edit', $room->id) }}" class="btn btn-primary btn-sm">
                                <i class="fa fa-pencil"></i> Edit
                            </a>
                            <a href="{{ route('collegeadmin.room.archive', $room->id) }}" class="btn btn-warning btn-sm" onclick="return confirm('Are you sure you want to archive this room?')">
                                <i class="fa fa-archive"></i> Archive
                            </a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info">
        No rooms found matching your search criteria.
    </div>
@endif
