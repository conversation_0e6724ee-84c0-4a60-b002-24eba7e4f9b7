<?php
$layout = '';

if (Auth::user()->is_first_login == 1) {
    $layout = 'layouts.first_login';
} else {
    if (Auth::user()->accesslevel == 100) {
        $layout = 'layouts.superadmin';
    } elseif (Auth::user()->accesslevel == 1) {
        $layout = 'layouts.instructor';
    } elseif (Auth::user()->accesslevel == 0) {
        $layout = 'layouts.admin';
    }
}

?>
<?php
$programs = \App\academic_programs::distinct()
    ->orderBy('program_code')
    ->get(['program_code', 'program_name']); ?>
@extends($layout)

@section('title', 'AdminLTE')

@section('content_header')
    <h1>Dashboard</h1>
@stop
@section('main-content')
    <link rel='stylesheet' href='{{ asset('plugins/select2/select2.css') }}'>
    <section class="content-header">
        <h1><i class="fa fa-bullhorn"></i>
            Add Curriculum
            <small></small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
            <li><a href="#"> Curriculum Management</a></li>
            <li class="active"><a>Add Curriculum</a></li>
        </ol>
    </section>
    <section class="content container-fluid">

        <div class="box box-default">
            <form action="{{ url('superadmin/curriculum_management/add_curriculum/save_changes') }}" method="post">
                {{ csrf_field() }}
                <div class="box-header"><i></i>
                    <h5 class='box-title'></h5>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dynamic_field">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th width="15%">College Code</th>
                                    <th width="20%">Program Course</th>
                                    <th>Curriculum Year</th>
                                    <th width="15%">Period</th>
                                    <th width="12%">Level</th>
                                    <th width="10%">Subject Code</th>
                                    <th width="20%">Description Title</th>
                                    <th width="7%">Lec</th>
                                    <th width="7%">Lab</th>
                                    <th width="7%">Units</th>
                                    <th width="7%">Complab</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><button class="add btn btn-flat btn-primary"><i
                                                class="fa fa-plus-circle"></i></button></td>

                                                <td>
                                                    <select class="form-control select2" style="width: 100%" id="college1" name="college_code[]">
                                                        <option value="">Select College</option>
                                                        @foreach ($colleges as $college)
                                                            <option value="{{ $college->college_code }}">{{ $college->college_code }} - {{ $college->college_name }}</option>
                                                        @endforeach
                                                    </select>
                                                </td>

                                                <td>
                                                    <select class="form-control select2" style="width: 100%" id="program1" name="program_code[]" data-college-input="college1">
                                                        <option value="">Select Program</option>
                                                        @foreach ($uniquePrograms as $program)
                                                            <option value="{{ $program->program_code }}" data-college="{{ $program->college_code }}">
                                                                {{ $program->program_code }} - {{ $program->program_name }}
                                                                @if($program->college_code)
                                                                    ({{ $program->college_code }})
                                                                @endif
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <small class="text-muted">Programs filtered by college</small>
                                                    <!-- Hidden input to ensure college_code is always passed -->
                                                    <input type="hidden" id="hidden_college1" name="hidden_college_code[]" value="">
                                                </td>

                                    <td><input type="text" class="form-control" style="width: 100%" name="curriculum_year[]" id="c_year1">
                                    </td>


                                    <td>
                                        <select class="select2 form-control" style="width: 100%" id="period1" name="period[]">
                                            <option value="1st Semester">1st Semester</option>
                                            <option value="2nd Semester">2nd Semester</option>
                                            <option value="1st Trimester">1st Trimester</option>
                                            <option value="2nd Trimester">2nd Trimester</option>
                                            <option value="3rd Trimester">3rd Trimester</option>
                                            <option value="Summer">Summer</option>
                                        </select>
                                    </td>


                                    <td>
                                        <select class="select2 form-control" style="width: 100%" id="level1" name="level[]">
                                            <option value="1st Year">1st Year</option>
                                            <option value="2nd Year">2nd Year</option>
                                            <option value="3rd Year">3rd Year</option>
                                            <option value="Mid Year">Mid Year</option>
                                            <option value="4th Year">4th Year</option>
                                            <option value="5th Year">5th Year</option>

                                        </select>
                                    </td>



                                    <td><input type="text" class="form-control" style="width: 100%" name="course_code[]" id="code1"></td>
                                    <td><input type="text" class="form-control" style="width: 100%" name="course_name[]" id="name1"></td>
                                    <td><input type="text" class="form-control" style="width: 100%" name="lec[]" id="lec1"></td>
                                    <td><input type="text" class="form-control" style="width: 100%" name="lab[]" id="lab1"></td>
                                    <td><input type="text" class="form-control" style="width: 100%" name="units[]" id="units1"></td>
                                    <td align="center"><select class='form-control' style="width: 100%" name='complab[]' id='complab1'>
                                            <option value='0'>No</option>
                                            <option value='1'>Yes</option>
                                        </select></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="box-footer">
                    <div class="pull-right">
                        <button onclick="submit()" class="btn btn-flat btn-success"><i class="fa fa-check-circle"></i> Save
                            Changes</button>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <script></script>
@endsection

@section('footer-script')
    <script src='{{ asset('plugins/select2/select2.js') }}'></script>
    <script></script>
    <script>
        // Store programs by college for filtering
        var programsByCollege = @json($programsByCollege);

        $(document).ready(function() {
            $('.select2').select2({
                width: '100%'
            });

            // Initialize the program dropdown based on the selected college
            $('#college1').on('change', function() {
                var collegeCode = $(this).val();
                updateProgramDropdown('#program1', collegeCode);
                // Update hidden college input
                $('#hidden_college1').val(collegeCode);
            });

            // Update hidden college input when program changes
            $('#program1').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var collegeCode = selectedOption.data('college');
                if (collegeCode) {
                    $('#hidden_college1').val(collegeCode);
                } else {
                    // If no college in program, use the selected college
                    var selectedCollege = $('#college1').val();
                    if (selectedCollege) {
                        $('#hidden_college1').val(selectedCollege);
                    }
                }
            });
        });

        // Function to update program dropdown based on selected college
        function updateProgramDropdown(programSelector, collegeCode) {
            var $programDropdown = $(programSelector);
            $programDropdown.empty();

            // Add default option
            $programDropdown.append('<option value="">Select Program</option>');

            // If a college is selected and programs exist for that college
            if (collegeCode && programsByCollege[collegeCode]) {
                // Add programs for the selected college
                $.each(programsByCollege[collegeCode], function(index, program) {
                    $programDropdown.append('<option value="' + program.program_code + '" data-college="' + collegeCode + '">' +
                        program.program_code + ' - ' + program.program_name + ' (' + collegeCode + ')</option>');
                });
            } else {
                // If no college selected or no programs for that college, add all programs
                @foreach ($uniquePrograms as $program)
                    $programDropdown.append('<option value="{{ $program->program_code }}" data-college="{{ $program->college_code }}">' +
                        '{{ $program->program_code }} - {{ $program->program_name }}' +
                        '@if($program->college_code) ({{ $program->college_code }}) @endif' +
                        '</option>');
                @endforeach
            }

            // Refresh select2
            $programDropdown.trigger('change');
        }

        var no = 1;
        $('.add').on('click', function(e) {
            if ($("#c_year" + no).val() == "" || $("#code" + no).val() == "" || $("#name" + no).val() == "" || $(
                    "#lec" + no).val() == "" || $("#lab" + no).val() == "" || $("#units" + no).val() == "") {
                toastr.warning("Please Fill-up Required Fields ");
            } else {
                no++;
                var collegeOptions = "";
                @foreach ($colleges as $college)
                    collegeOptions += "<option value='{{ $college->college_code }}'>{{ $college->college_code }} - {{ $college->college_name }}</option>";
                @endforeach

                $('#dynamic_field').append("<tr id='row" + no + "'>\n\
                                        <td><button class='btn btn-flat btn-danger remove' id='" + no + "'><i class='fa fa-close'></i></button></td>\n\
                                        <td><select class='form-control select2-college' style='width: 100%' name='college_code[]' id='college" + no + "'><option value=''>Select College</option>" + collegeOptions + "</select></td>\n\
                                        <td><select class='form-control select2' style='width: 100%' name='program_code[]' id='program" + no + "' data-college-input='college" + no + "'><option value=''>Select Program</option></select><input type='hidden' id='hidden_college" + no + "' name='hidden_college_code[]' value=''></td>\n\
                                        <td><input type='text' name='curriculum_year[]' class='form-control' style='width: 100%' id='c_year" +
                    no + "'></td>\n\
                                        <td><select class='form-control select2' style='width: 100%' name='period[]' id='period" + no + "'><option value='1st Semester'>1st Semester</option><option value='2nd Semester'>2nd Semester</option><option value='1st Trimester'>1st Trimester</option><option value='2nd Trimester'>2nd Trimester</option><option value='3rd Trimester'>3rd Trimester</option><option value='Summer'>Summer</option></select></td>\n\
                                        <td><select class='form-control select2' style='width: 100%' name='level[]' id='level" + no + "'><option value='1st Year'>1st Year</option><option value='2nd Year'>2nd Year</option><option value='3rd Year'>3rd Year</option><option value='Mid Year'>Mid Year</option><option value='4th Year'>4th Year</option><option value='5th Year'>5th Year</option></select></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='course_code[]' id='code" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='course_name[]' id='name" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='lec[]' id='lec" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='lab[]' id='lab" + no + "'></td>\n\
                                        <td><input type='text' class='form-control' style='width: 100%' name='units[]' id='units" + no + "'></td>\n\
                                        <td align='center'><select class='form-control' style='width: 100%' id='complab" + no + "' name='complab[]'><option value='0'>No</option><option value='1'>Yes</option></select></td>\n\
                                    </tr>");
                $('.select2, .select2-college').select2({
                    width: '100%'
                });

                // Add change event handler for the college dropdown
                $('#college' + no).on('change', function() {
                    var collegeCode = $(this).val();
                    var programId = '#program' + no;
                    updateProgramDropdown(programId, collegeCode);
                    // Update hidden college input
                    $('#hidden_college' + no).val(collegeCode);
                });

                // Add change event handler for the program dropdown
                $('#program' + no).on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var collegeCode = selectedOption.data('college');
                    var hiddenId = '#hidden_college' + no;
                    if (collegeCode) {
                        $(hiddenId).val(collegeCode);
                    } else {
                        // If no college in program, use the selected college
                        var selectedCollege = $('#college' + no).val();
                        if (selectedCollege) {
                            $(hiddenId).val(selectedCollege);
                        }
                    }
                });
            }
            e.preventDefault();
            return false;
        })

        $('#dynamic_field').on('click', '.remove', function(e) {
            var button_id = $(this).attr("id");
            $("#row" + button_id + "").remove();
            i--;
            e.preventDefault();
            return false;
        });
    </script>
@endsection
