<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddProgramCodesToCollegesTableRawSql extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the column already exists
        if (!Schema::hasColumn('colleges', 'program_codes')) {
            // Add the program_codes column
            DB::statement('ALTER TABLE colleges ADD COLUMN program_codes VARCHAR(255) NULL AFTER description');
            
            // Copy data from description to program_codes
            DB::statement('UPDATE colleges SET program_codes = description');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('colleges', 'program_codes')) {
            DB::statement('ALTER TABLE colleges DROP COLUMN program_codes');
        }
    }
}
