<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Starting semester standardization...\n";

// Standardize semester values in the curriculum table
$count1 = DB::table('curricula')
    ->where('period', 'like', '%first%')
    ->orWhere('period', 'like', '%1st%')
    ->update(['period' => 'First Semester']);

echo "Updated {$count1} records to 'First Semester'\n";

$count2 = DB::table('curricula')
    ->where('period', 'like', '%second%')
    ->orWhere('period', 'like', '%2nd%')
    ->update(['period' => 'Second Semester']);

echo "Updated {$count2} records to 'Second Semester'\n";

$count3 = DB::table('curricula')
    ->where('period', 'like', '%summer%')
    ->orWhere('period', 'like', '%mid%')
    ->orWhere('period', 'like', '%mid-year%')
    ->update(['period' => 'Mid-year']);

echo "Updated {$count3} records to 'Mid-year'\n";

echo "Semester standardization completed.\n";
