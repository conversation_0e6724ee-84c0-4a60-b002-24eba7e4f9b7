@extends('adminlte::page')
@section('title', 'Curriculum Management by College')
@section('content_header')
    <h1><i class="fa fa-university"></i> Curriculum Management by College</h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li class="active"><a href="{{ url('/admin/curriculum_management/colleges') }}"> Colleges</a></li>
    </ol>
@stop
@section('main-content')
<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-default">
                <div class="box-header">
                    <h3 class="box-title">Colleges</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/admin/curiculum_management/curriculum') }}" class="btn btn-flat btn-default"><i class="fa fa-graduation-cap"></i> View by Program</a>
                        <a href="{{ url('/admin/curriculum_management/add_curriculum') }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> New Curriculum</a>
                    </div>
                </div>
                <div class="box-body">
                    <div class='table-responsive'>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="15%">College Code</th>
                                    <th width="30%">College Name</th>
                                    <th width="35%">Programs</th>
                                    <th width="20%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($colleges as $college)
                                <tr>
                                    <td>{{ $college->college_code }}</td>
                                    <td>{{ $college->college_name }}</td>
                                    <td>
                                        @if(count($college->courses_array) > 0)
                                            <ul class="list-unstyled">
                                                @foreach($college->courses_array as $course)
                                                    <li><span class="label label-info">{{ $course }}</span></li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <span class="text-muted">No programs specified</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ url('/admin/curriculum_management/college', $college->college_code) }}" class="btn btn-flat btn-primary"><i class="fa fa-eye"></i> View Programs</a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
