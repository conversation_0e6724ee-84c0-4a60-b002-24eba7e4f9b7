<?php

use Illuminate\Database\Seeder;
use App\TimeBlock;

class TimeBlockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // MWF Time Blocks (1 hour per session)
        $this->createMWFBlocks();

        // TTh Time Blocks (1.5 hours per session)
        $this->createTThBlocks();

        // Saturday Time Blocks (3 hours per session)
        $this->createSaturdayBlocks();

        // Individual day blocks
        $this->createIndividualDayBlocks();
    }

    /**
     * Create MWF time blocks (1 hour per session)
     */
    private function createMWFBlocks()
    {
        $startTime = strtotime('08:00:00');
        $endTime = strtotime('17:00:00');
        $interval = 60 * 60; // 1 hour in seconds

        while ($startTime < $endTime) {
            $start = date('H:i:s', $startTime);
            $end = date('H:i:s', $startTime + $interval);
            $displayText = date('g:i A', $startTime) . ' - ' . date('g:i A', $startTime + $interval);

            TimeBlock::create([
                'day_type' => 'MWF',
                'day' => 'MWF',
                'start_time' => $start,
                'end_time' => $end,
                'display_text' => $displayText,
                'is_active' => 1
            ]);

            $startTime += $interval;
        }
    }

    /**
     * Create TTh time blocks (1.5 hours per session)
     */
    private function createTThBlocks()
    {
        $startTime = strtotime('08:00:00');
        $endTime = strtotime('17:00:00');
        $interval = 90 * 60; // 1.5 hours in seconds

        while ($startTime < $endTime) {
            $start = date('H:i:s', $startTime);
            $end = date('H:i:s', $startTime + $interval);
            $displayText = date('g:i A', $startTime) . ' - ' . date('g:i A', $startTime + $interval);

            TimeBlock::create([
                'day_type' => 'TTh',
                'day' => 'TTh',
                'start_time' => $start,
                'end_time' => $end,
                'display_text' => $displayText,
                'is_active' => 1
            ]);

            $startTime += $interval;
        }
    }

    /**
     * Create Saturday time blocks (3 hours per session)
     */
    private function createSaturdayBlocks()
    {
        // Morning block
        TimeBlock::create([
            'day_type' => 'Saturday',
            'day' => 'S',
            'start_time' => '08:00:00',
            'end_time' => '11:00:00',
            'display_text' => '8:00 AM - 11:00 AM',
            'is_active' => 1
        ]);

        // Afternoon block
        TimeBlock::create([
            'day_type' => 'Saturday',
            'day' => 'S',
            'start_time' => '13:00:00',
            'end_time' => '16:00:00',
            'display_text' => '1:00 PM - 4:00 PM',
            'is_active' => 1
        ]);
    }

    /**
     * Create individual day blocks
     */
    private function createIndividualDayBlocks()
    {
        $days = ['M', 'T', 'W', 'Th', 'F'];
        $startTime = strtotime('08:00:00');
        $endTime = strtotime('17:00:00');
        $interval = 60 * 60; // 1 hour in seconds

        foreach ($days as $day) {
            $currentTime = $startTime;

            while ($currentTime < $endTime) {
                $start = date('H:i:s', $currentTime);
                $end = date('H:i:s', $currentTime + $interval);
                $displayText = date('g:i A', $currentTime) . ' - ' . date('g:i A', $currentTime + $interval);

                TimeBlock::create([
                    'day_type' => 'Individual',
                    'day' => $day,
                    'start_time' => $start,
                    'end_time' => $end,
                    'display_text' => $displayText,
                    'is_active' => 1
                ]);

                $currentTime += $interval;
            }
        }
    }
}
