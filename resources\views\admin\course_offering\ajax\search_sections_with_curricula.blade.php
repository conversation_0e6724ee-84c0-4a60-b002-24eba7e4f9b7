<?php use Illuminate\Support\Facades\Schema; ?>
@if(!$sections->isEmpty())
    @foreach($sections as $section)
    <tr>
        @if(Schema::hasColumn('ctr_sections', 'college_code'))
        <td>{{$section->college_code ?? 'N/A'}}</td>
        @endif
        <td>{{$section->program_code}}</td>
        <td>{{$section->level}}</td>
        <td>{{$section->section_name}}</td>
        <td>
            <span class="label label-primary">{{$section->offered_count}} Offered</span>
            @if($section->available_count > 0)
                <span class="label label-warning">{{$section->available_count}} Available</span>
            @endif
        </td>
        <td>
            @if($section->is_active == 1)
            <label class='label label-success'>Active</label>
            @else
            <label class='label label-danger'>Inactive</label>
            @endif
        </td>
        <td>
            <a href="{{url('/admin/section_curriculum/view',[$section->id])}}" class="btn btn-flat btn-primary" title="Manage Curriculum"><i class="fa fa-book"></i></a>
            <a href="{{url('/admin/section_curriculum/add_all',[$section->id])}}" class="btn btn-flat btn-success" title="Add All Available Curriculum Subjects" onclick="return confirm('Add all available curriculum subjects to this section?')"><i class="fa fa-plus-circle"></i></a>
            <button data-toggle="modal" data-target="#myModal" onclick="editsection('{{$section->id}}')" title="Edit Section" class="btn btn-flat btn-info"><i class="fa fa-pencil"></i></button>
            <a href="{{url('/admin/section_management/archive',[$section->id])}}" class="btn btn-flat btn-danger" title="Archive Section" onclick="return confirm('Do you wish to archive this section?')"><i class="fa fa-archive"></i></a>
        </td>
    </tr>
    @endforeach
@else
    <tr>
        <td colspan="{{ Schema::hasColumn('ctr_sections', 'college_code') ? 7 : 6 }}" class="text-center">No sections found matching your search criteria</td>
    </tr>
@endif
