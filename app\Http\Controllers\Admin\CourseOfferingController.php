<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CourseOfferingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('admin');
    }

    public function course_offering_index()
    {
        $colleges = \App\College::where('is_active', 1)->get();

        // Make sure colleges is not null
        if ($colleges === null) {
            $colleges = collect(); // Create an empty collection if null
        }

        return view('admin.course_offering.course_offering', compact('colleges'));
    }

    public function course_offering_college($college_code)
    {
        $college = \App\College::where('college_code', $college_code)->first();
        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        $programs = \App\ProgramHelper::getProgramsByCollegeCode($college_code);

        // If no programs found, show a message
        if (empty($programs)) {
            Session::flash('warning', "No programs found for {$college->college_name}");
        }

        return view('admin.course_offering.course_offering_college', compact('college', 'programs'));
    }

    public function course_offering_program($program_code)
    {
        // Find the program
        $program = \App\ProgramHelper::getProgramByCode($program_code);
        if (!$program) {
            if (request()->ajax()) {
                return response()->json(['error' => "Program with code {$program_code} not found"], 404);
            }
            Session::flash('error', "Program with code {$program_code} not found");
            return redirect('/admin/course_offerings');
        }

        // Get curriculum years for this program
        $curriculum_year = \App\curriculum::distinct()
            ->where('program_code', $program_code)
            ->get(['curriculum_year']);

        // Get active sections for this program
        $sections = \App\CtrSection::where('program_code', $program_code)
            ->where('is_active', 1)
            ->get();

        // Set flash messages only for non-AJAX requests
        if (!request()->ajax()) {
            // If no curriculum years found, show a message
            if ($curriculum_year->isEmpty()) {
                Session::flash('warning', "No curriculum years found for {$program->program_name}");
            }

            // If no sections found, show a message
            if ($sections->isEmpty()) {
                Session::flash('info', "No active sections found for {$program->program_name}");
            }
        }

        $view = request()->ajax()
            ? 'admin.course_offering.partials.program_offerings'
            : 'admin.course_offering.course_offering_program';

        return view($view, compact('curriculum_year', 'program_code', 'program', 'sections'));
    }

    public function section_management()
    {
        // Redirect to the new section curriculum management
        return redirect('/admin/section_curriculum');
    }

    public function section_management_archive()
    {
        // Redirect to the new section curriculum archive
        return redirect('/admin/section_curriculum/archive');
    }

    public function new_section(Request $request)
    {
        // Redirect to the new section curriculum create method
        return app('App\Http\Controllers\Admin\SectionCurriculumController')->createSectionWithCurriculum($request);
    }

    public function archive_section($section_id)
    {
        // Redirect to the new section curriculum archive method
        return app('App\Http\Controllers\Admin\SectionCurriculumController')->archiveSection($section_id);
    }

    public function update_section(Request $request)
    {
        // Redirect to the new section curriculum update method
        return app('App\Http\Controllers\Admin\SectionCurriculumController')->updateSection($request);
    }

    public function room_management()
    {
        $rooms = \App\CtrRoom::where('is_active', 1)->get();
        $programs = \App\academic_programs::distinct()->get(['program_code', 'program_name']);
        $colleges = \App\College::where('is_active', 1)->get();

        // Get unique room numbers and buildings for dropdowns
        $uniqueRooms = \App\CtrRoom::where('is_active', 1)->distinct()->pluck('room')->toArray();
        $uniqueBuildings = \App\CtrRoom::where('is_active', 1)->distinct()->pluck('building')->toArray();
        $uniqueColleges = \App\CtrRoom::where('is_active', 1)->whereNotNull('college_code')->distinct()->pluck('college_code')->toArray();

        return view('admin.course_offering.room_management', compact('rooms', 'programs', 'colleges', 'uniqueRooms', 'uniqueBuildings', 'uniqueColleges'));
    }

    public function room_management_archive()
    {
        $rooms = \App\CtrRoom::where('is_active', 0)->get();
        $colleges = \App\College::where('is_active', 1)->get();

        // Get unique room numbers and buildings for dropdowns
        $uniqueRooms = \App\CtrRoom::where('is_active', 0)->distinct()->pluck('room')->toArray();
        $uniqueBuildings = \App\CtrRoom::where('is_active', 0)->distinct()->pluck('building')->toArray();
        $uniqueColleges = \App\CtrRoom::where('is_active', 0)->whereNotNull('college_code')->distinct()->pluck('college_code')->toArray();

        return view('admin.course_offering.room_management_archive', compact('rooms', 'colleges', 'uniqueRooms', 'uniqueBuildings', 'uniqueColleges'));
    }

    public function new_room(Request $request)
    {
        // Validate input
        $request->validate([
            'room' => 'required|string|max:20',
            'building' => 'required|string|max:50',
            'college_code' => 'nullable|string|exists:colleges,college_code',
            'description' => 'nullable|string|max:255',
        ]);

        // Check if room already exists
        $check_exists = \App\CtrRoom::where('room', $request->room)
            ->where('building', $request->building)
            ->first();

        if ($check_exists) {
            Session::flash('error', "Room '{$request->room}' in building '{$request->building}' already exists!");
            return redirect(url('/admin/room_management'));
        }

        // Create new room
        $new_room = new \App\CtrRoom;
        $new_room->room = $request->room;
        $new_room->building = $request->building;
        $new_room->college_code = $request->college_code;
        $new_room->description = $request->description ?? '';
        $new_room->is_active = 1;
        $new_room->save();

        Session::flash('success', "Room '{$request->room}' in building '{$request->building}' successfully created!");
        return redirect(url('/admin/room_management'));
    }

    public function archive_room($room_id)
    {
        $archive = \App\CtrRoom::find($room_id);
        if (!$archive) {
            Session::flash('error', 'Room not found');
            return redirect(url('/admin/room_management'));
        }

        switch ($archive->is_active) {
            case 1: // Currently active, move to archive
                $archive->is_active = 0;
                $archive->update();
                Session::flash('error', "{$archive->room} has been moved to the archive section.");
                break;

            case 0: // Currently archived, restore
                $archive->is_active = 1;
                $archive->update();
                Session::flash('success', "{$archive->room} has been restored.");
                break;

            default:
                Session::flash('error', 'Invalid room status');
        }

        return redirect(url('/admin/room_management'));
    }

    public function update_room(Request $request)
    {
        // Validate input
        $request->validate([
            'room_id' => 'required|integer|exists:ctr_rooms,id',
            'room' => 'required|string|max:20',
            'building' => 'required|string|max:50',
            'college_code' => 'nullable|string|exists:colleges,college_code',
            'description' => 'nullable|string|max:255',
        ]);

        // Find the room
        $room = \App\CtrRoom::find($request->room_id);
        if (!$room) {
            Session::flash('error', 'Room not found');
            return redirect(url('/admin/room_management'));
        }

        // Check if another room with the same details already exists
        $check_exists = \App\CtrRoom::where('room', $request->room)
            ->where('building', $request->building)
            ->where('id', '!=', $request->room_id)
            ->first();

        if ($check_exists) {
            Session::flash('error', "Room '{$request->room}' in building '{$request->building}' already exists!");
            return redirect(url('/admin/room_management'));
        }

        // Update the room
        $room->room = $request->room;
        $room->building = $request->building;
        $room->college_code = $request->college_code;
        $room->description = $request->description ?? '';
        $room->update();

        Session::flash('success', "Room '{$request->room}' in building '{$request->building}' successfully updated!");
        return redirect(url('/admin/room_management'));
    }

    /**
     * Redirect to Section Curriculum Management
     */
    public function section_curriculum_management()
    {
        return redirect('/admin/section_curriculum');
    }
}



