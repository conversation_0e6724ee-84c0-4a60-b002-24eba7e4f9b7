<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\College;
use App\academic_programs;
use App\Program;
use Illuminate\Support\Facades\DB;

class MigrateAcademicProgramsToPrograms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:academic-programs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate data from academic_programs table to programs table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Starting migration of academic_programs to programs table...');

        // Get all academic programs
        $academicPrograms = academic_programs::all();
        $this->info('Found ' . $academicPrograms->count() . ' academic programs to migrate.');

        $migratedCount = 0;
        $skippedCount = 0;

        foreach ($academicPrograms as $academicProgram) {
            // Skip if program_code is empty
            if (empty($academicProgram->program_code)) {
                $this->warn('Skipping academic program with empty program_code (ID: ' . $academicProgram->id . ')');
                $skippedCount++;
                continue;
            }

            // Find the college by college_code
            $college = College::where('college_code', $academicProgram->college_code)->first();

            if (!$college) {
                $this->warn('College not found for academic program: ' . $academicProgram->program_code . ' (College Code: ' . $academicProgram->college_code . ')');
                
                // Create unassigned college if it doesn't exist
                $college = College::firstOrCreate(
                    ['college_code' => 'UNASSIGNED'],
                    [
                        'college_name' => 'Unassigned College',
                        'is_active' => 1
                    ]
                );
                
                $this->info('Created/found unassigned college for program: ' . $academicProgram->program_code);
            }

            // Check if program already exists in programs table
            $existingProgram = Program::where('college_id', $college->id)
                ->where('program_code', $academicProgram->program_code)
                ->first();

            if (!$existingProgram) {
                // Create new program
                Program::create([
                    'college_id' => $college->id,
                    'program_code' => $academicProgram->program_code,
                    'program_name' => $academicProgram->program_name ?? $academicProgram->program_code
                ]);

                $this->info('Migrated academic program: ' . $academicProgram->program_code . ' to programs table.');
                $migratedCount++;
            } else {
                $this->info('Program already exists in programs table: ' . $academicProgram->program_code);
                $skippedCount++;
            }
        }

        $this->info('Migration completed. Migrated: ' . $migratedCount . ', Skipped: ' . $skippedCount);
    }
}
