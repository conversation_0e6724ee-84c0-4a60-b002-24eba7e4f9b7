<?php

use Illuminate\Database\Seeder;
use App\CtrSection;
use App\College;

class SampleSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if there are any sections
        if (CtrSection::count() > 0) {
            $this->command->info('Sections already exist. Skipping sample section creation.');
            return;
        }

        // Get all colleges
        $colleges = College::where('is_active', 1)->get();
        
        if ($colleges->isEmpty()) {
            $this->command->info('No active colleges found. Please run the college seeder first.');
            return;
        }

        // Sample levels
        $levels = ['1st Year', '2nd Year', '3rd Year', '4th Year'];
        
        // Sample section names
        $sectionNames = ['A', 'B', 'C', 'D', 'E'];
        
        // Create sample sections for each college and level
        foreach ($colleges as $college) {
            foreach ($levels as $level) {
                foreach ($sectionNames as $index => $sectionName) {
                    // Only create a few sections per level
                    if ($index > 2) {
                        continue;
                    }
                    
                    $fullSectionName = $level . ' - ' . $sectionName;
                    
                    $section = new CtrSection();
                    $section->section_name = $fullSectionName;
                    $section->level = $level;
                    $section->college_code = $college->college_code;
                    $section->is_active = 1;
                    $section->save();
                    
                    $this->command->info("Created section: {$fullSectionName} for college {$college->college_name}");
                }
            }
        }
        
        $this->command->info('Sample sections have been created.');
    }
}
