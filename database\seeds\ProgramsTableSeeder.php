<?php

use Illuminate\Database\Seeder;
use App\College;
use App\Program;
use App\academic_programs;

class ProgramsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Seeding programs table from academic_programs...');

        // Get all academic programs
        $academicPrograms = academic_programs::all();
        $this->command->info('Found ' . $academicPrograms->count() . ' academic programs to migrate.');

        $migratedCount = 0;
        $skippedCount = 0;

        foreach ($academicPrograms as $academicProgram) {
            // Skip if program_code is empty
            if (empty($academicProgram->program_code)) {
                $this->command->warn('Skipping academic program with empty program_code (ID: ' . $academicProgram->id . ')');
                $skippedCount++;
                continue;
            }

            // Find the college by college_code
            $college = College::where('college_code', $academicProgram->college_code)->first();

            if (!$college) {
                $this->command->warn('College not found for academic program: ' . $academicProgram->program_code . ' (College Code: ' . $academicProgram->college_code . ')');
                
                // Create unassigned college if it doesn't exist
                $college = College::firstOrCreate(
                    ['college_code' => 'UNASSIGNED'],
                    [
                        'college_name' => 'Unassigned College',
                        'is_active' => 1
                    ]
                );
                
                $this->command->info('Created/found unassigned college for program: ' . $academicProgram->program_code);
            }

            // Check if program already exists in programs table
            $existingProgram = Program::where('college_id', $college->id)
                ->where('program_code', $academicProgram->program_code)
                ->first();

            if (!$existingProgram) {
                // Create new program
                Program::create([
                    'college_id' => $college->id,
                    'program_code' => $academicProgram->program_code,
                    'program_name' => $academicProgram->program_name ?? $academicProgram->program_code
                ]);

                $this->command->info('Migrated academic program: ' . $academicProgram->program_code . ' to programs table.');
                $migratedCount++;
            } else {
                $this->command->info('Program already exists in programs table: ' . $academicProgram->program_code);
                $skippedCount++;
            }
        }

        // Also migrate from colleges table's program_codes field
        $this->command->info('Seeding programs table from colleges program_codes...');
        $colleges = College::all();
        $this->command->info('Found ' . $colleges->count() . ' colleges to check for programs.');

        foreach ($colleges as $college) {
            if (isset($college->program_codes) && !empty($college->program_codes)) {
                $programCodes = explode(',', $college->program_codes);
                $programCodes = array_map('trim', $programCodes);

                foreach ($programCodes as $programCode) {
                    if (!empty($programCode)) {
                        // Check if this program already exists in the programs table
                        $existingProgram = Program::where('college_id', $college->id)
                            ->where('program_code', $programCode)
                            ->first();

                        if (!$existingProgram) {
                            // Try to find program name in program_names table
                            $programName = \App\ProgramName::getNameByCode($programCode) ?? $programCode;

                            Program::create([
                                'college_id' => $college->id,
                                'program_code' => $programCode,
                                'program_name' => $programName
                            ]);

                            $this->command->info('Migrated college program: ' . $programCode . ' to programs table.');
                            $migratedCount++;
                        } else {
                            $this->command->info('Program already exists in programs table: ' . $programCode);
                            $skippedCount++;
                        }
                    }
                }
            }
        }

        $this->command->info('Seeding completed. Migrated: ' . $migratedCount . ', Skipped: ' . $skippedCount);
    }
}
