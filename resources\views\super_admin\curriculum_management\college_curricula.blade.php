@extends('adminlte::page')
@section('title', 'Curricula - ' . $college->college_name)
@section('content_header')
    <h1><i class="fa fa-book"></i> Curricula - {{ $college->college_name }}</h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li><a href="{{ url('/superadmin/curriculum_management/colleges') }}"> Colleges</a></li>
        <li><a href="{{ url('/superadmin/curriculum_management/college', $college->college_code) }}"> {{ $college->college_name }}</a></li>
        <li class="active"><a href="{{ url('/superadmin/curriculum_management/college/'.$college->college_code.'/curricula') }}"> Curricula</a></li>
    </ol>
@stop
@section('main-content')
<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-default">
                <div class="box-header">
                    <h3 class="box-title">Curricula for {{ $college->college_name }} ({{ $college->college_code }})</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/superadmin/curriculum_management/college', $college->college_code) }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back to Programs</a>
                        <a href="{{ url('/admin/curriculum_management/add_curriculum') }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> New Curriculum</a>
                    </div>
                </div>
                <div class="box-body">
                    <div class='table-responsive'>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="40%">Curriculum Year</th>
                                    <th width="30%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($curriculumYears as $year)
                                <tr>
                                    <td>{{ $year }} - {{ is_numeric($year) ? (int)$year + 1 : $year }}</td>
                                    <td class="text-center">
                                        <a href="{{ url('/superadmin/curriculum_management/college/'.$college->college_code.'/curriculum/'.$year) }}" class="btn btn-flat btn-primary"><i class="fa fa-eye"></i> View Curriculum</a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
