<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\CtrRoom;
use App\CtrSection;
use App\instructors_infos;
use App\room_schedules;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    /**
     * Display the dashboard with all components
     */
    public function index()
    {
        // Basic statistics
        $totalRooms = CtrRoom::count();
        $totalPrograms = \App\academic_programs::count();
        $totalInstructors = instructors_infos::count();
        $totalSections = CtrSection::count();

        // Get colleges for filters
        $colleges = College::where('is_active', 1)->get();

        // Get rooms for filters
        $rooms = CtrRoom::where('is_active', 1)->get();

        // Get instructors for filters
        $instructors = User::where('accesslevel', 1)->get();

        // Get sections for filters
        $sections = CtrSection::where('is_active', 1)->get();

        // Room utilization data
        $roomUtilization = $this->getRoomUtilizationData();

        // Instructor load data
        $instructorLoad = $this->getInstructorLoadData();

        // Enrollment statistics
        $enrollmentStats = $this->getEnrollmentStatistics();

        // Popular time slots
        $popularTimeSlots = $this->getPopularTimeSlots();

        return view('super_admin.dashboard', compact(
            'totalRooms',
            'totalPrograms',
            'totalInstructors',
            'totalSections',
            'colleges',
            'rooms',
            'instructors',
            'sections',
            'roomUtilization',
            'instructorLoad',
            'enrollmentStats',
            'popularTimeSlots'
        ));
    }

    /**
     * Get room utilization data for analytics
     */
    private function getRoomUtilizationData()
    {
        $rooms = CtrRoom::where('is_active', 1)->get();
        $utilization = [];

        foreach ($rooms as $room) {
            $totalHours = 0;
            $schedules = room_schedules::where('room', $room->room)
                ->where('is_active', 1)
                ->get();

            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);
                $totalHours += $end->diffInHours($start);
            }

            // Assuming 12 hours per day, 6 days per week = 72 hours max
            $utilizationRate = ($totalHours > 0) ? ($totalHours / 72) * 100 : 0;

            $utilization[] = [
                'room' => $room->room,
                'building' => $room->building,
                'total_hours' => $totalHours,
                'utilization_rate' => round($utilizationRate, 2)
            ];
        }

        // Sort by utilization rate (descending)
        usort($utilization, function ($a, $b) {
            return $b['utilization_rate'] <=> $a['utilization_rate'];
        });

        return array_slice($utilization, 0, 10); // Return top 10
    }

    /**
     * Get instructor load data for analytics
     */
    private function getInstructorLoadData()
    {
        $instructors = User::where('accesslevel', 1)->get();
        $loads = [];

        foreach ($instructors as $instructor) {
            $totalHours = 0;
            $schedules = room_schedules::where('instructor', $instructor->id)
                ->where('is_active', 1)
                ->get();

            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);
                $totalHours += $end->diffInHours($start);
            }

            $loads[] = [
                'instructor_id' => $instructor->id,
                'instructor_name' => $instructor->name . ' ' . $instructor->lastname,
                'total_hours' => $totalHours
            ];
        }

        // Sort by total hours (descending)
        usort($loads, function ($a, $b) {
            return $b['total_hours'] <=> $a['total_hours'];
        });

        return array_slice($loads, 0, 10); // Return top 10
    }

    /**
     * Get enrollment statistics
     */
    private function getEnrollmentStatistics()
    {
        $sections = CtrSection::where('is_active', 1)->get();
        $stats = [];

        foreach ($sections as $section) {
            // Get enrollment count (this is a placeholder - adjust based on your actual data structure)
            $enrollmentCount = rand(15, 40); // Replace with actual enrollment count
            $capacity = 40; // Replace with actual capacity

            $stats[] = [
                'section' => $section->section_name,
                'enrollment' => $enrollmentCount,
                'capacity' => $capacity,
                'percentage' => round(($enrollmentCount / $capacity) * 100, 2)
            ];
        }

        // Sort by enrollment percentage (descending)
        usort($stats, function ($a, $b) {
            return $b['percentage'] <=> $a['percentage'];
        });

        return array_slice($stats, 0, 10); // Return top 10
    }

    /**
     * Get popular time slots
     */
    private function getPopularTimeSlots()
    {
        $timeSlots = [
            '07:00:00-10:00:00' => 0,
            '10:00:00-13:00:00' => 0,
            '13:00:00-16:00:00' => 0,
            '16:00:00-19:00:00' => 0,
            '19:00:00-22:00:00' => 0
        ];

        $schedules = room_schedules::where('is_active', 1)->get();

        foreach ($schedules as $schedule) {
            $start = Carbon::parse($schedule->time_starts);

            if ($start->between(Carbon::parse('07:00:00'), Carbon::parse('10:00:00'))) {
                $timeSlots['07:00:00-10:00:00']++;
            } elseif ($start->between(Carbon::parse('10:00:00'), Carbon::parse('13:00:00'))) {
                $timeSlots['10:00:00-13:00:00']++;
            } elseif ($start->between(Carbon::parse('13:00:00'), Carbon::parse('16:00:00'))) {
                $timeSlots['13:00:00-16:00:00']++;
            } elseif ($start->between(Carbon::parse('16:00:00'), Carbon::parse('19:00:00'))) {
                $timeSlots['16:00:00-19:00:00']++;
            } elseif ($start->between(Carbon::parse('19:00:00'), Carbon::parse('22:00:00'))) {
                $timeSlots['19:00:00-22:00:00']++;
            }
        }

        $result = [];
        foreach ($timeSlots as $slot => $count) {
            $result[] = [
                'time_slot' => $slot,
                'count' => $count
            ];
        }

        // Sort by count (descending)
        usort($result, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });

        return $result;
    }
}
