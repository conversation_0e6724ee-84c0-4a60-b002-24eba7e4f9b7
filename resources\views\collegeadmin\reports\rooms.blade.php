<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-file-text"></i>
        Rooms Occupied
        <small>{{ Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Rooms Occupied</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Room Schedules</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-success btn-sm" id="print-report">
                            <i class="fa fa-print"></i> Print Report
                        </button>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="filter-building">Filter by Building</label>
                                <select class="form-control" id="filter-building">
                                    <option value="">All Buildings</option>
                                    @php
                                        $buildings = $rooms->pluck('building')->unique();
                                    @endphp
                                    @foreach($buildings as $building)
                                        <option value="{{ $building }}">{{ $building }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    @if(count($rooms) > 0)
                        <div id="rooms-report">
                            @foreach($rooms as $room)
                                <div class="room-section" data-building="{{ $room->building }}">
                                    <h4>{{ $room->room }} ({{ $room->building }})</h4>
                                    @if(count($room->schedules) > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Course</th>
                                                        <th>Schedule</th>
                                                        <th>Instructor</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($room->schedules as $schedule)
                                                        <tr>
                                                            <td>{{ $schedule->course_code }} - {{ $schedule->course_name }}</td>
                                                            <td>
                                                                {{ $schedule->sched_day }} 
                                                                {{ date('h:i A', strtotime($schedule->sched_from)) }} - 
                                                                {{ date('h:i A', strtotime($schedule->sched_to)) }}
                                                            </td>
                                                            <td>{{ $schedule->instructor_name }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="alert alert-info">
                                            No schedules found for this room.
                                        </div>
                                    @endif
                                    <hr>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info">
                            No rooms found for your college.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
$(function() {
    // Handle filter by building
    $('#filter-building').change(function() {
        var building = $(this).val();
        
        if (building) {
            $('.room-section').hide();
            $('.room-section[data-building="' + building + '"]').show();
        } else {
            $('.room-section').show();
        }
    });
    
    // Handle print report button click
    $('#print-report').click(function() {
        window.print();
    });
});
</script>
@endsection
