<?php
use Illuminate\Support\Facades\Auth;

$layout = "layouts.app";

if(Auth::check()){
    if(Auth::user()->is_first_login == 1){
        $layout = 'layouts.first_login';
    }else{
        if(Auth::user()->accesslevel == 100){
            $layout = 'layouts.superadmin';
        }elseif(Auth::user()->accesslevel == 50){
            $layout = 'layouts.collegeadmin';
        }elseif(Auth::user()->accesslevel == 1){
            $layout = 'layouts.instructor';
        }elseif(Auth::user()->accesslevel == 0){
            $layout = 'layouts.admin';
        }
    }
}
?>
@extends($layout)

@section('htmlheader_title')
    Unauthorized Access
@endsection

@section('main-content')
    <div class="error-page">
        <h2 class="headline text-yellow"> 401</h2>
        <div class="error-content" style="margin-top:200px;">
            <h3><i class="fa fa-warning text-yellow"></i> Oops! Unauthorized Access.</h3>
            <p>
                You are not authorized to view this page. Meanwhile, you may
                <a href='{{ url('/home') }}'>return to dashboard</a>.
            </p>
        </div><!-- /.error-content -->
    </div><!-- /.error-page -->
@endsection
