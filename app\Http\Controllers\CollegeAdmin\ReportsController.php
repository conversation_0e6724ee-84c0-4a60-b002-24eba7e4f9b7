<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\User;
use App\CtrRoom;
use App\room_schedules;
use App\offerings_infos_table;
use App\curriculum;

class ReportsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Display faculty reports
     */
    public function facultyReports()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get faculty members for this college
        $faculty = User::where('accesslevel', 1)
            ->where('college_code', $collegeCode)
            ->get();

        return view('collegeadmin.reports.faculty', compact('faculty'));
    }

    /**
     * Display rooms reports
     */
    public function roomsReports()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get rooms for this college
        $rooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->get();

        // Get schedules for each room
        foreach ($rooms as $room) {
            $room->schedules = room_schedules::where('room', $room->room)
                ->where('is_active', 1)
                ->where('college_code', $collegeCode) // Filter by college code
                ->get();

            // Get course details for each schedule
            foreach ($room->schedules as $schedule) {
                // First try to get the offering
                $offering = offerings_infos_table::where('id', $schedule->offering_id)->first();

                if ($offering) {
                    // Get curriculum details using the relationship
                    $curriculum = curriculum::where('id', $offering->curriculum_id)
                        ->where('college_code', $collegeCode) // Filter by college code
                        ->first();

                    if ($curriculum) {
                        // Try to get course info from curriculum first
                        $schedule->course_code = $curriculum->course_code ?: 'Unknown';
                        $schedule->course_name = $curriculum->course_name ?: 'Unknown';

                        // If course_name is empty or generic, try to get from subject relationship
                        if (
                            empty($curriculum->course_name) || $curriculum->course_name == 'Unknown' ||
                            strpos($curriculum->course_name, 'Course:') === 0
                        ) {
                            $subject = $curriculum->subject;
                            if ($subject) {
                                $schedule->course_code = $subject->subject_code;
                                $schedule->course_name = $subject->subject_name;
                            }
                        }
                    } else {
                        $schedule->course_code = 'Unknown';
                        $schedule->course_name = 'Unknown';
                    }
                } else {
                    $schedule->course_code = 'Unknown';
                    $schedule->course_name = 'Unknown';
                }

                // Get instructor details
                if ($schedule->instructor) {
                    $instructor = User::find($schedule->instructor);
                    $schedule->instructor_name = $instructor ? "{$instructor->name} {$instructor->lastname}" : 'No Instructor';
                } else {
                    $schedule->instructor_name = 'No Instructor';
                }
            }
        }

        return view('collegeadmin.reports.rooms', compact('rooms'));
    }
}
