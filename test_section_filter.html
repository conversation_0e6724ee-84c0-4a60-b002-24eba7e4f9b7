<!DOCTYPE html>
<html>
<head>
    <title>Test Section Filter</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>College Admin Section Filter Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Check if AJAX endpoint is accessible</h2>
        <button onclick="testAjaxEndpoint()">Test AJAX Endpoint</button>
        <div id="ajax-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Test filter with program code</h2>
        <input type="text" id="test-program" placeholder="Enter program code" value="BSIT">
        <button onclick="testProgramFilter()">Test Program Filter</button>
        <div id="program-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Test filter with level</h2>
        <input type="text" id="test-level" placeholder="Enter level" value="1st Year">
        <button onclick="testLevelFilter()">Test Level Filter</button>
        <div id="level-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Test combined filters</h2>
        <input type="text" id="test-combined-program" placeholder="Program" value="BSIT">
        <input type="text" id="test-combined-level" placeholder="Level" value="1st Year">
        <button onclick="testCombinedFilter()">Test Combined Filter</button>
        <div id="combined-result" class="result"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000';
        
        function testAjaxEndpoint() {
            $('#ajax-result').html('Testing AJAX endpoint...');
            
            $.ajax({
                type: "GET",
                url: baseUrl + "/ajax/collegeadmin/section_management/search",
                data: {},
                success: function(data) {
                    $('#ajax-result').html('<div class="success">✓ AJAX endpoint is accessible</div><pre>' + data + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#ajax-result').html('<div class="error">✗ AJAX endpoint error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testProgramFilter() {
            const program = $('#test-program').val();
            $('#program-result').html('Testing program filter: ' + program);
            
            $.ajax({
                type: "GET",
                url: baseUrl + "/ajax/collegeadmin/section_management/search",
                data: {
                    program_code: program,
                    level: '',
                    section_name: ''
                },
                success: function(data) {
                    $('#program-result').html('<div class="success">✓ Program filter works</div><pre>' + data + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#program-result').html('<div class="error">✗ Program filter error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testLevelFilter() {
            const level = $('#test-level').val();
            $('#level-result').html('Testing level filter: ' + level);
            
            $.ajax({
                type: "GET",
                url: baseUrl + "/ajax/collegeadmin/section_management/search",
                data: {
                    program_code: '',
                    level: level,
                    section_name: ''
                },
                success: function(data) {
                    $('#level-result').html('<div class="success">✓ Level filter works</div><pre>' + data + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#level-result').html('<div class="error">✗ Level filter error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testCombinedFilter() {
            const program = $('#test-combined-program').val();
            const level = $('#test-combined-level').val();
            $('#combined-result').html('Testing combined filter: ' + program + ' + ' + level);
            
            $.ajax({
                type: "GET",
                url: baseUrl + "/ajax/collegeadmin/section_management/search",
                data: {
                    program_code: program,
                    level: level,
                    section_name: ''
                },
                success: function(data) {
                    $('#combined-result').html('<div class="success">✓ Combined filter works</div><pre>' + data + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#combined-result').html('<div class="error">✗ Combined filter error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }
    </script>
</body>
</html>
