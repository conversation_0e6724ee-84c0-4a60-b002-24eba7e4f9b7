<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCollegeCodeToCtrSectionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the column already exists
        if (!Schema::hasColumn('ctr_sections', 'college_code')) {
            Schema::table('ctr_sections', function (Blueprint $table) {
                $table->string('college_code', 20)->nullable()->after('section_name');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ctr_sections', function (Blueprint $table) {
            $table->dropColumn('college_code');
        });
    }
}
