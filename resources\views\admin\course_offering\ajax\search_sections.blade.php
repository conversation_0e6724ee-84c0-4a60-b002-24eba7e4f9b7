@if(!$sections->isEmpty())
    @foreach($sections as $section)
    <tr>
        <td>{{$section->program_code}}</td>
        <td>{{$section->level}}</td>
        <td>{{$section->section_name}}</td>
        <td>
            @if($section->is_active == 1)
            <label class='label label-success'>Active</label>
            @else
            <label class='label label-danger'>Inactive</label>
            @endif
        </td>
        <td>
            <button data-toggle="modal" data-target="#myModal" onclick="editsection('{{$section->id}}')" title="Edit Record" class="btn btn-flat btn-primary"><i class="fa fa-pencil"></i></button>
            <a href="{{url('/admin/section_management/archive',[$section->id])}}" class="btn btn-flat btn-danger" title="Change to Inactive Status?" onclick="confirm('Do you wish to archive the Record?')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    @endforeach
@else
    <tr>
        <td colspan="5" class="text-center">No sections found matching your search criteria.</td>
    </tr>
@endif
