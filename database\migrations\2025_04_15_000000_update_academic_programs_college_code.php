<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateAcademicProgramsCollegeCode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, ensure all academic programs have a college_code
        $this->populateMissingCollegeCodes();

        // Drop the foreign key constraint first
        Schema::table('academic_programs', function (Blueprint $table) {
            // Check if the foreign key exists before trying to drop it
            if (Schema::hasColumn('academic_programs', 'college_code')) {
                // Get all foreign keys
                $foreignKeys = DB::select(
                    "SELECT CONSTRAINT_NAME
                     FROM information_schema.TABLE_CONSTRAINTS
                     WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
                     AND TABLE_NAME = 'academic_programs'
                     AND CONSTRAINT_NAME = 'academic_programs_college_code_foreign'"
                );

                if (!empty($foreignKeys)) {
                    $table->dropForeign('academic_programs_college_code_foreign');
                }
            }
        });

        // Then, make the college_code field required
        DB::statement('ALTER TABLE academic_programs MODIFY college_code VARCHAR(20) NOT NULL');

        // Add the foreign key
        Schema::table('academic_programs', function (Blueprint $table) {
            $table->foreign('college_code')->references('college_code')->on('colleges')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('academic_programs', function (Blueprint $table) {
            // Drop the foreign key first
            $table->dropForeign(['college_code']);
        });

        // Make the column nullable using raw SQL
        DB::statement('ALTER TABLE academic_programs MODIFY college_code VARCHAR(20) NULL');

        // Re-add the foreign key
        Schema::table('academic_programs', function (Blueprint $table) {
            $table->foreign('college_code')->references('college_code')->on('colleges')->onDelete('set null');
        });
    }

    /**
     * Populate missing college_code values in academic_programs table
     */
    private function populateMissingCollegeCodes()
    {
        // Get all programs without a college_code
        $programs = DB::table('academic_programs')->whereNull('college_code')->get();

        foreach ($programs as $program) {
            // Try to find a college that has this program in its description
            $colleges = DB::table('colleges')->get();

            foreach ($colleges as $college) {
                $courses = explode(',', $college->description);
                $courses = array_map('trim', $courses);

                if (in_array($program->program_code, $courses)) {
                    // Update the program with the college_code
                    DB::table('academic_programs')
                        ->where('id', $program->id)
                        ->update(['college_code' => $college->college_code]);

                    break;
                }
            }

            // If no college found, create a default college for unassigned programs
            if (DB::table('academic_programs')->where('id', $program->id)->whereNull('college_code')->exists()) {
                // Check if default college exists
                $defaultCollege = DB::table('colleges')->where('college_code', 'UNASSIGNED')->first();

                if (!$defaultCollege) {
                    // Create default college
                    $defaultCollegeId = DB::table('colleges')->insertGetId([
                        'college_code' => 'UNASSIGNED',
                        'college_name' => 'Unassigned College',
                        'description' => $program->program_code,
                        'is_active' => 1,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                } else {
                    // Update description to include this program
                    $description = $defaultCollege->description;
                    if ($description) {
                        $description .= ', ' . $program->program_code;
                    } else {
                        $description = $program->program_code;
                    }

                    DB::table('colleges')
                        ->where('id', $defaultCollege->id)
                        ->update(['description' => $description]);
                }

                // Assign the program to the default college
                DB::table('academic_programs')
                    ->where('id', $program->id)
                    ->update(['college_code' => 'UNASSIGNED']);
            }
        }
    }
}
