@if(count($offerings) > 0)
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Course</th>
                    <th>Schedule (Drag the Schedule to the Calendar)</th>
                </tr>
            </thead>
            <tbody>
                @foreach($offerings as $offering)
                    @php
                        $schedule = App\room_schedules::where('offering_id', $offering->id)
                            ->where('is_active', 1)
                            ->first();

                        // Define colors for the callout boxes
                        $colors = ['info', 'warning', 'success', 'danger'];
                        $colorIndex = $loop->index % count($colors);
                        $color = $colors[$colorIndex];
                    @endphp

                    @if($schedule)
                        <tr>
                            <td>
                                <div align="center">
                                    <strong>{{ $offering->course_code }}</strong><br>
                                    {{ $offering->course_name }}<br>
                                    Section: {{ $offering->section_name ?? 'N/A' }}<br>
                                    @php
                                        $curriculum = App\curriculum::find($offering->curriculum_id);
                                        $semester = $curriculum ? $curriculum->period : 'Not specified';
                                    @endphp
                                    Semester: {{ $semester }}
                                </div>
                            </td>
                            <td>
                                <div class="draggable-item">
                                    <div data-offering="{{ $offering->id }}" class="callout callout-{{ $color }} draggable-schedule" data-event='{"offering": "{{ $offering->id }}", "title": "{{ $offering->course_code }} ({{ $schedule->room }})", "duration": "{{ date('H:i', strtotime($schedule->sched_to) - strtotime($schedule->sched_from)) }}"}'>
                                        <div align="center">
                                            <strong>{{ $schedule->room }}</strong><br>
                                            {{ $schedule->sched_day }}
                                            {{ date('h:i A', strtotime($schedule->sched_from)) }} -
                                            {{ date('h:i A', strtotime($schedule->sched_to)) }}
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm btn-block add-to-load" data-offering="{{ $offering->id }}">
                                    <i class="fa fa-plus"></i> Add to Load
                                </button>
                            </td>
                        </tr>
                    @endif
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info">
        No matching courses found.
    </div>
@endif
