<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateSubjectNames extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 1. Create a mapping of subject codes to descriptive names
        $subjectNames = [
            // Computer Science/IT Subjects
            'CSP107' => 'Introduction to Programming',
            'CSP108' => 'Data Structures and Algorithms',
            'CSP109' => 'Database Management Systems',
            'CSA101' => 'Computer Architecture',
            'CSA102' => 'Operating Systems',
            'CSP110' => 'Web Development',
            'CSP111' => 'Software Engineering',
            'CSA103' => 'Computer Networks',
            'CSA104' => 'Information Security',
            'CSA105' => 'Mobile Application Development',
            'CSE3' => 'Computer Ethics',
            'CSP112' => 'Advanced Programming',
            'CSP113' => 'Artificial Intelligence',

            // Add more mappings as needed for other subjects
        ];

        // 2. Update the subjects table with the new names
        foreach ($subjectNames as $code => $name) {
            // Check if the subject exists
            $subject = DB::table('subjects')
                ->where('subject_code', $code)
                ->first();

            if ($subject) {
                // Update the subject name
                DB::table('subjects')
                    ->where('subject_code', $code)
                    ->update(['subject_name' => $name]);

                // Log the update
                DB::table('logs')->insert([
                    'action' => 'Updated subject name',
                    'description' => "Updated subject {$code} name to '{$name}'",
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            } else {
                // Create the subject if it doesn't exist
                DB::table('subjects')->insert([
                    'subject_code' => $code,
                    'subject_name' => $name,
                    'lec' => 3, // Default values
                    'lab' => 0,
                    'units' => 3,
                    'is_complab' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                // Log the creation
                DB::table('logs')->insert([
                    'action' => 'Created subject',
                    'description' => "Created subject {$code} with name '{$name}'",
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        // 3. Update the curricula table to reference the subjects
        foreach ($subjectNames as $code => $name) {
            // Get the subject ID
            $subject = DB::table('subjects')
                ->where('subject_code', $code)
                ->first();

            if ($subject) {
                // Update curricula records that have this course code
                if (Schema::hasColumn('curricula', 'course_name')) {
                    DB::table('curricula')
                        ->where('course_code', $code)
                        ->update([
                            'subject_id' => $subject->id,
                            'course_name' => $name // Also update the course_name directly
                        ]);
                } else {
                    DB::table('curricula')
                        ->where('course_code', $code)
                        ->update([
                            'subject_id' => $subject->id
                        ]);
                }

                // Also update records where control_code matches but course_code might be different
                if (Schema::hasColumn('curricula', 'course_name')) {
                    DB::table('curricula')
                        ->where('control_code', $code)
                        ->whereNull('subject_id')
                        ->update([
                            'subject_id' => $subject->id,
                            'course_name' => $name
                        ]);
                } else {
                    DB::table('curricula')
                        ->where('control_code', $code)
                        ->whereNull('subject_id')
                        ->update([
                            'subject_id' => $subject->id
                        ]);
                }
            }
        }

        // 4. Fix any remaining "Course: XXX" names in the curricula table
        $coursePrefixRecords = DB::table('curricula')
            ->where('course_name', 'like', 'Course: %')
            ->get();

        foreach ($coursePrefixRecords as $record) {
            // Extract the course code from the name
            $codeMatch = [];
            if (preg_match('/Course: (.+)/', $record->course_name, $codeMatch)) {
                $extractedCode = $codeMatch[1];

                // Find a subject with this code
                $subject = DB::table('subjects')
                    ->where('subject_code', $extractedCode)
                    ->first();

                if ($subject) {
                    // Update the curriculum record
                    if (Schema::hasColumn('curricula', 'course_name')) {
                        DB::table('curricula')
                            ->where('id', $record->id)
                            ->update([
                                'subject_id' => $subject->id,
                                'course_name' => $subject->subject_name
                            ]);
                    } else {
                        DB::table('curricula')
                            ->where('id', $record->id)
                            ->update([
                                'subject_id' => $subject->id
                            ]);
                    }
                } else {
                    // Create a more descriptive name based on the code
                    $descriptiveName = 'Subject: ' . $extractedCode;

                    // Update the curriculum record with a better name
                    if (Schema::hasColumn('curricula', 'course_name')) {
                        DB::table('curricula')
                            ->where('id', $record->id)
                            ->update([
                                'course_name' => $descriptiveName
                            ]);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration cannot be reversed as it updates data
        // We could restore from a backup if needed
    }
}
