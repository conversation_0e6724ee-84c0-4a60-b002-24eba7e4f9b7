<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\CtrSection;
use App\College;
use App\curriculum;
use App\offerings_infos_table;
use App\Services\ProgramService;
use Auth;
use Session;
use Illuminate\Support\Facades\Log;

class SectionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Show the section management page for college admin
     */
    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get sections for this college only
        $sections = CtrSection::where('is_active', 1)
            ->where('college_code', $collegeCode)
            ->get();

        // Get college information
        $college = College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get programs from this college using the ProgramService
        $programService = new ProgramService();
        $programs = $programService->getProgramsByCollege($collegeCode);

        // Get unique program codes and levels for dropdowns (filtered by college)
        $uniquePrograms = CtrSection::where('is_active', 1)
            ->where('college_code', $collegeCode)
            ->distinct()
            ->pluck('program_code')
            ->toArray();

        $uniqueLevels = CtrSection::where('is_active', 1)
            ->where('college_code', $collegeCode)
            ->distinct()
            ->pluck('level')
            ->toArray();

        return view('collegeadmin.section_management.index', compact(
            'sections',
            'college',
            'programs',
            'uniquePrograms',
            'uniqueLevels'
        ));
    }

    /**
     * Show archived sections for college admin
     */
    public function archive()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get archived sections for this college only
        $sections = CtrSection::where('is_active', 0)
            ->where('college_code', $collegeCode)
            ->get();

        // Get college information
        $college = College::where('college_code', $collegeCode)->first();

        return view('collegeadmin.section_management.archive', compact('sections', 'college'));
    }

    /**
     * Create a new section with curriculum subjects for college admin
     */
    public function createSectionWithCurriculum(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Validate the request
        $this->validate($request, [
            'program_code' => 'required|string|max:20',
            'level' => 'required|string|max:20',
            'section_name' => 'required|string|max:50',
            'add_curriculum' => 'required|in:yes,no'
        ]);

        // Verify that the program belongs to this college
        $programService = new ProgramService();
        $programCollegeCode = $programService->getCollegeCodeForProgram($request->program_code);

        if ($programCollegeCode !== $collegeCode) {
            Session::flash('error', 'You can only create sections for programs in your college!');
            return redirect(url('/collegeadmin/section_management'));
        }

        // Check if section already exists
        $check_exists = CtrSection::where('program_code', $request->program_code)
            ->where('level', $request->level)
            ->where('section_name', $request->section_name)
            ->where('college_code', $collegeCode)
            ->first();

        if ($check_exists) {
            Session::flash('error', "Section '{$request->section_name}' already exists for this program and level!");
            return redirect(url('/collegeadmin/section_management'));
        }

        // Create new section
        $new_section = new CtrSection;
        $new_section->program_code = $request->program_code;
        $new_section->level = $request->level;
        $new_section->section_name = $request->section_name;
        $new_section->college_code = $collegeCode;
        $new_section->is_active = 1;
        $new_section->save();

        // Add curriculum subjects if requested
        if ($request->add_curriculum === 'yes') {
            $addedCount = $new_section->addAllCurriculaToOfferings();

            if ($addedCount > 0) {
                Session::flash('success', "Section '{$request->section_name}' created successfully with {$addedCount} curriculum subjects!");
            } else {
                Session::flash('success', "Section '{$request->section_name}' created successfully! No curriculum subjects found to add.");
            }
        } else {
            Session::flash('success', "Section '{$request->section_name}' created successfully!");
        }

        Log::info("College Admin " . Auth::user()->name . " created section {$request->section_name} for college {$collegeCode}");

        return redirect(url('/collegeadmin/section_management'));
    }

    /**
     * View a specific section with its curriculum for college admin
     */
    public function viewSection($section_id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        $section = CtrSection::where('id', $section_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$section) {
            Session::flash('error', 'Section not found or you do not have permission to view it');
            return redirect(url('/collegeadmin/section_management'));
        }

        // Get offerings for this section
        $offerings = offerings_infos_table::where('section_name', $section->section_name)
            ->with('curriculum')
            ->get();

        // Get available curricula that can be added to this section
        $availableCurricula = $section->getAvailableCurricula();

        return view('collegeadmin.section_management.view', compact(
            'section',
            'offerings',
            'availableCurricula'
        ));
    }

    /**
     * Archive a section for college admin
     */
    public function archiveSection($section_id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        $section = CtrSection::where('id', $section_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$section) {
            Session::flash('error', 'Section not found or you do not have permission to archive it');
            return redirect(url('/collegeadmin/section_management'));
        }

        $section->is_active = 0;
        $section->save();

        Session::flash('success', "Section '{$section->section_name}' has been archived successfully!");
        Log::info("College Admin " . Auth::user()->name . " archived section {$section->section_name} for college {$collegeCode}");

        return redirect(url('/collegeadmin/section_management'));
    }

    /**
     * Update an existing section for college admin
     */
    public function updateSection(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Validate input
        $request->validate([
            'section_id' => 'required|integer|exists:ctr_sections,id',
            'program_code' => 'required|string|max:20',
            'level' => 'required|string|max:20',
            'section_name' => 'required|string|max:50',
        ]);

        // Find the section and verify it belongs to this college
        $section = CtrSection::where('id', $request->section_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$section) {
            Session::flash('error', 'Section not found or you do not have permission to edit it');
            return redirect(url('/collegeadmin/section_management'));
        }

        // Verify that the new program belongs to this college
        $programService = new ProgramService();
        $programCollegeCode = $programService->getCollegeCodeForProgram($request->program_code);

        if ($programCollegeCode !== $collegeCode) {
            Session::flash('error', 'You can only assign programs from your college!');
            return redirect(url('/collegeadmin/section_management'));
        }

        // Check if another section with the same details already exists
        $check_exists = CtrSection::where('program_code', $request->program_code)
            ->where('level', $request->level)
            ->where('section_name', $request->section_name)
            ->where('college_code', $collegeCode)
            ->where('id', '!=', $request->section_id)
            ->first();

        if ($check_exists) {
            Session::flash('error', "Another section with the same details already exists!");
            return redirect(url('/collegeadmin/section_management'));
        }

        // Update the section
        $old_section_name = $section->section_name;
        $section->program_code = $request->program_code;
        $section->level = $request->level;
        $section->section_name = $request->section_name;
        $section->save();

        // Update related offerings if section name changed
        if ($old_section_name !== $request->section_name) {
            offerings_infos_table::where('section_name', $old_section_name)
                ->update(['section_name' => $request->section_name]);
        }

        Session::flash('success', "Section updated successfully!");
        Log::info("College Admin " . Auth::user()->name . " updated section {$request->section_name} for college {$collegeCode}");

        return redirect(url('/collegeadmin/section_management'));
    }

    /**
     * Add all available curriculum subjects to a section for college admin
     */
    public function addAllCurricula($section_id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        $section = CtrSection::where('id', $section_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$section) {
            Session::flash('error', 'Section not found or you do not have permission to modify it');
            return redirect(url('/collegeadmin/section_management'));
        }

        $addedCount = $section->addAllCurriculaToOfferings();

        if ($addedCount > 0) {
            Session::flash('success', "Successfully added {$addedCount} curriculum subjects to {$section->section_name}");
            Log::info("College Admin " . Auth::user()->name . " added {$addedCount} curriculum subjects to section {$section->section_name} for college {$collegeCode}");
        } else {
            Session::flash('info', "No new curriculum subjects to add to {$section->section_name}");
        }

        return redirect(url('/collegeadmin/section_management/view', [$section_id]));
    }

    /**
     * Add specific curriculum subjects to a section for college admin
     */
    public function addSelectedCurricula(Request $request, $section_id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        $section = CtrSection::where('id', $section_id)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$section) {
            Session::flash('error', 'Section not found or you do not have permission to modify it');
            return redirect(url('/collegeadmin/section_management'));
        }

        // Validate the request
        $this->validate($request, [
            'curriculum_ids' => 'required|array',
            'curriculum_ids.*' => 'required|integer|exists:curricula,id'
        ]);

        $addedCount = 0;
        foreach ($request->curriculum_ids as $curriculum_id) {
            $curriculum = curriculum::where('id', $curriculum_id)
                ->where('college_code', $collegeCode)
                ->first();

            if (!$curriculum) {
                continue; // Skip curricula that don't belong to this college
            }

            // Check if this curriculum is already added to the section
            $existing = offerings_infos_table::where('section_name', $section->section_name)
                ->where('curriculum_id', $curriculum_id)
                ->first();

            if (!$existing) {
                $offering = new offerings_infos_table;
                $offering->section_name = $section->section_name;
                $offering->curriculum_id = $curriculum_id;
                $offering->save();
                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            Session::flash('success', "Successfully added {$addedCount} curriculum subjects to {$section->section_name}");
            Log::info("College Admin " . Auth::user()->name . " added {$addedCount} selected curriculum subjects to section {$section->section_name} for college {$collegeCode}");
        } else {
            Session::flash('info', "No new curriculum subjects were added to {$section->section_name}");
        }

        return redirect(url('/collegeadmin/section_management/view', [$section_id]));
    }

    /**
     * Remove a curriculum offering from a section for college admin
     */
    public function removeOffering($offering_id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        $offering = offerings_infos_table::find($offering_id);

        if (!$offering) {
            Session::flash('error', 'Offering not found');
            return redirect()->back();
        }

        // Get the section to verify it belongs to this college
        $section = CtrSection::where('section_name', $offering->section_name)
            ->where('college_code', $collegeCode)
            ->first();

        if (!$section) {
            Session::flash('error', 'You do not have permission to modify this offering');
            return redirect(url('/collegeadmin/section_management'));
        }

        $curriculum_name = $offering->curriculum ? $offering->curriculum->course_code : 'Unknown';
        $offering->delete();

        Session::flash('success', "Successfully removed {$curriculum_name} from {$section->section_name}");
        Log::info("College Admin " . Auth::user()->name . " removed offering {$curriculum_name} from section {$section->section_name} for college {$collegeCode}");

        return redirect()->back();
    }
}
