<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-building"></i>
        Room Management
        <small>{{ $college->college_name }} ({{ $college->college_code }})</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Room Management</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-4">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Add New Room</h3>
                </div>
                <form action="{{ route('collegeadmin.room.store') }}" method="POST">
                    @csrf
                    <div class="box-body">
                        <div class="form-group">
                            <label for="room">Room Number</label>
                            <input type="text" class="form-control" id="room" name="room" required>
                        </div>
                        <div class="form-group">
                            <label for="building">Building</label>
                            <input type="text" class="form-control" id="building" name="building" required>
                        </div>
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="box-footer">
                        <button type="submit" class="btn btn-primary">Add Room</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Rooms</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.room.archived') }}" class="btn btn-default btn-sm">
                            <i class="fa fa-archive"></i> View Archived Rooms
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search-room" placeholder="Search rooms...">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" id="search-room-btn">Search</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="rooms-list">
                        @if(count($rooms) > 0)
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Room</th>
                                            <th>Building</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($rooms as $room)
                                            <tr>
                                                <td>{{ $room->room }}</td>
                                                <td>{{ $room->building }}</td>
                                                <td>{{ $room->description }}</td>
                                                <td>
                                                    <a href="{{ route('collegeadmin.room.edit', $room->id) }}" class="btn btn-primary btn-sm">
                                                        <i class="fa fa-pencil"></i> Edit
                                                    </a>
                                                    <a href="{{ route('collegeadmin.room.archive', $room->id) }}" class="btn btn-warning btn-sm" onclick="return confirm('Are you sure you want to archive this room?')">
                                                        <i class="fa fa-archive"></i> Archive
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info">
                                No rooms found for your college.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
$(function() {
    // Handle search button click
    $('#search-room-btn').click(function() {
        var searchTerm = $('#search-room').val();
        
        $.ajax({
            url: "{{ url('/ajax/collegeadmin/room_management/search') }}",
            type: 'GET',
            data: { search: searchTerm },
            success: function(data) {
                $('#rooms-list').html(data);
            },
            error: function() {
                $('#rooms-list').html('<div class="alert alert-danger">Error searching for rooms.</div>');
            }
        });
    });
    
    // Handle Enter key in search box
    $('#search-room').keypress(function(e) {
        if (e.which == 13) {
            $('#search-room-btn').click();
            return false;
        }
    });
});
</script>
@endsection
