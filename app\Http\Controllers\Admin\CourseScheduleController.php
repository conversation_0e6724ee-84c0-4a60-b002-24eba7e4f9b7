<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Support\Facades\Session;
use DB;
use App\TimeBlock;
use App\College;
use App\curriculum;
use App\CtrRoom;
use App\CtrSection;
use App\offerings_infos_table;
use App\room_schedules;
use App\User;
use App\SchedulePriority;

class CourseScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('admin');
    }

    public function course_schedule()
    {
        $colleges = College::where('is_active', 1)->orderBy('college_code')->get();

        // Check if there are any sections
        $sectionCount = CtrSection::count();

        // Create sample sections if none exist
        if ($sectionCount == 0 && $colleges->isNotEmpty()) {
            $this->createSampleSections($colleges);
        }

        // Get all sections for initial display
        $sections = CtrSection::distinct()->get(['section_name', 'level', 'college_code', 'program_code']);

        // Get all levels
        $levels = CtrSection::distinct()->pluck('level')->toArray();

        // Get all program codes and names
        $programs = \App\ProgramName::all();

        // If no programs exist in the program_names table, try to get them from academic_programs
        if ($programs->isEmpty()) {
            $academicPrograms = \App\academic_programs::distinct()->get(['program_code', 'program_name']);
            foreach ($academicPrograms as $program) {
                if (!empty($program->program_code) && !empty($program->program_name)) {
                    \App\ProgramName::updateOrCreate(
                        ['program_code' => $program->program_code],
                        ['program_name' => $program->program_name]
                    );
                }
            }
            $programs = \App\ProgramName::all();
        }

        // If still no programs, extract them from sections
        if ($programs->isEmpty()) {
            $sectionPrograms = CtrSection::distinct()->whereNotNull('program_code')->pluck('program_code')->toArray();
            foreach ($sectionPrograms as $programCode) {
                \App\ProgramName::updateOrCreate(
                    ['program_code' => $programCode],
                    ['program_name' => $programCode]
                );
            }
            $programs = \App\ProgramName::all();
        }

        return view('admin.course_schedule.course_schedule', compact('colleges', 'sections', 'levels', 'programs'));
    }

    /**
     * Create sample sections for testing
     *
     * @param \Illuminate\Database\Eloquent\Collection $colleges
     */
    private function createSampleSections($colleges)
    {
        // Sample levels
        $levels = ['1st Year', '2nd Year', '3rd Year', '4th Year'];

        // Sample section names
        $sectionNames = ['A', 'B', 'C'];

        // Create sample sections for each college and level
        foreach ($colleges as $college) {
            foreach ($levels as $level) {
                foreach ($sectionNames as $sectionName) {
                    $fullSectionName = $level . ' - ' . $sectionName;

                    $section = new CtrSection();
                    $section->section_name = $fullSectionName;
                    $section->level = $level;
                    $section->college_code = $college->college_code;
                    $section->is_active = 1;
                    $section->save();
                }
            }
        }
    }

    function add_schedule($offering_id, $section_name)
    {
        $offering = \App\offerings_infos_table::find($offering_id);
        $curricula = \App\curriculum::find($offering->curriculum_id);
        $inactive = \App\room_schedules::where('is_active', 0)->get();
        $get_schedule = $this->getSchedule($offering_id);
        $is_complab = \App\curriculum::find($offering->curriculum_id)->is_complab;
        return view('admin.course_schedule.add_schedule', compact('offering', 'curricula', 'inactive', 'offering_id', 'get_schedule', 'section_name', 'is_complab'));

    }

    public static function getSchedule($offering_id)
    {
        $event_array = [];
        $schedules = room_schedules::where('offering_id', $offering_id)->get();
        if (!$schedules->isEmpty()) {
            foreach ($schedules as $sched) {
                try {
                    // Get the offering and curriculum using the model relationships
                    $offering = offerings_infos_table::find($offering_id);

                    if (!$offering) {
                        continue; // Skip if offering not found
                    }

                    $curriculum = curriculum::find($offering->curriculum_id);

                    if (!$curriculum) {
                        continue; // Skip if curriculum not found
                    }

                    // Use the accessor methods to get course_code and section_name
                    $courseCode = $curriculum->course_code; // This will use our accessor method
                    $sectionName = $offering->section_name;

                    // Determine day and color
                    switch ($sched->day) {
                        case 'M':
                            $day = 'Monday';
                            $color = 'LightSalmon';
                            break;
                        case 'T':
                            $day = 'Tuesday';
                            $color = 'lightblue';
                            break;
                        case 'W':
                            $day = 'Wednesday';
                            $color = 'LightSalmon';
                            break;
                        case 'Th':
                            $day = 'Thursday';
                            $color = 'lightblue';
                            break;
                        case 'F':
                            $day = 'Friday';
                            $color = 'LightSalmon';
                            break;
                        case 'Sa':
                            $day = 'Saturday';
                            $color = 'lightblue';
                            break;
                        case 'Su':
                            $day = 'Sunday';
                            $color = 'LightSalmon';
                            break;
                        default:
                            $day = 'Monday';
                            $color = 'lightgray';
                    }

                    $event_array[] = [
                        'id' => $sched->id,
                        'title' => "{$courseCode}<br>{$sched->room}<br>{$sectionName}",
                        'start' => date('Y-m-d', strtotime("{$day} this week")) . "T{$sched->time_starts}",
                        'end' => date('Y-m-d', strtotime("{$day} this week")) . "T{$sched->time_end}",
                        'color' => $color,
                        'textEscape' => 'false',
                        'textColor' => 'black',
                        'offering_id' => $offering_id,
                        'day' => $sched->day,
                        'day_type' => $sched->day_type
                    ];
                } catch (\Exception $e) {
                    \Log::error('Error in getSchedule: ' . $e->getMessage());
                    // Continue to the next schedule
                    continue;
                }
            }
        }
        return json_encode($event_array);
    }

    public function add_schedule_post(Request $request)
    {
        $offering_id = $request->offering_id;
        $time_start = $request->time_start;
        $time_end = $request->time_end;
        $section_name = $request->section_name;
        $instructor_id = $request->instructor;
        $day_type = $request->day_type;

        // Validate that start and end times are not the same
        if ($time_start === $time_end) {
            Session::flash('error', 'Start time and end time cannot be the same!');
            return redirect()->back();
        }

        // Get the curriculum and college information
        $offering = offerings_infos_table::find($offering_id);
        $curriculum = curriculum::find($offering->curriculum_id);
        $college_code = $curriculum->college_code;

        // Get priority level if any
        $priority = SchedulePriority::getPriorityLevel($curriculum->id);

        // Check if we have multiple days
        if ($request->has('multiple_days')) {
            $days = explode(',', $request->multiple_days);
        } else {
            $days = [$request->day]; // Single day
        }

        $successCount = 0;
        $conflictCount = 0;
        $existingCount = 0;

        foreach ($days as $day) {
            // Check for conflicts
            $conflicts = room_schedules::getAllConflicts(
                $day,
                $time_start,
                $time_end,
                $request->room,
                $instructor_id,
                $section_name
            );

            // Check for existing schedules
            $same_sched = DB::table('offerings_infos')
                ->join('room_schedules', 'offerings_infos.id', 'room_schedules.offering_id')
                ->where('offerings_infos.section_name', $section_name)
                ->where('room_schedules.day', $day)
                ->where('room_schedules.time_starts', date('H:i:s', strtotime($time_start)))
                ->where('room_schedules.time_end', date('H:i:s', strtotime($time_end)))
                ->get();

            if (count($same_sched) == 0) {
                $new_schedule = new room_schedules;
                $new_schedule->day = $day;
                $new_schedule->day_type = $day_type;
                $new_schedule->time_starts = date('H:i:s', strtotime($time_start));
                $new_schedule->time_end = date('H:i:s', strtotime($time_end));
                $new_schedule->room = $request->room;
                $new_schedule->offering_id = $offering_id;
                $new_schedule->college_code = $college_code;
                $new_schedule->priority_level = $priority;

                // Assign instructor if provided
                if ($instructor_id) {
                    $new_schedule->instructor = $instructor_id;
                }

                $new_schedule->save();
                $successCount++;

                if (!empty($conflicts)) {
                    $conflictCount++;
                }
            } else {
                $existingCount++;
            }
        }

        // Set appropriate flash message based on results
        if ($successCount > 0) {
            if ($conflictCount > 0) {
                Session::flash('warning', "Created {$successCount} schedule(s) with some conflicts.");
            } else {
                Session::flash('success', "Successfully created {$successCount} schedule(s)!");
            }
        }

        if ($existingCount > 0) {
            Session::flash('error', "{$existingCount} schedule(s) already exist and were not created.");
        }

        return redirect(url('/admin/course_scheduling/schedule', [$offering_id, $section_name]));
    }

    public function remove_schedule($schedule_id, $offering_id)
    {


        $offering = offerings_infos_table::find($offering_id);

        $schedule = room_schedules::find($schedule_id);
        $schedule->is_active = 0;
        $schedule->offering_id = NULL;
        $schedule->update();

        Session::flash('error', 'Changed the status of the schedule!');
        return redirect(url('/admin/course_scheduling/schedule', [$offering_id, $offering->section_name]));

    }

    public function attach_schedule($schedule_id, $offering_id)
    {


        $offering = offerings_infos_table::find($offering_id);

        $schedule = room_schedules::find($schedule_id);
        $schedule->is_active = 1;
        $schedule->offering_id = $offering_id;
        $schedule->update();

        Session::flash('success', 'Successfully in attaching the schedule!');
        return redirect(url('/admin/course_scheduling/schedule', [$offering_id, $offering->section_name]));

    }

    public function delete_schedule($schedule_id, $offering_id)
    {


        $offering = offerings_infos_table::find($offering_id);

        $schedule = room_schedules::find($schedule_id);
        $schedule->delete();

        Session::flash('error', 'Deleted the schedule!');
        return redirect(url('/admin/course_scheduling/schedule', [$offering_id, $offering->section_name]));

    }

    /**
     * Get time blocks for a specific day type
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTimeBlocks(Request $request)
    {
        $dayType = $request->day_type;
        $timeBlocks = TimeBlock::getByDayType($dayType);
        return response()->json($timeBlocks);
    }

    /**
     * Get available instructors for a specific time slot
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableInstructors(Request $request)
    {
        $day = $request->day;
        $timeStart = $request->time_start;
        $timeEnd = $request->time_end;

        // Get all instructors (users with accesslevel = 1)
        $instructors = User::where('accesslevel', 1)->get();

        $availableInstructors = [];

        foreach ($instructors as $instructor) {
            if (!room_schedules::hasInstructorConflict($day, $timeStart, $timeEnd, $instructor->id)) {
                $availableInstructors[] = [
                    'id' => $instructor->id,
                    'name' => $instructor->lastname . ', ' . $instructor->name
                ];
            }
        }

        return response()->json($availableInstructors);
    }

    /**
     * Check for schedule conflicts
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkConflicts(Request $request)
    {
        $day = $request->day;
        $timeStart = $request->time_start;
        $timeEnd = $request->time_end;
        $room = $request->room;
        $instructorId = $request->instructor_id;
        $sectionName = $request->section_name;

        $conflicts = room_schedules::getAllConflicts(
            $day,
            $timeStart,
            $timeEnd,
            $room,
            $instructorId,
            $sectionName
        );

        return response()->json([
            'has_conflicts' => !empty($conflicts),
            'conflicts' => $conflicts
        ]);
    }

    /**
     * Generate schedule for a section
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function generateSchedule(Request $request)
    {
        $sectionName = $request->section_name;
        $section = CtrSection::where('section_name', $sectionName)->first();

        if (!$section) {
            Session::flash('error', 'Section not found!');
            return redirect()->back();
        }

        // Get all offerings for this section
        $offerings = offerings_infos_table::where('section_name', $sectionName)->get();

        if ($offerings->isEmpty()) {
            Session::flash('error', 'No courses offered for this section!');
            return redirect()->back();
        }

        // Get all available rooms
        $rooms = CtrRoom::where('is_active', 1)->get();

        // Get all available instructors
        $instructors = User::where('accesslevel', 1)->get();

        // Get time blocks
        $mwfBlocks = TimeBlock::getByDayType('MWF');
        $tthBlocks = TimeBlock::getByDayType('TTh');
        $saturdayBlocks = TimeBlock::getByDayType('Saturday');

        // Sort offerings by priority
        $sortedOfferings = [];
        foreach ($offerings as $offering) {
            $curriculum = curriculum::find($offering->curriculum_id);
            $priority = SchedulePriority::getPriorityLevel($curriculum->id);

            $sortedOfferings[] = [
                'offering' => $offering,
                'curriculum' => $curriculum,
                'priority' => $priority
            ];
        }

        // Sort by priority (higher priority first)
        usort($sortedOfferings, function ($a, $b) {
            return $b['priority'] - $a['priority'];
        });

        $scheduledCount = 0;
        $failedCount = 0;

        // Schedule each offering
        foreach ($sortedOfferings as $offeringData) {
            $offering = $offeringData['offering'];
            $curriculum = $offeringData['curriculum'];

            // Determine day type based on curriculum
            $dayType = 'MWF'; // Default
            $timeBlocks = $mwfBlocks;

            if ($curriculum->is_complab) {
                $dayType = 'TTh';
                $timeBlocks = $tthBlocks;
            }

            // Try to schedule
            $scheduled = false;

            foreach ($timeBlocks as $timeBlock) {
                if ($dayType == 'MWF') {
                    $days = ['M', 'W', 'F'];
                } elseif ($dayType == 'TTh') {
                    $days = ['T', 'Th'];
                } else {
                    $days = ['Sa'];
                }

                foreach ($days as $day) {
                    foreach ($rooms as $room) {
                        // Skip if room is already scheduled for this time
                        if (
                            room_schedules::hasRoomConflict(
                                $day,
                                $timeBlock->start_time,
                                $timeBlock->end_time,
                                $room->room
                            )
                        ) {
                            continue;
                        }

                        // Skip if section is already scheduled for this time
                        if (
                            room_schedules::hasSectionConflict(
                                $day,
                                $timeBlock->start_time,
                                $timeBlock->end_time,
                                $sectionName
                            )
                        ) {
                            continue;
                        }

                        // Find available instructor
                        $availableInstructor = null;

                        foreach ($instructors as $instructor) {
                            if (
                                !room_schedules::hasInstructorConflict(
                                    $day,
                                    $timeBlock->start_time,
                                    $timeBlock->end_time,
                                    $instructor->id
                                )
                            ) {
                                $availableInstructor = $instructor;
                                break;
                            }
                        }

                        if ($availableInstructor) {
                            // Create schedule
                            $schedule = new room_schedules;
                            $schedule->day = $day;
                            $schedule->day_type = $dayType;
                            $schedule->time_starts = $timeBlock->start_time;
                            $schedule->time_end = $timeBlock->end_time;
                            $schedule->room = $room->room;
                            $schedule->offering_id = $offering->id;
                            $schedule->instructor = $availableInstructor->id;
                            $schedule->college_code = $curriculum->college_code;
                            $schedule->priority_level = $offeringData['priority'];
                            $schedule->is_active = 1;
                            $schedule->save();

                            $scheduled = true;
                            $scheduledCount++;
                            break 3; // Break out of all loops
                        }
                    }
                }
            }

            if (!$scheduled) {
                $failedCount++;
            }
        }

        if ($scheduledCount > 0) {
            Session::flash('success', "Successfully scheduled $scheduledCount courses. Failed to schedule $failedCount courses.");
        } else {
            Session::flash('error', "Failed to schedule any courses. Please try manual scheduling.");
        }

        return redirect()->back();
    }

    /**
     * View schedule in tabular format
     *
     * @param string $sectionName
     * @return \Illuminate\View\View
     */
    public function viewTabularSchedule($sectionName)
    {
        try {
            // First, check if the curricula table has the course_code column
            $hasColumnCourseCode = \Schema::hasColumn('curricula', 'course_code');
            $hasColumnControlCode = \Schema::hasColumn('curricula', 'control_code');

            // Build the query based on available columns
            $query = room_schedules::join('offerings_infos', 'offerings_infos.id', '=', 'room_schedules.offering_id')
                ->join('curricula', 'curricula.id', '=', 'offerings_infos.curriculum_id')
                ->leftJoin('users', 'users.id', '=', 'room_schedules.instructor')
                ->where('offerings_infos.section_name', $sectionName)
                ->where('room_schedules.is_active', 1);

            // Select the appropriate columns based on what's available
            $selectColumns = [
                'room_schedules.*',
                'users.name as instructor_name',
                'users.lastname as instructor_lastname'
            ];

            if ($hasColumnCourseCode) {
                $selectColumns[] = 'curricula.course_code';
            } elseif ($hasColumnControlCode) {
                $selectColumns[] = 'curricula.control_code as course_code';
            } else {
                // If neither column exists, use a default value
                $selectColumns[] = DB::raw("'N/A' as course_code");
            }

            // Always include course_name
            $selectColumns[] = 'curricula.course_name';

            $schedules = $query->select($selectColumns)->get();

            return view('admin.course_schedule.tabular_schedule', compact('schedules', 'sectionName'));
        } catch (\Exception $e) {
            \Log::error('Error in viewTabularSchedule: ' . $e->getMessage());
            Session::flash('error', 'An error occurred while loading the schedule. Please try again.');
            return redirect()->back();
        }
    }

    /**
     * Remove a batch of schedules based on day type
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeBatchSchedules(Request $request)
    {
        $scheduleId = $request->schedule_id;
        $offeringId = $request->offering_id;
        $dayType = $request->day_type;

        // Get the reference schedule
        $referenceSchedule = room_schedules::find($scheduleId);

        if (!$referenceSchedule) {
            return response()->json([
                'success' => false,
                'message' => 'Schedule not found'
            ]);
        }

        // Get the offering and section name
        $offering = offerings_infos_table::find($offeringId);
        if (!$offering) {
            return response()->json([
                'success' => false,
                'message' => 'Offering not found'
            ]);
        }

        // Get all schedules for this offering with the same time and day type
        $schedules = room_schedules::where('offering_id', $offeringId)
            ->where('time_starts', $referenceSchedule->time_starts)
            ->where('time_end', $referenceSchedule->time_end)
            ->where('day_type', $dayType)
            ->get();

        if ($schedules->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No schedules found to remove'
            ]);
        }

        $count = 0;
        foreach ($schedules as $schedule) {
            $schedule->is_active = 0;
            $schedule->offering_id = NULL;
            $schedule->update();
            $count++;
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully removed {$count} schedule(s)",
            'count' => $count
        ]);
    }
}
