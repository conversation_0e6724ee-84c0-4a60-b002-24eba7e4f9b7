<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CtrRoom extends Model
{
    protected $table = 'ctr_rooms';

    protected $fillable = [
        'room',
        'building',
        'college_code',
        'description'
    ];

    /**
     * Get the college that owns the room
     */
    public function college()
    {
        return $this->belongsTo('App\College', 'college_code', 'college_code');
    }

    /**
     * Get all schedules for this room
     */
    public function schedules()
    {
        return $this->hasMany('App\room_schedules', 'room', 'room');
    }

    /**
     * Get active schedules for this room
     */
    public function activeSchedules()
    {
        return $this->schedules()->where('is_active', 1);
    }

    /**
     * Check if this room is available at a specific time
     *
     * @param string $day Day of the week
     * @param string $timeStart Start time (format: HH:MM)
     * @param string $timeEnd End time (format: HH:MM)
     * @return bool
     */
    public function isAvailable($day, $timeStart, $timeEnd)
    {
        // Check for overlapping schedules
        $overlappingSchedules = $this->activeSchedules()
            ->where('day', $day)
            ->where(function ($query) use ($timeStart, $timeEnd) {
                // Schedule starts during another schedule
                $query->where(function ($q) use ($timeStart, $timeEnd) {
                    $q->where('time_starts', '<=', $timeStart)
                        ->where('time_end', '>=', $timeStart);
                })
                    // Schedule ends during another schedule
                    ->orWhere(function ($q) use ($timeStart, $timeEnd) {
                    $q->where('time_starts', '<=', $timeEnd)
                        ->where('time_end', '>=', $timeEnd);
                })
                    // Schedule completely contains another schedule
                    ->orWhere(function ($q) use ($timeStart, $timeEnd) {
                    $q->where('time_starts', '>=', $timeStart)
                        ->where('time_end', '<=', $timeEnd);
                });
            })
            ->count();

        return $overlappingSchedules === 0;
    }

    /**
     * Get available rooms for a specific time
     *
     * @param string $day Day of the week
     * @param string $timeStart Start time (format: HH:MM)
     * @param string $timeEnd End time (format: HH:MM)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAvailableRooms($day, $timeStart, $timeEnd)
    {
        // Get all active rooms
        $rooms = self::where('is_active', 1)->get();

        // Filter rooms that are available at the specified time
        return $rooms->filter(function ($room) use ($day, $timeStart, $timeEnd) {
            return $room->isAvailable($day, $timeStart, $timeEnd);
        });
    }
}
