<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-book"></i>
        {{ $program->program_name }} ({{ $curriculum_year }})
        <small></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/superadmin/course_offerings/curriculum')}}">Course Offering Curriculum</a></li>
        <li><a href="{{url('/superadmin/course_offerings/curriculum/program', [$program->program_code])}}">{{ $program->program_name }}</a></li>
        <li class="active">{{ $curriculum_year }}</li>
      </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            @if(Session::has('success'))
                <div class="alert alert-success">
                    {{Session::get('success')}}
                </div>
            @endif
            @if(Session::has('error'))
                <div class="alert alert-danger">
                    {{Session::get('error')}}
                </div>
            @endif
            @if(Session::has('warning'))
                <div class="alert alert-warning">
                    {{Session::get('warning')}}
                </div>
            @endif
            @if(Session::has('info'))
                <div class="alert alert-info">
                    {{Session::get('info')}}
                </div>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Subjects</h3>
                </div>
                <div class="box-body">
                    @if($curricula->isEmpty())
                        <div class="alert alert-info">
                            No subjects found for this curriculum.
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                        <th>Level</th>
                                        <th>Period</th>
                                        <th>CompLab</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($curricula as $subject)
                                        <tr>
                                            <td>{{ $subject->course_code }}</td>
                                            <td>{{ $subject->course_name }}</td>
                                            <td>{{ $subject->lec }}</td>
                                            <td>{{ $subject->lab }}</td>
                                            <td>{{ $subject->units }}</td>
                                            <td>{{ $subject->level }}</td>
                                            <td>{{ $subject->period }}</td>
                                            <td>{{ $subject->is_complab ? 'Yes' : 'No' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ url('/superadmin/course_offerings/curriculum/program', [$program->program_code]) }}" class="btn btn-default">Back to Program</a>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="{{asset('plugins/select2/select2.js')}}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
@endsection
