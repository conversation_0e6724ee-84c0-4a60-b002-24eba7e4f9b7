<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixUsersTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:fix-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix the id column in the users table to be auto-increment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Checking users table structure...');

        // Check if the id column exists
        $idColumn = DB::select("SHOW COLUMNS FROM users WHERE Field = 'id'");
        
        if (empty($idColumn)) {
            $this->info('The id column does not exist. Adding it...');
            
            // Add the id column
            DB::statement('ALTER TABLE users ADD id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST');
            
            $this->info('The id column has been added successfully!');
        } else {
            $this->info('The id column exists. Checking if it is auto-increment...');
            
            if ($idColumn[0]->Extra != 'auto_increment') {
                $this->info('The id column is not auto-increment. Fixing it...');
                
                // Check if there's a primary key
                $primaryKey = DB::select("SHOW KEYS FROM users WHERE Key_name = 'PRIMARY'");
                
                if (!empty($primaryKey)) {
                    $this->info('Dropping the primary key...');
                    DB::statement('ALTER TABLE users DROP PRIMARY KEY');
                }
                
                // Modify the id column to be auto-increment
                DB::statement('ALTER TABLE users MODIFY id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY');
                
                $this->info('The id column has been fixed successfully!');
            } else {
                $this->info('The id column is already auto-increment. No changes needed.');
            }
        }
        
        return 0;
    }
}
