<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Class Schedule Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin-bottom: 5px;
        }
        .date {
            margin-bottom: 20px;
        }
        .filter-info {
            margin-bottom: 20px;
        }
        .filter-info table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .filter-info th, .filter-info td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .filter-info th {
            background-color: #f2f2f2;
            width: 25%;
        }
        .day-header {
            background-color: #f2f2f2;
            padding: 8px;
            margin-top: 20px;
            margin-bottom: 10px;
            font-weight: bold;
            border: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Class Schedule Report</h1>
        <p>Generated on: {{ $date }}</p>
    </div>
    
    <div class="filter-info">
        <h3>Filter Information</h3>
        <table>
            <tr>
                <th>Department</th>
                <td>{{ $filterInfo['department'] }}</td>
                <th>Instructor</th>
                <td>{{ $filterInfo['instructor'] }}</td>
            </tr>
            <tr>
                <th>Room</th>
                <td>{{ $filterInfo['room'] }}</td>
                <th>Section</th>
                <td>{{ $filterInfo['section'] }}</td>
            </tr>
        </table>
    </div>
    
    @if(count($schedules) > 0)
        @foreach($schedules as $day => $daySchedules)
            <div class="day-header">{{ $day }}</div>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Course Code</th>
                        <th>Section</th>
                        <th>Room</th>
                        <th>Instructor</th>
                        <th>Time</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($daySchedules as $index => $schedule)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $schedule['course_code'] }}</td>
                            <td>{{ $schedule['section'] }}</td>
                            <td>{{ $schedule['room'] }}</td>
                            <td>{{ $schedule['instructor'] }}</td>
                            <td>{{ date('h:i A', strtotime($schedule['time_starts'])) }} - {{ date('h:i A', strtotime($schedule['time_end'])) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endforeach
    @else
        <p>No schedules found matching the specified criteria.</p>
    @endif
    
    <div class="footer">
        <p>This is an automatically generated report. For any questions, please contact the system administrator.</p>
    </div>
</body>
</html>
