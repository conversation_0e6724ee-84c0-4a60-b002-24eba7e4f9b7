@if($availableCurricula->isEmpty())
    <div class="alert alert-info">
        No available subjects found for this section.
    </div>
@else
    <form id="addSelectedForm" action="{{ url('/admin/course_offerings/curriculum/section/add_selected', [$section->id]) }}" method="POST">
        {{ csrf_field() }}
        <div class="form-group">
            <div class="checkbox">
                <label>
                    <input type="checkbox" id="selectAll"> Select All
                </label>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="5%">Select</th>
                        <th>Course Code</th>
                        <th>Course Name</th>
                        <th>Lec</th>
                        <th>Lab</th>
                        <th>Units</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($availableCurricula as $curriculum)
                        <tr>
                            <td>
                                <input type="checkbox" name="curriculum_ids[]" value="{{ $curriculum->id }}">
                            </td>
                            <td>{{ $curriculum->course_code }}</td>
                            <td>{{ $curriculum->course_name }}</td>
                            <td>{{ $curriculum->lec }}</td>
                            <td>{{ $curriculum->lab }}</td>
                            <td>{{ $curriculum->units }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </form>
@endif
