<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\CtrRoom;
use App\CtrSection;
use App\instructors_infos;
use App\room_schedules;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use PDF;

class DashboardExportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    /**
     * Export room utilization report as PDF
     */
    public function exportRoomUtilization(Request $request)
    {
        $rooms = CtrRoom::where('is_active', 1)->get();
        $utilization = [];
        
        foreach ($rooms as $room) {
            $totalHours = 0;
            $schedules = room_schedules::where('room', $room->room)
                ->where('is_active', 1)
                ->get();
            
            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);
                $totalHours += $end->diffInHours($start);
            }
            
            // Assuming 12 hours per day, 6 days per week = 72 hours max
            $utilizationRate = ($totalHours > 0) ? ($totalHours / 72) * 100 : 0;
            
            $utilization[] = [
                'room' => $room->room,
                'building' => $room->building,
                'college_code' => $room->college_code,
                'total_hours' => $totalHours,
                'utilization_rate' => round($utilizationRate, 2)
            ];
        }
        
        // Sort by utilization rate (descending)
        usort($utilization, function($a, $b) {
            return $b['utilization_rate'] <=> $a['utilization_rate'];
        });
        
        $pdf = PDF::loadView('super_admin.dashboard.exports.room_utilization', [
            'utilization' => $utilization,
            'date' => Carbon::now()->format('F d, Y')
        ]);
        
        return $pdf->download('room_utilization_report.pdf');
    }

    /**
     * Export instructor load report as PDF
     */
    public function exportInstructorLoad(Request $request)
    {
        $instructors = User::where('accesslevel', 1)->get();
        $loads = [];
        
        foreach ($instructors as $instructor) {
            $totalHours = 0;
            $schedules = room_schedules::where('instructor', $instructor->id)
                ->where('is_active', 1)
                ->get();
            
            foreach ($schedules as $schedule) {
                $start = Carbon::parse($schedule->time_starts);
                $end = Carbon::parse($schedule->time_end);
                $totalHours += $end->diffInHours($start);
            }
            
            $loads[] = [
                'instructor_id' => $instructor->id,
                'instructor_name' => $instructor->name . ' ' . $instructor->lastname,
                'total_hours' => $totalHours
            ];
        }
        
        // Sort by total hours (descending)
        usort($loads, function($a, $b) {
            return $b['total_hours'] <=> $a['total_hours'];
        });
        
        $pdf = PDF::loadView('super_admin.dashboard.exports.instructor_load', [
            'loads' => $loads,
            'date' => Carbon::now()->format('F d, Y')
        ]);
        
        return $pdf->download('instructor_load_report.pdf');
    }

    /**
     * Export enrollment statistics report as PDF
     */
    public function exportEnrollmentStats(Request $request)
    {
        $sections = CtrSection::where('is_active', 1)->get();
        $stats = [];
        
        foreach ($sections as $section) {
            // Get enrollment count (this is a placeholder - adjust based on your actual data structure)
            $enrollmentCount = rand(15, 40); // Replace with actual enrollment count
            $capacity = 40; // Replace with actual capacity
            
            $stats[] = [
                'section' => $section->section_name,
                'enrollment' => $enrollmentCount,
                'capacity' => $capacity,
                'percentage' => round(($enrollmentCount / $capacity) * 100, 2)
            ];
        }
        
        // Sort by enrollment percentage (descending)
        usort($stats, function($a, $b) {
            return $b['percentage'] <=> $a['percentage'];
        });
        
        $pdf = PDF::loadView('super_admin.dashboard.exports.enrollment_stats', [
            'stats' => $stats,
            'date' => Carbon::now()->format('F d, Y')
        ]);
        
        return $pdf->download('enrollment_statistics_report.pdf');
    }

    /**
     * Export schedule report as PDF
     */
    public function exportSchedule(Request $request)
    {
        $department = $request->input('department');
        $instructor = $request->input('instructor');
        $room = $request->input('room');
        $section = $request->input('section');
        
        $query = room_schedules::where('is_active', 1);
        
        // Apply filters
        if ($department) {
            $query->where('college_code', $department);
        }
        
        if ($instructor) {
            $query->where('instructor', $instructor);
        }
        
        if ($room) {
            $query->where('room', $room);
        }
        
        if ($section) {
            // Join with offerings to filter by section
            $query->join('offerings_infos', 'room_schedules.offering_id', '=', 'offerings_infos.id')
                  ->where('offerings_infos.section', $section);
        }
        
        $schedules = $query->get();
        
        // Group schedules by day
        $groupedSchedules = [];
        foreach ($schedules as $schedule) {
            if (!isset($groupedSchedules[$schedule->day])) {
                $groupedSchedules[$schedule->day] = [];
            }
            
            // Get offering details
            $offering = \App\offerings_infos_table::find($schedule->offering_id);
            $courseCode = $offering ? $offering->course_code : 'Unknown';
            $sectionName = $offering ? $offering->section : 'Unknown';
            
            // Get instructor name
            $instructorName = 'Not Assigned';
            if ($schedule->instructor) {
                $instructorUser = User::find($schedule->instructor);
                if ($instructorUser) {
                    $instructorName = $instructorUser->name . ' ' . $instructorUser->lastname;
                }
            }
            
            $groupedSchedules[$schedule->day][] = [
                'id' => $schedule->id,
                'course_code' => $courseCode,
                'section' => $sectionName,
                'room' => $schedule->room,
                'instructor' => $instructorName,
                'time_starts' => $schedule->time_starts,
                'time_end' => $schedule->time_end
            ];
        }
        
        // Sort each day's schedules by start time
        foreach ($groupedSchedules as $day => $daySchedules) {
            usort($groupedSchedules[$day], function($a, $b) {
                return strtotime($a['time_starts']) <=> strtotime($b['time_starts']);
            });
        }
        
        // Sort days in correct order
        $orderedDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        $orderedSchedules = [];
        foreach ($orderedDays as $day) {
            if (isset($groupedSchedules[$day])) {
                $orderedSchedules[$day] = $groupedSchedules[$day];
            }
        }
        
        $filterInfo = [
            'department' => $department ? College::where('college_code', $department)->first()->college_name : 'All',
            'instructor' => $instructor ? User::find($instructor)->name . ' ' . User::find($instructor)->lastname : 'All',
            'room' => $room ?: 'All',
            'section' => $section ?: 'All'
        ];
        
        $pdf = PDF::loadView('super_admin.dashboard.exports.schedule', [
            'schedules' => $orderedSchedules,
            'filterInfo' => $filterInfo,
            'date' => Carbon::now()->format('F d, Y')
        ]);
        
        return $pdf->download('class_schedule_report.pdf');
    }
}
