<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SchedulingNotification extends Model
{
    protected $table = 'scheduling_notifications';
    
    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'reference_id',
        'is_read',
        'created_at',
        'updated_at'
    ];
    
    /**
     * Get the user that owns the notification.
     */
    public function user()
    {
        return $this->belongsTo('App\User');
    }
    
    /**
     * Create a new conflict notification
     */
    public static function createConflictNotification($userId, $roomId, $scheduleId1, $scheduleId2)
    {
        $room = CtrRoom::find($roomId);
        $schedule1 = room_schedules::find($scheduleId1);
        $schedule2 = room_schedules::find($scheduleId2);
        
        if (!$room || !$schedule1 || !$schedule2) {
            return false;
        }
        
        $title = 'Scheduling Conflict Detected';
        $message = 'Conflict detected in room ' . $room->room . ' between schedules #' . $scheduleId1 . ' and #' . $scheduleId2;
        
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'conflict',
            'reference_id' => $roomId,
            'is_read' => 0
        ]);
    }
    
    /**
     * Get unread notifications for a user
     */
    public static function getUnreadNotifications($userId)
    {
        return self::where('user_id', $userId)
            ->where('is_read', 0)
            ->orderBy('created_at', 'desc')
            ->get();
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead()
    {
        $this->is_read = 1;
        return $this->save();
    }
}
