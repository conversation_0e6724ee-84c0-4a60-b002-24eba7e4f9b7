<?php

namespace App;

use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'name',
        'middlename',
        'lastname',
        'extensionname',
        'accesslevel',
        'email',
        'password',
        'is_first_login',
        'college_code'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the instructor information associated with the user
     */
    public function instructorInfo()
    {
        return $this->hasOne('App\instructors_infos', 'instructor_id');
    }

    /**
     * Get the college associated with the user
     */
    public function college()
    {
        return $this->belongsTo('App\College', 'college_code', 'college_code');
    }
}
