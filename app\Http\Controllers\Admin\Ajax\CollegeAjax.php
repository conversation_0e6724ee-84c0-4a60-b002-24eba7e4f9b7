<?php

namespace App\Http\Controllers\Admin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\College;
use App\ProgramName;
use App\academic_programs;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;

class CollegeAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('admin');
    }

    public function get_college_codes(Request $request)
    {
        if ($request->ajax()) {
            $term = $request->input('term', '');

            $codes = College::where('is_active', 1)
                ->where('college_code', 'like', "%$term%")
                ->select('college_code as id', 'college_code as text')
                ->orderBy('college_code')
                ->limit(15)
                ->get();

            return response()->json(['results' => $codes]);
        }
    }

    public function get_college_names(Request $request)
    {
        if ($request->ajax()) {
            $term = $request->input('term', '');

            $names = College::where('is_active', 1)
                ->where('college_name', 'like', "%$term%")
                ->select('college_name as id', 'college_name as text')
                ->orderBy('college_name')
                ->limit(10)
                ->get();

            return response()->json(['results' => $names]);
        }
    }

    public function get_courses(Request $request)
    {
        if ($request->ajax()) {
            $term = $request->input('term', '');
            $college_code = $request->input('college_code', '');

            // Get courses based on filters
            if (!empty($college_code)) {
                // Find the college by code
                $college = College::where('college_code', $college_code)->first();

                if ($college) {
                    // Get programs for a specific college from programs table
                    $programs = \App\Program::where('college_id', $college->id);

                    // Apply search term if provided
                    if (!empty($term)) {
                        $programs->where(function ($query) use ($term) {
                            $query->where('program_code', 'like', "%$term%")
                                ->orWhere('program_name', 'like', "%$term%");
                        });
                    }

                    $programs = $programs->get();

                    // Format for Select2
                    $result = [];
                    foreach ($programs as $program) {
                        if (count($result) >= 50) {
                            break; // Limit to 50 results
                        }

                        $displayText = $program->program_code;
                        if (!empty($program->program_name)) {
                            $displayText .= ' - ' . $program->program_name;
                        }

                        $result[] = [
                            'id' => $program->program_code,
                            'text' => $displayText
                        ];
                    }

                    return response()->json(['results' => $result]);
                }
            }

            // If no college_code provided or college not found, get all programs
            $programs = \App\Program::select('program_code', 'program_name');

            // Apply search term if provided
            if (!empty($term)) {
                $programs->where(function ($query) use ($term) {
                    $query->where('program_code', 'like', "%$term%")
                        ->orWhere('program_name', 'like', "%$term%");
                });
            }

            $programs = $programs->limit(50)->get();

            // Format for Select2
            $result = [];
            foreach ($programs as $program) {
                $displayText = $program->program_code;
                if (!empty($program->program_name)) {
                    $displayText .= ' - ' . $program->program_name;
                }

                $result[] = [
                    'id' => $program->program_code,
                    'text' => $displayText
                ];
            }

            return response()->json(['results' => $result]);
        }
    }

    public function edit_college(Request $request)
    {
        if ($request->ajax()) {
            $college_id = $request->input('college_id');
            $college = College::find($college_id);

            return view('admin.college.ajax.edit_college', compact('college'));
        }
    }

    public function search_colleges(Request $request)
    {
        if ($request->ajax()) {
            $code = $request->input('code');
            $name = $request->input('name');
            $description = $request->input('description');

            $query = College::where('is_active', 1);

            // Apply filters if they are provided
            if (!empty($code)) {
                $query->where('college_code', 'like', "%$code%");
            }

            if (!empty($name)) {
                $query->where('college_name', 'like', "%$name%");
            }

            // If program filter is provided, filter colleges by programs in programs table
            if (!empty($description)) {
                // Get college IDs that have this program
                $collegeIdsWithProgram = \App\Program::where('program_code', 'like', "%$description%")
                    ->orWhere('program_name', 'like', "%$description%")
                    ->pluck('college_id')
                    ->toArray();

                // Add these college IDs to the query
                if (!empty($collegeIdsWithProgram)) {
                    $query->whereIn('id', $collegeIdsWithProgram);
                } else {
                    // If no matching programs found, return no results
                    $query->where('id', 0); // This will ensure no results
                }
            }

            $colleges = $query->get();

            return view('admin.college.ajax.search_colleges', compact('colleges'));
        }
    }

    public function search_colleges_archive(Request $request)
    {
        if ($request->ajax()) {
            $code = $request->input('code');
            $name = $request->input('name');
            $description = $request->input('description');

            $query = College::where('is_active', 0);

            // Apply filters if they are provided
            if (!empty($code)) {
                $query->where('college_code', 'like', "%$code%");
            }

            if (!empty($name)) {
                $query->where('college_name', 'like', "%$name%");
            }

            // If program filter is provided, filter colleges by programs in programs table
            if (!empty($description)) {
                // Get college IDs that have this program
                $collegeIdsWithProgram = \App\Program::where('program_code', 'like', "%$description%")
                    ->orWhere('program_name', 'like', "%$description%")
                    ->pluck('college_id')
                    ->toArray();

                // Add these college IDs to the query
                if (!empty($collegeIdsWithProgram)) {
                    $query->whereIn('id', $collegeIdsWithProgram);
                } else {
                    // If no matching programs found, return no results
                    $query->where('id', 0); // This will ensure no results
                }
            }

            $colleges = $query->get();

            return view('admin.college.ajax.search_colleges_archive', compact('colleges'));
        }
    }

    /**
     * Get college code by college name
     */
    public function get_college_by_name(Request $request)
    {
        if ($request->ajax()) {
            $name = $request->input('name');

            $college = College::where('college_name', $name)
                ->where('is_active', 1)
                ->first();

            if ($college) {
                return response()->json([
                    'success' => true,
                    'college_code' => $college->college_code
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'College not found'
            ]);
        }
    }

    /**
     * Get programs for a specific college
     */
    public function get_programs(Request $request)
    {
        if ($request->ajax()) {
            $college_code = $request->input('college_code');

            // Log the request parameters for debugging
            Log::info('Getting programs for college: ' . $college_code);

            if (!$college_code) {
                // If no college code provided, return all programs
                $allPrograms = \App\Program::select('program_code', 'program_name')->distinct()->get();
                return response()->json($allPrograms);
            }

            // Get the college
            $college = College::where('college_code', $college_code)
                ->where('is_active', 1)
                ->first();

            if (!$college) {
                return response()->json([]);
            }

            // Get programs from the programs table
            $programs = \App\Program::where('college_id', $college->id)
                ->select('program_code', 'program_name')
                ->get();

            if ($programs->isEmpty()) {
                // If no programs found in the programs table, try to get from program_codes field
                $programCodes = $college->getProgramCodes();

                // Create basic entries with code as name
                $programs = [];
                foreach ($programCodes as $code) {
                    $programs[] = [
                        'program_code' => $code,
                        'program_name' => $code
                    ];
                }
            } else {
                $programs = $programs->toArray();
            }

            Log::info('Found ' . count($programs) . ' programs for college: ' . $college_code);

            return response()->json($programs);
        }
    }
}
