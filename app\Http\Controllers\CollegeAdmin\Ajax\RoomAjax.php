<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\CtrRoom;

class RoomAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Search rooms
     */
    public function searchRooms(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get search term from request
        $search = $request->input('search');
        
        // Search for rooms
        $rooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->where(function($query) use ($search) {
                $query->where('room', 'like', '%' . $search . '%')
                    ->orWhere('building', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%');
            })
            ->get();
        
        return view('collegeadmin.ajax.search_rooms', compact('rooms'));
    }

    /**
     * Search archived rooms
     */
    public function searchRoomsArchive(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get search term from request
        $search = $request->input('search');
        
        // Search for archived rooms
        $rooms = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 0)
            ->where(function($query) use ($search) {
                $query->where('room', 'like', '%' . $search . '%')
                    ->orWhere('building', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%');
            })
            ->get();
        
        return view('collegeadmin.ajax.search_rooms_archive', compact('rooms'));
    }

    /**
     * Get room details for editing
     */
    public function editRoom(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get room ID from request
        $roomId = $request->input('room_id');
        
        // Get the room
        $room = CtrRoom::where('id', $roomId)
            ->where('college_code', $collegeCode)
            ->first();
            
        if (!$room) {
            return response()->json([
                'success' => false,
                'message' => 'Room not found or does not belong to your college.'
            ]);
        }
        
        return response()->json([
            'success' => true,
            'room' => $room
        ]);
    }
}
