/**
 * Faculty Loading Drag and Drop Functionality
 * 
 * This file contains common functions for implementing drag and drop
 * functionality for faculty loading across different admin interfaces.
 */

/**
 * Initialize draggable items for faculty loading
 * @param {string} selector - The selector for draggable items
 */
function initDraggableItems(selector) {
    $(selector).each(function() {
        // Initialize draggable
        $(this).draggable({
            zIndex: 999,
            revert: true,
            revertDuration: 0,
            helper: 'clone',
            appendTo: 'body',
            scroll: false,
            start: function(event, ui) {
                $(this).css('z-index', 1000);
            }
        });
        
        // Store event data
        var eventObject = {
            title: $(this).attr('data-object') || $(this).text(),
            offering: $(this).attr('data-object') || $(this).attr('data-offering-id')
        };
        
        $(this).data('eventObject', eventObject);
    });
}

/**
 * Initialize calendar with droppable functionality
 * @param {string} selector - The selector for the calendar element
 * @param {object} options - Additional options for the calendar
 * @param {function} addCallback - Callback function when an item is dropped
 * @param {function} removeCallback - Callback function when an event is removed
 */
function initDroppableCalendar(selector, options, addCallback, removeCallback) {
    var defaultOptions = {
        header: {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek,agendaDay'
        },
        defaultView: 'agendaWeek',
        minTime: '07:00:00',
        maxTime: '22:00:00',
        hiddenDays: [0], // Hide Sunday
        firstDay: 1, // Monday as first day
        height: 500,
        allDaySlot: false,
        columnFormat: 'ddd',
        editable: false,
        droppable: true, // Allow external events to be dropped onto the calendar
        drop: function(date, jsEvent, ui, resourceId) {
            // Called when a draggable item is dropped onto the calendar
            var $item = $(ui.helper);
            var eventData = $item.data('eventObject');
            
            if (addCallback && typeof addCallback === 'function') {
                addCallback(eventData, date);
            }
        },
        eventClick: function(calEvent, jsEvent, view) {
            // Handle click on calendar event (for removing)
            if (removeCallback && typeof removeCallback === 'function') {
                removeCallback(calEvent);
            }
        },
        eventRender: function(event, element) {
            element.find('div.fc-title').html(element.find('div.fc-title').text());
        }
    };
    
    // Merge default options with custom options
    var calendarOptions = $.extend({}, defaultOptions, options);
    
    // Initialize the calendar
    $(selector).fullCalendar(calendarOptions);
}

/**
 * Add faculty load via AJAX
 * @param {string} url - The URL to send the AJAX request to
 * @param {object} data - The data to send with the request
 * @param {function} successCallback - Callback function on success
 * @param {function} errorCallback - Callback function on error
 */
function addFacultyLoad(url, data, successCallback, errorCallback) {
    $.ajax({
        url: url,
        type: 'GET',
        data: data,
        success: function(response) {
            if (successCallback && typeof successCallback === 'function') {
                successCallback(response);
            } else {
                if (response.success) {
                    alert('Course added to faculty load successfully!');
                    location.reload(); // Reload the page to update the schedule
                } else {
                    alert('Error: ' + response.message);
                }
            }
        },
        error: function(xhr, status, error) {
            if (errorCallback && typeof errorCallback === 'function') {
                errorCallback(xhr, status, error);
            } else {
                if (xhr.status == 500) {
                    alert('Conflict in Schedule Found!');
                } else if (xhr.status == 404) {
                    var override = confirm('The number of units loaded exceeds. Do you want to override?');
                    if (override) {
                        // Handle override logic
                    }
                } else {
                    alert('Error adding course to faculty load.');
                }
            }
        }
    });
}

/**
 * Remove faculty load via AJAX
 * @param {string} url - The URL to send the AJAX request to
 * @param {object} data - The data to send with the request
 * @param {function} successCallback - Callback function on success
 * @param {function} errorCallback - Callback function on error
 */
function removeFacultyLoad(url, data, successCallback, errorCallback) {
    if (confirm('Are you sure you want to remove this course from the faculty load?')) {
        $.ajax({
            url: url,
            type: 'GET',
            data: data,
            success: function(response) {
                if (successCallback && typeof successCallback === 'function') {
                    successCallback(response);
                } else {
                    alert('Course removed from faculty load successfully!');
                    location.reload(); // Reload the page to update the schedule
                }
            },
            error: function(xhr, status, error) {
                if (errorCallback && typeof errorCallback === 'function') {
                    errorCallback(xhr, status, error);
                } else {
                    alert('Error removing course from faculty load.');
                }
            }
        });
    }
}
