<div class="row">
    <div class="col-md-12">
        @if($curriculum_year->isEmpty() && $sections->isEmpty())
            <div class="alert alert-info">
                No curriculum years or sections found for this program.
            </div>
        @else
            @if(!$curriculum_year->isEmpty())
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Curriculum Years</h3>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Year</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($curriculum_year as $cy)
                                        <tr>
                                            <td>{{ $cy->curriculum_year }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-info"
                                                        onclick="viewCurriculumDetails('{{ $cy->curriculum_year }}')">
                                                    <i class="fa fa-eye"></i> View Details
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            @if(!$sections->isEmpty())
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Active Sections</h3>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Section Name</th>
                                        <th>Level</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sections as $section)
                                        <tr>
                                            <td>{{ $section->section_name }}</td>
                                            <td>{{ $section->level }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-success"
                                                        onclick="viewSectionOfferings('{{ $section->section_name }}')">
                                                    <i class="fa fa-list"></i> View Offerings
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        @endif
    </div>
</div>

<script>
function viewCurriculumDetails(curriculumYear) {
    window.location.href = '{{ url("/admin/course_offerings/curriculum/program/curriculum", [$program_code]) }}/' + curriculumYear;
}

function viewSectionOfferings(sectionName) {
    // Find the section ID based on the section name
    var sectionId = null;
    @foreach($sections as $section)
        if ('{{ $section->section_name }}' === sectionName) {
            sectionId = {{ $section->id }};
        }
    @endforeach

    if (sectionId) {
        window.location.href = '{{ url("/admin/course_offerings/curriculum/section") }}/' + sectionId;
    } else {
        alert('Section not found');
    }
}
</script>