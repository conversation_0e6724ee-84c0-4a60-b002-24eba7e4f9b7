<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\User;
use App\instructors_infos;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Hash;

class FacultyController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Display a listing of faculty members for the college
     */
    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get faculty members for this college
        $faculty = User::where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->get();

        return view('collegeadmin.faculty.index', compact('faculty', 'college'));
    }

    /**
     * Show the form for adding a new faculty member
     */
    public function create()
    {
        return view('collegeadmin.faculty.create');
    }

    /**
     * Store a newly created faculty member
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'username' => 'required|string|max:255|unique:users',
            'name' => 'required|string|max:255',
            'middlename' => 'nullable|string|max:255',
            'lastname' => 'required|string|max:255',
            'extensionname' => 'nullable|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'department' => 'nullable|string|max:255',
            'employee_type' => 'nullable|string|max:255',
            'gender' => 'nullable|string|max:255',
            'cell_no' => 'nullable|string|max:255',
            'emerg_cont_#' => 'nullable|string|max:255',
            'program_graduated' => 'nullable|string|max:255',
        ]);

        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        DB::beginTransaction();
        try {
            // Create the user
            $user = new User();
            $user->username = $request->username;
            $user->name = $request->name;
            $user->middlename = $request->middlename;
            $user->lastname = $request->lastname;
            $user->extensionname = $request->extensionname;
            $user->accesslevel = 1; // Instructor access level
            $user->email = $request->email;
            $user->password = Hash::make($request->password);
            $user->is_first_login = 1; // Require password change on first login
            $user->college_code = $collegeCode; // Associate with the college
            $user->save();

            // Create instructor info
            $instructorInfo = new instructors_infos();
            $instructorInfo->instructor_id = $user->id;
            $instructorInfo->college = $collegeCode;
            $instructorInfo->department = $request->department;
            $instructorInfo->employee_type = $request->employee_type;
            $instructorInfo->gender = $request->gender;
            $instructorInfo->cell_no = $request->cell_no;
            $instructorInfo->{'emerg_cont_#'} = $request->{'emerg_cont_#'};
            $instructorInfo->program_graduated = $request->program_graduated;
            $instructorInfo->save();

            DB::commit();
            Session::flash('success', 'Faculty member added successfully!');
            return redirect()->route('collegeadmin.faculty.index');
        } catch (\Exception $e) {
            DB::rollback();
            Session::flash('error', 'Error adding faculty member: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified faculty member
     */
    public function show($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get the faculty member
        $faculty = User::where('id', $id)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->firstOrFail();

        // Get instructor info
        $info = instructors_infos::where('instructor_id', $id)->first();

        // Get teaching load
        $schedules = \App\room_schedules::where('instructor', $id)
            ->where('is_active', 1)
            ->get();

        return view('collegeadmin.faculty.show', compact('faculty', 'info', 'college', 'schedules'));
    }

    /**
     * Show the form for editing the specified faculty member
     */
    public function edit($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get the faculty member
        $faculty = User::where('id', $id)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->firstOrFail();

        // Get instructor info
        $info = instructors_infos::where('instructor_id', $id)->first();

        return view('collegeadmin.faculty.edit', compact('faculty', 'info', 'college'));
    }

    /**
     * Update the specified faculty member
     */
    public function update(Request $request, $id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get the faculty member
        $faculty = User::where('id', $id)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->firstOrFail();

        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'middlename' => 'nullable|string|max:255',
            'lastname' => 'required|string|max:255',
            'extensionname' => 'nullable|string|max:255',
            'email' => "required|string|email|max:255|unique:users,email,{$id}",
            'department' => 'nullable|string|max:255',
            'employee_type' => 'nullable|string|max:255',
            'gender' => 'nullable|string|max:255',
            'cell_no' => 'nullable|string|max:255',
            'emerg_cont_#' => 'nullable|string|max:255',
            'program_graduated' => 'nullable|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            // Update the user
            $faculty->name = $request->name;
            $faculty->middlename = $request->middlename;
            $faculty->lastname = $request->lastname;
            $faculty->extensionname = $request->extensionname;
            $faculty->email = $request->email;

            // If this faculty doesn't have a college_code yet, assign it
            if (empty($faculty->college_code)) {
                $faculty->college_code = $collegeCode;
            }

            $faculty->save();

            // Update instructor info
            $info = instructors_infos::where('instructor_id', $id)->first();
            if (!$info) {
                // Create instructor info if it doesn't exist
                $info = new instructors_infos();
                $info->instructor_id = $id;
            }

            // Update instructor info fields
            $info->college = $collegeCode;
            $info->department = $request->department;
            $info->employee_type = $request->employee_type;
            $info->gender = $request->gender;
            $info->cell_no = $request->cell_no;
            $info->{'emerg_cont_#'} = $request->{'emerg_cont_#'};
            $info->program_graduated = $request->program_graduated;
            $info->save();

            DB::commit();
            Session::flash('success', 'Faculty member updated successfully!');
            return redirect()->route('collegeadmin.faculty.index');
        } catch (\Exception $e) {
            DB::rollback();
            Session::flash('error', 'Error updating faculty member: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified faculty member
     */
    public function destroy($id)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get college information
        $college = \App\College::where('college_code', $collegeCode)->first();

        if (!$college) {
            Session::flash('error', 'College not found');
            return redirect()->back();
        }

        // Get the faculty member
        $faculty = User::where('id', $id)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->firstOrFail();

        // Instead of deleting, we'll set the user to inactive
        $faculty->is_active = 0;
        $faculty->save();

        Session::flash('success', 'Faculty member deactivated successfully!');
        return redirect()->route('collegeadmin.faculty.index');
    }
}
