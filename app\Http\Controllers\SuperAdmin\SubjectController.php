<?php

namespace App\Http\Controllers\SuperAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Subject;
use App\curriculum;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class SubjectController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('superadmin');
    }

    /**
     * Display a listing of the subjects.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $subjects = Subject::orderBy('subject_code')->get();
        return view('super_admin.subjects.index', compact('subjects'));
    }

    /**
     * Show the form for creating a new subject.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('super_admin.subjects.create');
    }

    /**
     * Store a newly created subject in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'subject_code' => 'required|unique:subjects',
            'subject_name' => 'required',
            'lec' => 'required|numeric|min:0',
            'lab' => 'required|numeric|min:0',
            'units' => 'required|numeric|min:0',
        ]);

        $subject = new Subject();
        $subject->subject_code = $request->subject_code;
        $subject->subject_name = $request->subject_name;
        $subject->lec = $request->lec;
        $subject->lab = $request->lab;
        $subject->units = $request->units;
        $subject->is_complab = $request->has('is_complab') ? 1 : 0;
        $subject->save();

        Session::flash('success', 'Subject created successfully!');
        Log::info('User ' . Auth::user()->name . ' created a new subject: ' . $subject->subject_code);

        return redirect()->route('superadmin.subjects.index');
    }

    /**
     * Show the form for editing the specified subject.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $subject = Subject::findOrFail($id);
        return view('super_admin.subjects.edit', compact('subject'));
    }

    /**
     * Update the specified subject in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $subject = Subject::findOrFail($id);

        $this->validate($request, [
            'subject_code' => 'required|unique:subjects,subject_code,' . $id,
            'subject_name' => 'required',
            'lec' => 'required|numeric|min:0',
            'lab' => 'required|numeric|min:0',
            'units' => 'required|numeric|min:0',
        ]);

        $subject->subject_code = $request->subject_code;
        $subject->subject_name = $request->subject_name;
        $subject->lec = $request->lec;
        $subject->lab = $request->lab;
        $subject->units = $request->units;
        $subject->is_complab = $request->has('is_complab') ? 1 : 0;
        $subject->save();

        // Update all curriculum entries that use this subject
        curriculum::where('subject_id', $id)->update([
            'course_code' => $request->subject_code,
            'course_name' => $request->subject_name,
            'lec' => $request->lec,
            'lab' => $request->lab,
            'units' => $request->units,
            'is_complab' => $request->has('is_complab') ? 1 : 0
        ]);

        Session::flash('success', 'Subject updated successfully!');
        Log::info('User ' . Auth::user()->name . ' updated subject: ' . $subject->subject_code);

        return redirect()->route('superadmin.subjects.index');
    }
}
