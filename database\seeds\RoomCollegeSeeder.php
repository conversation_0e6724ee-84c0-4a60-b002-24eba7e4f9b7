<?php

use Illuminate\Database\Seeder;
use App\CtrRoom;
use App\College;

class RoomCollegeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get all colleges
        $colleges = College::where('is_active', 1)->get();
        
        if ($colleges->isEmpty()) {
            $this->command->info('No active colleges found. Please run the college seeder first.');
            return;
        }
        
        // Get all rooms
        $rooms = CtrRoom::all();
        
        if ($rooms->isEmpty()) {
            $this->command->info('No rooms found. Please run the room seeder first.');
            return;
        }
        
        // Assign colleges to rooms based on building or other criteria
        foreach ($rooms as $room) {
            // Skip rooms that already have a college assigned
            if (!empty($room->college_code)) {
                continue;
            }
            
            // Assign college based on building name or other criteria
            // This is just an example - you would need to customize this logic
            // based on your actual building and college naming conventions
            foreach ($colleges as $college) {
                // Example: If building name contains college code or name, assign that college
                if (stripos($room->building, $college->college_code) !== false ||
                    stripos($room->building, $college->college_name) !== false) {
                    $room->college_code = $college->college_code;
                    $room->save();
                    break;
                }
            }
        }
        
        // For rooms that still don't have a college, assign them randomly
        $unassignedRooms = CtrRoom::whereNull('college_code')->get();
        foreach ($unassignedRooms as $room) {
            // Get a random college
            $randomCollege = $colleges->random();
            $room->college_code = $randomCollege->college_code;
            $room->save();
        }
        
        $this->command->info('Rooms have been assigned to colleges.');
    }
}
