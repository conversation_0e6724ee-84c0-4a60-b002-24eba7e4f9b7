<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-plus"></i>
        Add New Curriculum
        <small>{{ $college->college_name }} ({{ $college->college_code }})</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li class="active">Add New Curriculum</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Information</h3>
                </div>
                <form action="{{ route('collegeadmin.curriculum.store') }}" method="POST">
                    @csrf
                    <div class="box-body">
                        @if(Session::has('success'))
                            <div class="alert alert-success">
                                {{ Session::get('success') }}
                            </div>
                        @endif

                        @if(Session::has('error'))
                            <div class="alert alert-danger">
                                {{ Session::get('error') }}
                            </div>
                        @endif

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="program_code">Program</label>
                                    <select class="form-control" id="program_code" name="program_code" required>
                                        <option value="">Select Program</option>
                                        @foreach($programs as $program)
                                            <option value="{{ $program['program_code'] }}" {{ old('program_code') == $program['program_code'] ? 'selected' : '' }}>
                                                {{ $program['program_code'] }} - {{ $program['program_name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="curriculum_year">Curriculum Year</label>
                                    <input type="text" class="form-control" id="curriculum_year" name="curriculum_year" value="{{ old('curriculum_year') }}" required>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-12">
                                <h4>Subjects</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="subjects-table">
                                        <thead>
                                            <tr>
                                                <th>Course Code</th>
                                                <th>Course Name</th>
                                                <th>Lec</th>
                                                <th>Lab</th>
                                                <th>Units</th>
                                                <th>Level</th>
                                                <th>Period</th>
                                                <th>CompLab</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control" name="course_code[]" required>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="course_name[]" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control lec-input" name="lec[]" min="0" step="0.5" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control lab-input" name="lab[]" min="0" step="0.5" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control units-input" name="units[]" min="0" step="0.5" required readonly>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="level[]" required>
                                                        <option value="1">1st Year</option>
                                                        <option value="2">2nd Year</option>
                                                        <option value="3">3rd Year</option>
                                                        <option value="4">4th Year</option>
                                                        <option value="5">5th Year</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="period[]" required>
                                                        <option value="1">1st Semester</option>
                                                        <option value="2">2nd Semester</option>
                                                        <option value="3">Summer</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="complab[]" required>
                                                        <option value="0">No</option>
                                                        <option value="1">Yes</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm remove-subject">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-success" id="add-subject">
                                    <i class="fa fa-plus"></i> Add Subject
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="box-footer">
                        <button type="submit" class="btn btn-primary">Save Curriculum</button>
                        <a href="{{ route('collegeadmin.curriculum.index') }}" class="btn btn-default">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
$(function() {
    // Function to calculate units based on lec, lab, and complab values
    function calculateUnits(row) {
        var lecInput = row.find('.lec-input');
        var labInput = row.find('.lab-input');
        var complabSelect = row.find('select[name="complab[]"]');

        var lec = parseFloat(lecInput.val()) || 0;
        var lab = parseFloat(labInput.val()) || 0;
        var isComplab = complabSelect.val() === '1'; // Check if CompLab is set to "Yes"

        // Calculate total units based on CompLab status
        var units;
        if (isComplab) {
            // If CompLab is Yes: Units = Lec + 1
            units = lec + 1;
        } else {
            // If CompLab is No: Units = Lec + Lab
            units = lec + lab;
        }

        // Find the units input in the same row
        var unitsInput = row.find('.units-input');

        // Set the calculated value
        unitsInput.val(units.toFixed(1));
    }

    // Event handler for lec and lab inputs
    $(document).on('input', '.lec-input, .lab-input', function() {
        var row = $(this).closest('tr');
        calculateUnits(row);
    });

    // Event handler for complab select change
    $(document).on('change', 'select[name="complab[]"]', function() {
        var row = $(this).closest('tr');
        calculateUnits(row);
    });

    // Initialize units calculation for existing rows
    $('.lec-input').each(function() {
        var row = $(this).closest('tr');
        calculateUnits(row);
    });

    // Add new subject row
    $('#add-subject').click(function() {
        var newRow = `
            <tr>
                <td>
                    <input type="text" class="form-control" name="course_code[]" required>
                </td>
                <td>
                    <input type="text" class="form-control" name="course_name[]" required>
                </td>
                <td>
                    <input type="number" class="form-control lec-input" name="lec[]" min="0" step="0.5" required>
                </td>
                <td>
                    <input type="number" class="form-control lab-input" name="lab[]" min="0" step="0.5" required>
                </td>
                <td>
                    <input type="number" class="form-control units-input" name="units[]" min="0" step="0.5" required readonly>
                </td>
                <td>
                    <select class="form-control" name="level[]" required>
                        <option value="1">1st Year</option>
                        <option value="2">2nd Year</option>
                        <option value="3">3rd Year</option>
                        <option value="4">4th Year</option>
                        <option value="5">5th Year</option>
                    </select>
                </td>
                <td>
                    <select class="form-control" name="period[]" required>
                        <option value="1">1st Semester</option>
                        <option value="2">2nd Semester</option>
                        <option value="3">Summer</option>
                    </select>
                </td>
                <td>
                    <select class="form-control" name="complab[]" required>
                        <option value="0">No</option>
                        <option value="1">Yes</option>
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm remove-subject">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        $('#subjects-table tbody').append(newRow);
    });

    // Remove subject row
    $(document).on('click', '.remove-subject', function() {
        var rowCount = $('#subjects-table tbody tr').length;
        if (rowCount > 1) {
            $(this).closest('tr').remove();
        } else {
            alert('You cannot remove the last subject row.');
        }
    });
});
</script>
@endsection
