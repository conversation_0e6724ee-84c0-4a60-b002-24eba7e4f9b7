<?php

namespace App\Http\Controllers\Instructor\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Request;
use App\Events\LoadingNotification;

class FacultyLoadingAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    function get_offer_load()
    {
        if (Request::ajax()) {
            $offering_id = Input::get('offering_id');
            $schedules = \App\room_schedules::where('offering_id', $offering_id)
                ->where('instructor', Auth::user()->id)->get()->unique('offering_id');

            return view('instructor.faculty_loading.ajax.get_offer_load', compact('schedules', 'offering_id'));
        }
    }

    function accept_load()
    {
        if (Request::ajax()) {
            $offering_id = Input::get('offering_id');
            $reason = Input::get('reason');

            $schedules = \App\room_schedules::where('instructor', Auth::user()->id)
                ->where('offering_id', $offering_id)->get();
            if (!empty($schedules)) {
                foreach ($schedules as $schedule) {
                    $schedule->is_loaded = 1;
                    $schedule->update();
                }
            }

            $user = \App\User::find(Auth::user()->id);

            $notification = new \App\LoadNotification;
            $notification->date_time = date('Y-m-d H:i:s');
            $notification->content = "Instructor " . strtoupper($user->lastname) . ', ' . strtoupper($user->name) . ' have seen the faculty load. Remarks: ' . $reason;
            $notification->save();

            $content = "Instructor " . strtoupper($user->lastname) . ', ' . strtoupper($user->name) . ' have seen the faculty load. Remarks: ' . $reason;

            event(new LoadingNotification($content));
        }
    }

    /*function reject_offer(){
        if(Request::ajax()){
          $offering_id = Input::get('offering_id');
          $reason = Input::get('reason');

          $schedules = \App\room_schedules::where('instructor',Auth::user()->id)
                   ->where('offering_id',$offering_id)->get();
           if(!empty($schedules)){
               foreach($schedules as $schedule){
                   $schedule->is_loaded = 0;
                   $schedule->instructor = NULL;
                   $schedule->update();
               }
           }

           $user = \App\User::find(Auth::user()->id);

           $notification = new \App\LoadNotification;
           $notification->date_time = date('Y-m-d H:i:s');
           $notification->content = "Instructor ".strtoupper($user->lastname).', '.strtoupper($user->name).' rejected the faculty load suggested by the Admin. Due to '.$reason;
           $notification->save();

           $content = "Instructor ".strtoupper($user->lastname).', '.strtoupper($user->name).' rejected the faculty load suggested by the Admin. Due to '.$reason;

           event(new LoadingNotification($content));
        }
    } */

    function reloadtabular()
    {
        if (Request::ajax()) {
            // Get the instructor's college code
            $user = Auth::user();
            $collegeCode = $user->college_code;

            // If college_code is not set in user table, try to get it from instructor_infos
            if (empty($collegeCode)) {
                $instructorInfo = \App\instructors_infos::where('instructor_id', $user->id)->first();
                $collegeCode = $instructorInfo ? $instructorInfo->college : null;
            }

            // Get schedules for this instructor
            $query = \App\room_schedules::distinct()
                ->where('is_active', 1)
                ->where('instructor', $user->id);

            // If college code is available, filter by college
            if (!empty($collegeCode)) {
                $query = $query->whereExists(function ($subquery) use ($collegeCode) {
                    $subquery->select(DB::raw(1))
                        ->from('offerings_infos')
                        ->join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
                        ->whereRaw('offerings_infos.id = room_schedules.offering_id')
                        ->where('curricula.college_code', $collegeCode);
                });
            }

            $tabular_schedules = $query->get(['offering_id', 'is_loaded']);

            return view('instructor.faculty_loading.ajax.reloadtabular', compact('tabular_schedules'));
        }
    }

    function reloadcalendar()
    {
        // Get the instructor's college code
        $user = Auth::user();
        $collegeCode = $user->college_code;

        // If college_code is not set in user table, try to get it from instructor_infos
        if (empty($collegeCode)) {
            $instructorInfo = \App\instructors_infos::where('instructor_id', $user->id)->first();
            $collegeCode = $instructorInfo ? $instructorInfo->college : null;
        }

        // Get schedules for this instructor
        $schedules = \App\room_schedules::where('is_active', 1)
            ->where('instructor', $user->id);

        // If college code is available, filter by college
        if (!empty($collegeCode)) {
            $schedules = $schedules->whereExists(function ($query) use ($collegeCode) {
                $query->select(DB::raw(1))
                    ->from('offerings_infos')
                    ->join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
                    ->whereRaw('offerings_infos.id = room_schedules.offering_id')
                    ->where('curricula.college_code', $collegeCode);
            });
        }

        $schedules = $schedules->get();

        if (!$schedules->isEmpty()) {
            foreach ($schedules as $sched) {
                $offering = \App\offerings_infos_table::with('curriculum')->find($sched->offering_id);
                $curriculum = $offering ? $offering->curriculum : null;
                $course_code = $curriculum ? $curriculum->control_code : 'N/A';
                $section_name = $offering ? $offering->section_name : 'N/A';

                if ($sched->day == 'M') {
                    $day = 'Monday';
                } else if ($sched->day == 'T') {
                    $day = 'Tuesday';
                } else if ($sched->day == 'W') {
                    $day = 'Wednesday';
                } else if ($sched->day == 'Th') {
                    $day = 'Thursday';
                } else if ($sched->day == 'F') {
                    $day = 'Friday';
                } else if ($sched->day == 'Sa') {
                    $day = 'Saturday';
                } else if ($sched->day == 'Su') {
                    $day = 'Sunday';
                }

                if ($sched->is_loaded == 1) {
                    $color = "lighblue";
                } else {
                    $color = "lightsalmon";
                }

                $event_array[] = array(
                    'id' => $sched->id,
                    'title' => $course_code . '<br>' . $sched->room . '<br>' . $section_name,
                    'start' => date('Y-m-d', strtotime($day . ' this week')) . 'T' . $sched->time_starts,
                    'end' => date('Y-m-d', strtotime($day . ' this week')) . 'T' . $sched->time_end,
                    'color' => $color,
                    "textEscape" => 'false',
                    'textColor' => 'black',
                    'offering_id' => $sched->offering_id
                );
            }
            $get_schedule = json_encode($event_array);
        } else {
            $get_schedule = NULL;
        }

        return $get_schedule;
    }
}
