<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-archive"></i>
        Archived Subjects
        <small>{{ Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li class="active">Archived Subjects</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Archived Curriculum Subjects</h3>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($subjects) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Program</th>
                                        <th>Curriculum Year</th>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Level</th>
                                        <th>Period</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($subjects as $subject)
                                        <tr>
                                            <td>{{ $subject->program_code }}</td>
                                            <td>{{ $subject->curriculum_year }}</td>
                                            <td>{{ $subject->course_code }}</td>
                                            <td>{{ $subject->course_name }}</td>
                                            <td>{{ $subject->level }}</td>
                                            <td>{{ $subject->period }}</td>
                                            <td>
                                                <a href="{{ route('collegeadmin.curriculum.restore_subject', $subject->id) }}" class="btn btn-success btn-sm" onclick="return confirm('Are you sure you want to restore this subject?')">
                                                    <i class="fa fa-undo"></i> Restore
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No archived subjects found.
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.curriculum.index') }}" class="btn btn-default">Back to Curriculum</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
