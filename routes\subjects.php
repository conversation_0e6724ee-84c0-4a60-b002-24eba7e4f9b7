<?php

// Admin Subject Routes
Route::group(['prefix' => 'admin', 'middleware' => ['auth', 'admin']], function () {
    Route::get('/subjects', 'Admin\SubjectController@index')->name('admin.subjects.index');
    Route::get('/subjects/create', 'Admin\SubjectController@create')->name('admin.subjects.create');
    Route::post('/subjects', 'Admin\SubjectController@store')->name('admin.subjects.store');
    Route::get('/subjects/{id}/edit', 'Admin\SubjectController@edit')->name('admin.subjects.edit');
    Route::put('/subjects/{id}', 'Admin\SubjectController@update')->name('admin.subjects.update');
});

// SuperAdmin Subject Routes
Route::group(['prefix' => 'superadmin', 'middleware' => ['auth', 'superadmin']], function () {
    Route::get('/subjects', 'SuperAdmin\SubjectController@index')->name('superadmin.subjects.index');
    Route::get('/subjects/create', 'SuperAdmin\SubjectController@create')->name('superadmin.subjects.create');
    Route::post('/subjects', 'SuperAdmin\SubjectController@store')->name('superadmin.subjects.store');
    Route::get('/subjects/{id}/edit', 'SuperAdmin\SubjectController@edit')->name('superadmin.subjects.edit');
    Route::put('/subjects/{id}', 'SuperAdmin\SubjectController@update')->name('superadmin.subjects.update');
});
