<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckCourseNameColumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if course_name column exists in curricula table
        if (Schema::hasColumn('curricula', 'course_name')) {
            Log::info('course_name column exists in curricula table');
        } else {
            Log::info('course_name column does NOT exist in curricula table');
            
            // Add the column if it doesn't exist
            Schema::table('curricula', function (Blueprint $table) {
                $table->string('course_name')->nullable()->after('course_code');
            });
            
            Log::info('Added course_name column to curricula table');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Do nothing in down migration
    }
}
