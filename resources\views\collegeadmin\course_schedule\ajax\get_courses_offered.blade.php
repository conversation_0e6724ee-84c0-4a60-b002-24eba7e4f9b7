@if($offerings->count() > 0)
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">
                <i class="fa fa-book"></i> Courses for Section: {{ $section_name }}
            </h3>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-success btn-sm" onclick="viewTabularSchedule('{{ $section_name }}')">
                    <i class="fa fa-table"></i> View Schedule
                </button>
            </div>
        </div>
        <div class="box-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th width="15%">Course Code</th>
                            <th width="35%">Course Name</th>
                            <th width="10%">Units</th>
                            <th width="15%">Type</th>
                            <th width="15%">Status</th>
                            <th width="10%">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($offerings as $offering)
                            @php
                                $curriculum = $offering->curriculum;
                                $isScheduled = $offering->schedules->count() > 0;
                                $scheduleCount = $offering->schedules->count();
                            @endphp
                            <tr class="{{ $isScheduled ? 'success' : '' }}">
                                <td>
                                    <strong>
                                        @if(isset($curriculum->course_code))
                                            {{ $curriculum->course_code }}
                                        @elseif(isset($curriculum->control_code))
                                            {{ $curriculum->control_code }}
                                        @else
                                            {{ $curriculum->subject_code }}
                                        @endif
                                    </strong>
                                </td>
                                <td>{{ $curriculum->subject_name }}</td>
                                <td>
                                    <span class="badge badge-info">{{ $curriculum->units }}</span>
                                </td>
                                <td>
                                    @if($curriculum->is_complab)
                                        <span class="label label-warning">
                                            <i class="fa fa-desktop"></i> Computer Lab
                                        </span>
                                    @else
                                        <span class="label label-primary">
                                            <i class="fa fa-book"></i> Regular
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    @if($isScheduled)
                                        <span class="label label-success">
                                            <i class="fa fa-check"></i> Scheduled ({{ $scheduleCount }})
                                        </span>
                                    @else
                                        <span class="label label-danger">
                                            <i class="fa fa-times"></i> Not Scheduled
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('collegeadmin.course_scheduling.add_schedule', [$offering->id, $section_name]) }}" 
                                       class="btn btn-primary btn-xs" 
                                       title="{{ $isScheduled ? 'Manage Schedule' : 'Add Schedule' }}">
                                        <i class="fa {{ $isScheduled ? 'fa-edit' : 'fa-plus' }}"></i>
                                        {{ $isScheduled ? 'Manage' : 'Schedule' }}
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        <div class="box-footer">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted">
                        <i class="fa fa-info-circle"></i> 
                        Total Courses: {{ $offerings->count() }} | 
                        Scheduled: {{ $offerings->filter(function($o) { return $o->schedules->count() > 0; })->count() }} | 
                        Unscheduled: {{ $offerings->filter(function($o) { return $o->schedules->count() == 0; })->count() }}
                    </p>
                </div>
                <div class="col-md-6 text-right">
                    <button type="button" class="btn btn-warning btn-sm" onclick="generateScheduleForSection('{{ $section_name }}')">
                        <i class="fa fa-magic"></i> Auto-Generate Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function generateScheduleForSection(sectionName) {
            if (confirm('This will automatically generate schedules for all unscheduled courses in this section. Continue?')) {
                // Show loading indicator
                var $btn = $('button[onclick="generateScheduleForSection(\'' + sectionName + '\')"]');
                var originalHtml = $btn.html();
                $btn.html('<i class="fa fa-spinner fa-spin"></i> Generating...').prop('disabled', true);

                $.ajax({
                    url: "{{ route('collegeadmin.course_scheduling.generate_schedule') }}",
                    type: 'POST',
                    data: {
                        section_name: sectionName,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        // Reload the courses to show updated status
                        getcoursesoffered(sectionName);
                        toastr.success('Schedule generation completed!');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error generating schedule:', error);
                        toastr.error('Failed to generate schedule. Please try again.');
                        $btn.html(originalHtml).prop('disabled', false);
                    }
                });
            }
        }
    </script>
@else
    <div class="alert alert-info">
        <h4><i class="icon fa fa-info"></i> No Courses Found</h4>
        <p>No courses are available for section <strong>{{ $section_name }}</strong>.</p>
        <p>This could mean:</p>
        <ul>
            <li>The section has no curriculum assigned</li>
            <li>The courses are not offered for this semester</li>
            <li>There are no courses configured for this program and level</li>
        </ul>
    </div>
@endif
