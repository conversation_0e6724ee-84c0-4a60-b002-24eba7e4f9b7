<?php

namespace App\Http\Controllers\CollegeAdmin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\College;
use App\CtrRoom;
use App\curriculum;
use App\User;
use App\room_schedules;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    public function index()
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;
        
        // Get college information
        $college = College::where('college_code', $collegeCode)->first();
        
        // Get faculty count for this college
        $facultyCount = User::where('accesslevel', 1)
            ->where('college_code', $collegeCode)
            ->count();
        
        // Get rooms count for this college
        $roomsCount = CtrRoom::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->count();
        
        // Get curriculum count for this college
        $curriculumCount = curriculum::where('college_code', $collegeCode)
            ->where('is_active', 1)
            ->distinct('program_code')
            ->count('program_code');
        
        // Get active schedules count for this college
        $schedulesCount = room_schedules::join('offerings_infos', 'room_schedules.offering_id', '=', 'offerings_infos.id')
            ->join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
            ->where('curricula.college_code', $collegeCode)
            ->where('room_schedules.is_active', 1)
            ->count();
        
        return view('collegeadmin.dashboard', compact(
            'college',
            'facultyCount',
            'roomsCount',
            'curriculumCount',
            'schedulesCount'
        ));
    }
}
