@if(count($schedules) > 0)
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Course</th>
                    <th>Schedule</th>
                    <th>Room</th>
                </tr>
            </thead>
            <tbody>
                @foreach($schedules as $schedule)
                    @php
                        $offering = App\offerings_infos::join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
                            ->where('offerings_infos.id', $schedule->offering_id)
                            ->select('curricula.course_code', 'curricula.course_name')
                            ->first();
                    @endphp
                    
                    <tr>
                        <td>{{ $offering ? $offering->course_code : 'Unknown' }}</td>
                        <td>
                            {{ $schedule->sched_day }} 
                            {{ date('h:i A', strtotime($schedule->sched_from)) }} - 
                            {{ date('h:i A', strtotime($schedule->sched_to)) }}
                        </td>
                        <td>{{ $schedule->room }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info">
        No teaching load assigned yet.
    </div>
@endif
