<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProgramName extends Model
{
    protected $table = 'program_names';

    protected $fillable = [
        'program_code',
        'program_name'
    ];

    /**
     * Get program name by code
     *
     * @param string $programCode
     * @return string|null
     */
    public static function getNameByCode($programCode)
    {
        $program = self::where('program_code', $programCode)->first();
        return $program ? $program->program_name : null;
    }

    /**
     * Get all program codes and names as an associative array
     *
     * @return array
     */
    public static function getAllAsArray()
    {
        return self::pluck('program_name', 'program_code')->toArray();
    }
}
