<?php use Illuminate\Support\Facades\Schema; ?>
@if(!$sections->isEmpty())
    @foreach($sections as $section)
    <tr>
        @if(Schema::hasColumn('ctr_sections', 'college_code'))
        <td>{{$section->college_code ?? 'N/A'}}</td>
        @endif
        <td>{{$section->program_code}}</td>
        <td>{{$section->level}}</td>
        <td>{{$section->section_name}}</td>
        <td>
            @if($section->is_active == 1)
            <label class='label label-success'>Active</label>
            @else
            <label class='label label-danger'>Inactive</label>
            @endif
        </td>
        <td>
            <a href="{{url('/admin/section_management/archive',[$section->id])}}" class="btn btn-flat btn-success" title="Restore Section" onclick="return confirm('Do you wish to restore this section?')"><i class="fa fa-undo"></i> Restore</a>
        </td>
    </tr>
    @endforeach
@else
    <tr>
        <td colspan="{{ Schema::hasColumn('ctr_sections', 'college_code') ? 6 : 5 }}" class="text-center">No archived sections found matching your search criteria</td>
    </tr>
@endif
