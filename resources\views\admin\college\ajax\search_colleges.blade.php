@if(!$colleges->isEmpty())
    @foreach($colleges as $college)
    <tr>
        <td><strong>{{ $college->college_code }}</strong></td>
        <td><strong>{{ $college->college_name }}</strong></td>
        <td>
            @php
                $programs = \App\Program::where('college_id', $college->id)->get();
            @endphp
            @if(count($programs) > 0)
                <ul class="list-unstyled">
                    @foreach($programs as $program)
                        <li><span class="label label-info">{{ $program->program_code }} @if(!empty($program->program_name)) - {{ $program->program_name }} @endif</span></li>
                    @endforeach
                </ul>
            @else
                <span class="text-muted">No programs specified</span>
            @endif
        </td>
        <td>
            <button data-toggle="modal" data-target="#myModal" onclick="editcollege('{{ $college->id }}')" title="Edit Record" class="btn btn-flat btn-primary"><i class="fa fa-pencil"></i></button>
            <a href="{{ url('/admin/college/courses', [$college->id]) }}" class="btn btn-flat btn-success" title="Manage Courses"><i class="fa fa-book"></i></a>
            <a href="{{ url('/admin/college/archive', [$college->id]) }}" class="btn btn-flat btn-danger" title="Change to Inactive Status?" onclick="return confirm('Do you wish to archive the Record?')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    @endforeach
@else
    <tr>
        <td colspan="4" class="text-center">No colleges found matching your search criteria.</td>
    </tr>
@endif
