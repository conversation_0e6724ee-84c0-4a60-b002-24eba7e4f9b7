<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}

?>

@extends($layout)

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-danger">
                <div class="box-header">
                    <h5 class="box-title">Archived Colleges</h5>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/superadmin/college') }}" class="btn btn-flat btn-success"><i class="fa fa-check-circle"></i> Active Colleges</a>
                    </div>
                </div>
                <hr>
                <div class="box-body">
                    <!-- Search and Filter Section -->
                    <div class="row" style="margin-bottom: 15px;">
                        <div class="col-md-12">
                            <div class="box box-solid box-danger">
                                <div class="box-header with-border">
                                    <h3 class="box-title"><i class="fa fa-search"></i> Search & Filter</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>College Code</label>
                                                <input type="text" class="form-control" id="search-code" placeholder="Search by college code">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>College Name</label>
                                                <input type="text" class="form-control" id="search-name" placeholder="Search by college name">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>Courses</label>
                                                <input type="text" class="form-control" id="search-description" placeholder="Search by courses">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <button type="button" id="btn-search" class="btn btn-primary btn-flat"><i class="fa fa-search"></i> Search</button>
                                            <button type="button" id="btn-reset" class="btn btn-default btn-flat"><i class="fa fa-refresh"></i> Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Search and Filter Section -->

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="colleges-table">
                            <thead>
                                <tr>
                                    <th width='15%'>College Code</th>
                                    <th width='25%'>College Name</th>
                                    <th width='40%'>Courses</th>
                                    <th width='20%'>Action</th>
                                </tr>
                            </thead>
                            <tbody id="colleges-table-body">
                                @if(!$colleges->isEmpty())
                                @foreach($colleges as $college)
                                <tr>
                                    <td>{{ $college->college_code }}</td>
                                    <td>{{ $college->college_name }}</td>
                                    <td>
                                        @if(count($college->courses_array) > 0)
                                            <ul class="list-unstyled">
                                                @foreach($college->courses_array as $course)
                                                    <li><span class="label label-info">{{ $course }}</span></li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <span class="text-muted">No courses specified</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ url('/superadmin/college/archive', [$college->id]) }}" class="btn btn-flat btn-success" title="Change to Active Status?" onclick="return confirm('Do you wish to restore the Record?')"><i class="fa fa-recycle"></i></a>
                                    </td>
                                </tr>
                                @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer-script')
<link rel='stylesheet' href='{{ asset('plugins/select2/select2.css') }}'>
<script src='{{ asset('plugins/select2/select2.js') }}'></script>
<script>
// Initialize Select2 Elements
$(document).ready(function() {
    // Search button click event
    $('#btn-search').click(function() {
        searchColleges();
    });

    // Reset button click event
    $('#btn-reset').click(function() {
        $('#search-code').val('');
        $('#search-name').val('');
        $('#search-description').val('');
        searchColleges();
    });

    // Enter key press event for search inputs
    $('#search-code, #search-name, #search-description').keypress(function(e) {
        if(e.which == 13) { // Enter key
            searchColleges();
        }
    });

    // Function to perform the search
    function searchColleges() {
        var code = $('#search-code').val();
        var name = $('#search-name').val();
        var description = $('#search-description').val();

        $.ajax({
            type: "GET",
            url: "/ajax/superadmin/college/search_archive",
            data: {
                code: code,
                name: name,
                description: description
            },
            success: function(data) {
                $('#colleges-table-body').html(data);
            },
            error: function(xhr, status, error) {
                console.error("Error searching colleges:", error);
                toastr.error("An error occurred while searching. Please try again.");
            }
        });
    }
});
</script>
@endsection
