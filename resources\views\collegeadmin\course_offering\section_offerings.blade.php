<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOS - Course Offerings for ' . $section->section_name)

@section('main-content')
<section class="content-header">
    <h1>
        Course Offerings for {{ $section->section_name }}
        <small>{{ $section->program_code }} - Level {{ $section->level }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.course_offering.index') }}">Course Offerings</a></li>
        <li><a href="{{ route('collegeadmin.course_offering.program', $section->program_code) }}">{{ $section->program_code }}</a></li>
        <li class="active">{{ $section->section_name }}</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Offered Subjects</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.course_offering.add_all', $section->id) }}" class="btn btn-success btn-sm">
                            <i class="fa fa-plus"></i> Add All Available Subjects
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($offerings) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                        <th>Semester</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($offerings as $offering)
                                        @php
                                            $curriculum = App\curriculum::find($offering->curriculum_id);
                                        @endphp
                                        @if($curriculum)
                                            <tr>
                                                <td>{{ $curriculum->control_code }}</td>
                                                <td>{{ $curriculum->course_name }}</td>
                                                <td>{{ $curriculum->lec }}</td>
                                                <td>{{ $curriculum->lab }}</td>
                                                <td>{{ $curriculum->units }}</td>
                                                <td>
                                                    <form action="{{ route('collegeadmin.course_offering.update_semester', [$section->id, $offering->id]) }}" method="POST" class="form-inline semester-form">
                                                        @csrf
                                                        @method('PATCH')
                                                        <select name="semester" class="form-control input-sm semester-select" data-offering-id="{{ $offering->id }}">
                                                            @foreach($semesterOptions as $value => $label)
                                                                <option value="{{ $value }}" {{ $curriculum->period == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                            @endforeach
                                                        </select>
                                                        <button type="submit" class="btn btn-primary btn-sm update-semester-btn" style="display: none;">
                                                            <i class="fa fa-save"></i> Save
                                                        </button>
                                                    </form>
                                                </td>
                                                <td>
                                                    <a href="{{ route('collegeadmin.course_offering.remove', [$section->id, $offering->id]) }}" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to remove this subject?')">
                                                        <i class="fa fa-trash"></i> Remove
                                                    </a>
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No subjects offered for this section.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Available Subjects</h3>
                </div>
                <div class="box-body">
                    @if(count($availableCurricula) > 0)
                        <form action="{{ route('collegeadmin.course_offering.add_selected', $section->id) }}" method="POST">
                            @csrf
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th width="5%"><input type="checkbox" id="select-all"></th>
                                            <th>Course Code</th>
                                            <th>Course Name</th>
                                            <th>Lec</th>
                                            <th>Lab</th>
                                            <th>Units</th>
                                            <th>Semester</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($availableCurricula as $curriculum)
                                            <tr>
                                                <td><input type="checkbox" name="curriculum_ids[]" value="{{ $curriculum->id }}"></td>
                                                <td>{{ $curriculum->control_code }}</td>
                                                <td>{{ $curriculum->course_name }}</td>
                                                <td>{{ $curriculum->lec }}</td>
                                                <td>{{ $curriculum->lab }}</td>
                                                <td>{{ $curriculum->units }}</td>
                                                <td>{{ $curriculum->period }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Selected Subjects</button>
                        </form>
                    @else
                        <div class="alert alert-info">
                            No available subjects to add for this section.
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.course_offering.program', $section->program_code) }}" class="btn btn-default">Back to Program</a>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    $(document).ready(function() {
        // Select all checkboxes
        $('#select-all').click(function() {
            $('input[name="curriculum_ids[]"]').prop('checked', this.checked);
        });

        // Show save button when semester is changed
        $('.semester-select').change(function() {
            $(this).closest('form').find('.update-semester-btn').show();
        });

        // Auto-submit form when semester is changed
        $('.semester-select').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
@endpush
@endsection
