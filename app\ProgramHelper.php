<?php

namespace App;

use App\College;
use App\Program;

class ProgramHelper
{
    /**
     * Get a program by its code
     *
     * @param string $programCode
     * @return object|null
     */
    public static function getProgramByCode($programCode)
    {
        // Find the program in the programs table
        $program = Program::where('program_code', $programCode)->first();

        if ($program) {
            // Create a compatible object with the same structure as academic_programs
            return (object) [
                'program_code' => $program->program_code,
                'program_name' => $program->program_name,
                'college_code' => $program->college ? $program->college->college_code : null,
                'college_id' => $program->college_id
            ];
        }

        // If not found, try to get from academic_programs table (for backward compatibility)
        $academicProgram = \App\academic_programs::where('program_code', $programCode)->first();

        if ($academicProgram) {
            return (object) [
                'program_code' => $academicProgram->program_code,
                'program_name' => $academicProgram->program_name,
                'college_code' => $academicProgram->college_code,
                'college_id' => $academicProgram->college ? $academicProgram->college->id : null
            ];
        }

        // If not found, check if it exists in the curriculum table
        $curriculum = \App\curriculum::where('program_code', $programCode)->first();

        if ($curriculum) {
            return (object) [
                'program_code' => $curriculum->program_code,
                'program_name' => $curriculum->program_name,
                'college_code' => $curriculum->college_code,
                'college_id' => $curriculum->college ? $curriculum->college->id : null
            ];
        }

        // If still not found, try to get from program_names table
        $programName = \DB::table('program_names')
            ->where('program_code', $programCode)
            ->first();

        if ($programName) {
            // Create a basic program object with the code and name
            return (object) [
                'program_code' => $programCode,
                'program_name' => $programName->program_name,
                'college_code' => null,
                'college_id' => null
            ];
        }

        // If still not found but a valid program code was provided, create a basic object
        if (!empty($programCode)) {
            return (object) [
                'program_code' => $programCode,
                'program_name' => $programCode,
                'college_code' => null,
                'college_id' => null
            ];
        }

        return null;
    }

    /**
     * Get all programs
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getAllPrograms()
    {
        // Get all programs from the programs table
        $programs = Program::all();

        // If no programs found, try to get from academic_programs table (for backward compatibility)
        if ($programs->isEmpty()) {
            $academicPrograms = \App\academic_programs::all();

            // Convert to a collection of objects with the same structure
            return $academicPrograms->map(function ($program) {
                return (object) [
                    'program_code' => $program->program_code,
                    'program_name' => $program->program_name,
                    'college_code' => $program->college_code,
                    'college_id' => $program->college ? $program->college->id : null
                ];
            });
        }

        // Convert to a collection of objects with the same structure as academic_programs
        return $programs->map(function ($program) {
            return (object) [
                'program_code' => $program->program_code,
                'program_name' => $program->program_name,
                'college_code' => $program->college ? $program->college->college_code : null,
                'college_id' => $program->college_id
            ];
        });
    }

    /**
     * Get programs by college code
     *
     * @param string $collegeCode
     * @return \Illuminate\Support\Collection
     */
    public static function getProgramsByCollegeCode($collegeCode)
    {
        // If no college code provided, return all programs
        if (empty($collegeCode)) {
            return self::getAllPrograms();
        }

        // Find the college
        $college = College::where('college_code', $collegeCode)->first();

        if (!$college) {
            return collect([]);
        }

        // Get programs for this college
        $programs = Program::where('college_id', $college->id)->get();

        // If no programs found, try to get from academic_programs table (for backward compatibility)
        if ($programs->isEmpty()) {
            $academicPrograms = \App\academic_programs::where('college_code', $collegeCode)->get();

            // If still no programs found, try to get from the college's program_codes field
            if ($academicPrograms->isEmpty() && method_exists($college, 'getProgramCodes')) {
                $programCodes = $college->getProgramCodes();

                // Create basic entries with code as name
                $result = collect();
                foreach ($programCodes as $code) {
                    $result->push((object) [
                        'program_code' => $code,
                        'program_name' => $code,
                        'college_code' => $collegeCode,
                        'college_id' => $college->id
                    ]);
                }

                return $result;
            }

            // Convert academic_programs to a collection of objects with the same structure
            return $academicPrograms->map(function ($program) {
                return (object) [
                    'program_code' => $program->program_code,
                    'program_name' => $program->program_name,
                    'college_code' => $program->college_code,
                    'college_id' => $program->college ? $program->college->id : null
                ];
            });
        }

        // Convert to a collection of objects with the same structure as academic_programs
        return $programs->map(function ($program) {
            return (object) [
                'program_code' => $program->program_code,
                'program_name' => $program->program_name,
                'college_code' => $program->college ? $program->college->college_code : null,
                'college_id' => $program->college_id
            ];
        });
    }

    /**
     * Create a new program
     *
     * @param array $data
     * @return object
     */
    public static function createProgram($data)
    {
        // Find the college
        $college = null;

        if (isset($data['college_code'])) {
            $college = College::where('college_code', $data['college_code'])->first();
        }

        if (!$college) {
            // Create or find the unassigned college
            $college = College::firstOrCreate(
                ['college_code' => 'UNASSIGNED'],
                [
                    'college_name' => 'Unassigned College',
                    'is_active' => 1
                ]
            );
        }

        // Create the program
        $program = Program::firstOrCreate(
            [
                'college_id' => $college->id,
                'program_code' => $data['program_code']
            ],
            [
                'program_name' => $data['program_name'] ?? $data['program_code']
            ]
        );

        // Return a compatible object
        return (object) [
            'program_code' => $program->program_code,
            'program_name' => $program->program_name,
            'college_code' => $college->college_code,
            'college_id' => $college->id
        ];
    }
}
