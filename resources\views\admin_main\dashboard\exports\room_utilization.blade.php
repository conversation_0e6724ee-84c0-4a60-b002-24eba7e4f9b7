<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Room Utilization Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin-bottom: 5px;
        }
        .date {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
        }
        .high-utilization {
            background-color: #d4edda;
        }
        .medium-utilization {
            background-color: #fff3cd;
        }
        .low-utilization {
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Room Utilization Report</h1>
        <p>Generated on: {{ $date }}</p>
    </div>
    
    <div class="date">
        <p><strong>Report Summary:</strong> This report shows the utilization rate of all active rooms in the system. Utilization rate is calculated based on the total scheduled hours divided by the maximum available hours (72 hours per week).</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>#</th>
                <th>Room</th>
                <th>Building</th>
                <th>Department</th>
                <th>Total Hours</th>
                <th>Utilization Rate</th>
            </tr>
        </thead>
        <tbody>
            @foreach($utilization as $index => $room)
                @php
                    $class = '';
                    if ($room['utilization_rate'] >= 70) {
                        $class = 'high-utilization';
                    } elseif ($room['utilization_rate'] >= 40) {
                        $class = 'medium-utilization';
                    } else {
                        $class = 'low-utilization';
                    }
                @endphp
                <tr class="{{ $class }}">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $room['room'] }}</td>
                    <td>{{ $room['building'] }}</td>
                    <td>{{ $room['college_code'] ?? 'N/A' }}</td>
                    <td>{{ $room['total_hours'] }}</td>
                    <td>{{ $room['utilization_rate'] }}%</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="footer">
        <p>This is an automatically generated report. For any questions, please contact the system administrator.</p>
    </div>
</body>
</html>
