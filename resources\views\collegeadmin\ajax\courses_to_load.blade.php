@if(count($offerings) > 0)
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Course Code</th>
                    <th>Course Name</th>
                    <th>Schedule</th>
                    <th>Room</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($offerings as $offering)
                    @php
                        $schedule = App\room_schedules::where('offering_id', $offering->id)
                            ->where('is_active', 1)
                            ->first();
                    @endphp
                    
                    @if($schedule)
                        <tr>
                            <td>{{ $offering->course_code }}</td>
                            <td>{{ $offering->course_name }}</td>
                            <td>
                                {{ $schedule->sched_day }} 
                                {{ date('h:i A', strtotime($schedule->sched_from)) }} - 
                                {{ date('h:i A', strtotime($schedule->sched_to)) }}
                            </td>
                            <td>{{ $schedule->room }}</td>
                            <td>
                                <button type="button" class="btn btn-primary btn-sm add-to-load" data-offering="{{ $offering->id }}">
                                    <i class="fa fa-plus"></i> Add to Load
                                </button>
                            </td>
                        </tr>
                    @endif
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info">
        No available courses found.
    </div>
@endif
