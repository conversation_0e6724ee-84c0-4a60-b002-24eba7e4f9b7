<?php

// <PERSON><PERSON>'s bootstrap file
require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/bootstrap/app.php';

// Get the application instance
$app = app();

// Boot the application
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Use the DB facade
use Illuminate\Support\Facades\DB;

echo "Starting migration using direct SQL...\n";

try {
    // 1. Create subjects table if it doesn't exist
    $tableExists = DB::select("SHOW TABLES LIKE 'subjects'");
    if (empty($tableExists)) {
        echo "Creating subjects table...\n";
        DB::statement("
            CREATE TABLE `subjects` (
                `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                `subject_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `subject_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `lec` decimal(5,2) NOT NULL DEFAULT '0.00',
                `lab` decimal(5,2) NOT NULL DEFAULT '0.00',
                `units` decimal(5,2) NOT NULL DEFAULT '0.00',
                `is_complab` tinyint(1) NOT NULL DEFAULT '0',
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                UNIQUE KEY `subjects_subject_code_unique` (`subject_code`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "Extracting unique subjects from curricula...\n";
        // Extract unique subjects from curricula
        $subjects = DB::select("
            SELECT DISTINCT subject_code, subject_name, lec, lab, units, is_complab
            FROM curricula
        ");
        
        echo "Inserting " . count($subjects) . " subjects...\n";
        $insertedCodes = [];
        foreach ($subjects as $subject) {
            // Skip if we've already inserted this subject code
            if (in_array($subject->subject_code, $insertedCodes)) {
                echo "Skipping duplicate subject code: " . $subject->subject_code . "\n";
                continue;
            }
            
            try {
                DB::insert("
                    INSERT INTO subjects (subject_code, subject_name, lec, lab, units, is_complab, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                ", [
                    $subject->subject_code,
                    $subject->subject_name,
                    $subject->lec,
                    $subject->lab,
                    $subject->units,
                    $subject->is_complab
                ]);
                $insertedCodes[] = $subject->subject_code;
            } catch (Exception $e) {
                echo "Error inserting subject " . $subject->subject_code . ": " . $e->getMessage() . "\n";
            }
        }
        echo "Subjects table created and populated.\n";
    } else {
        echo "Subjects table already exists.\n";
    }
    
    // 2. Modify curricula table to reference subjects
    $columnExists = DB::select("SHOW COLUMNS FROM curricula LIKE 'subject_id'");
    if (empty($columnExists)) {
        echo "Adding subject_id column to curricula table...\n";
        DB::statement("
            ALTER TABLE curricula
            ADD COLUMN subject_id int(10) UNSIGNED NULL AFTER subject_code,
            ADD CONSTRAINT curricula_subject_id_foreign FOREIGN KEY (subject_id) REFERENCES subjects(id)
        ");
        
        echo "Updating curricula with subject_id references...\n";
        // Update curricula with subject_id references
        $curricula = DB::select("SELECT id, subject_code FROM curricula");
        foreach ($curricula as $curriculum) {
            $subject = DB::select("
                SELECT id FROM subjects
                WHERE subject_code = ?
            ", [$curriculum->subject_code]);
            
            if (!empty($subject)) {
                DB::update("
                    UPDATE curricula
                    SET subject_id = ?
                    WHERE id = ?
                ", [$subject[0]->id, $curriculum->id]);
            }
        }
        echo "Curricula table updated with subject_id references.\n";
    } else {
        echo "subject_id column already exists in curricula table.\n";
    }
    
    // Update the migrations table to mark this migration as run
    $migrationExists = DB::select("
        SELECT * FROM migrations
        WHERE migration = '2025_06_01_000000_normalize_curriculum_data'
    ");
    
    if (empty($migrationExists)) {
        $maxBatch = DB::select("SELECT MAX(batch) as max_batch FROM migrations")[0]->max_batch;
        $newBatch = $maxBatch + 1;
        
        DB::insert("
            INSERT INTO migrations (migration, batch)
            VALUES (?, ?)
        ", ['2025_06_01_000000_normalize_curriculum_data', $newBatch]);
        
        echo "Migration marked as run (batch " . $newBatch . ").\n";
    } else {
        echo "Migration already marked as run.\n";
    }
    
    echo "Migration completed successfully.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
