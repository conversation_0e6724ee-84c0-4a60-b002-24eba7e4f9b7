<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class College extends Model
{
    protected $table = 'colleges';

    protected $fillable = [
        'college_code',
        'college_name',
        'is_active'
    ];

    protected $appends = ['courses_array'];

    /**
     * Get all programs associated with this college
     *
     * @return array
     */
    public function getPrograms()
    {
        $programCodes = $this->courses_array;
        $result = [];

        foreach ($programCodes as $code) {
            $name = \App\ProgramName::getNameByCode($code) ?? $code;
            $result[] = [
                'program_code' => $code,
                'program_name' => $name
            ];
        }

        return $result;
    }

    /**
     * Get all program codes associated with this college
     *
     * @return array
     */
    public function getProgramCodes()
    {
        return $this->courses_array;
    }

    /**
     * Check if this college has a specific program
     *
     * @param string $programCode
     * @return bool
     */
    public function hasProgram($programCode)
    {
        return in_array($programCode, $this->courses_array);
    }

    // Relationship with curricula
    public function curricula()
    {
        return $this->hasMany('App\curriculum', 'college_code', 'college_code');
    }

    // Accessor to get courses as an array
    public function getCoursesArrayAttribute()
    {
        // Get programs from programs table
        $programs = \App\Program::where('college_id', $this->id)
            ->pluck('program_code')
            ->toArray();

        return $programs;
    }

    /**
     * Get the programs for this college
     */
    public function programs()
    {
        return $this->hasMany('App\Program', 'college_id');
    }

    // Get all curriculum years for this college
    public function getCurriculumYears()
    {
        return \App\curriculum::where('college_code', $this->college_code)
            ->distinct()
            ->pluck('curriculum_year');
    }

    // Get all curricula for this college
    public function getAllCurricula()
    {
        return \App\curriculum::where('college_code', $this->college_code)->get();
    }

    /**
     * Add a program to this college
     *
     * @param string $programCode
     * @param string|null $programName
     * @return $this
     */
    public function addProgram($programCode, $programName = null)
    {
        // Check if program already exists for this college
        $existingProgram = \App\Program::where('college_id', $this->id)
            ->where('program_code', $programCode)
            ->first();

        if (!$existingProgram) {
            // Create new program in programs table
            \App\Program::create([
                'college_id' => $this->id,
                'program_code' => $programCode,
                'program_name' => $programName ?? $programCode
            ]);
        } else if ($programName && $existingProgram->program_name != $programName) {
            // Update program name if it has changed
            $existingProgram->program_name = $programName;
            $existingProgram->save();
        }

        return $this;
    }

    /**
     * Remove a program from this college
     *
     * @param string $programCode
     * @return $this
     */
    public function removeProgram($programCode)
    {
        // Find the program in programs table
        $program = \App\Program::where('college_id', $this->id)
            ->where('program_code', $programCode)
            ->first();

        if ($program) {
            // Check if there's an unassigned college
            $unassignedCollege = \App\College::firstOrCreate(
                ['college_code' => 'UNASSIGNED'],
                [
                    'college_name' => 'Unassigned College',
                    'is_active' => 1
                ]
            );

            // Move the program to the unassigned college
            $program->college_id = $unassignedCollege->id;
            $program->save();
        }

        return $this;
    }

    /**
     * Update program name
     *
     * @param string $programCode
     * @param string $programName
     * @return $this
     */
    public function updateProgramName($programCode, $programName)
    {
        // Find the program in programs table
        $program = \App\Program::where('college_id', $this->id)
            ->where('program_code', $programCode)
            ->first();

        if ($program) {
            $program->program_name = $programName;
            $program->save();
        }

        return $this;
    }
}
