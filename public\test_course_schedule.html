<!DOCTYPE html>
<html>
<head>
    <title>Test Course Schedule AJAX</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Course Schedule AJAX Testing</h1>

    <div class="test-section">
        <h3>Test 1: Check Test Section Search Endpoint</h3>
        <button onclick="testSectionSearch()">Test Section Search</button>
        <div id="section-search-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Check AJAX Endpoint</h3>
        <button onclick="testAjaxEndpoint()">Test AJAX Endpoint</button>
        <div id="ajax-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test with Filters</h3>
        <label>Program: <input type="text" id="test-program" value="BSIT" /></label>
        <label>Level: <input type="text" id="test-level" value="1st Year" /></label>
        <button onclick="testWithFilters()">Test with Filters</button>
        <div id="filter-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Direct AJAX Simulation</h3>
        <button onclick="testDirectAjax()">Test Direct AJAX</button>
        <div id="direct-ajax-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 5: Direct Database Check</h3>
        <button onclick="testDatabase()">Check Database</button>
        <div id="database-result" class="result"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000';

        function testSectionSearch() {
            $('#section-search-result').html('Testing section search endpoint...');

            $.ajax({
                type: "GET",
                url: baseUrl + "/collegeadmin/test_section_search",
                success: function(data) {
                    $('#section-search-result').html('<div class="success">✓ Section search works</div><pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#section-search-result').html('<div class="error">✗ Section search error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testAjaxEndpoint() {
            $('#ajax-result').html('Testing AJAX endpoint...');

            $.ajax({
                type: "GET",
                url: baseUrl + "/ajax/collegeadmin/course_scheduling/get_sections",
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    $('#ajax-result').html('<div class="success">✓ AJAX endpoint works</div><pre>' + data + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#ajax-result').html('<div class="error">✗ AJAX endpoint error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testWithFilters() {
            const program = $('#test-program').val();
            const level = $('#test-level').val();
            $('#filter-result').html('Testing with filters: Program=' + program + ', Level=' + level);

            $.ajax({
                type: "GET",
                url: baseUrl + "/ajax/collegeadmin/course_scheduling/get_sections",
                data: {
                    program_code: program,
                    level: level
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    $('#filter-result').html('<div class="success">✓ Filtered request works</div><pre>' + data + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#filter-result').html('<div class="error">✗ Filtered request error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testDirectAjax() {
            $('#direct-ajax-result').html('Testing direct AJAX simulation...');

            $.ajax({
                type: "GET",
                url: baseUrl + "/collegeadmin/test_direct_ajax",
                data: {
                    program_code: 'BSIT',
                    level: '1st Year'
                },
                success: function(data) {
                    $('#direct-ajax-result').html('<div class="success">✓ Direct AJAX simulation works</div><pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#direct-ajax-result').html('<div class="error">✗ Direct AJAX simulation error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }

        function testDatabase() {
            $('#database-result').html('Testing database connection...');

            $.ajax({
                type: "GET",
                url: baseUrl + "/collegeadmin/test_data",
                success: function(data) {
                    $('#database-result').html('<div class="success">✓ Database connection works</div><pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#database-result').html('<div class="error">✗ Database connection error: ' + error + '</div><pre>Status: ' + status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        }
    </script>
</body>
</html>
