<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Course Attendance Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .course-info {
            margin-bottom: 20px;
        }
        .course-info p {
            margin: 5px 0;
        }
        .instructor-info {
            margin-bottom: 20px;
        }
        .instructor-info p {
            margin: 5px 0;
        }
        .progress-bar {
            background-color: #f2f2f2;
            height: 15px;
            width: 100%;
            position: relative;
        }
        .progress-value {
            background-color: #4CAF50;
            height: 15px;
            position: absolute;
            top: 0;
            left: 0;
        }
        .progress-text {
            position: absolute;
            width: 100%;
            text-align: center;
            font-size: 10px;
            line-height: 15px;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>COURSE ATTENDANCE REPORT</h1>
        <p>{{ date('F d, Y') }}</p>
    </div>
    
    <div class="instructor-info">
        <p><strong>Instructor:</strong> {{ $instructor->name }} {{ $instructor->lastname }}</p>
        <p><strong>ID:</strong> {{ $instructor->id }}</p>
    </div>
    
    <div class="course-info">
        <p><strong>Course:</strong> {{ $course_info['course_code'] }} - {{ $course_info['course_name'] }}</p>
        <p><strong>Section:</strong> {{ $course_info['section_name'] }}</p>
        <p><strong>Total Students:</strong> {{ $attendance_data['total_students'] }}</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Present</th>
                <th>Absent</th>
                <th>Attendance Rate</th>
            </tr>
        </thead>
        <tbody>
            @foreach($attendance_data['sessions'] as $session)
            <tr>
                <td>{{ $session['date'] }}</td>
                <td>{{ $session['present'] }}</td>
                <td>{{ $session['absent'] }}</td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-value" style="width: {{ $session['percentage'] }}%;"></div>
                        <div class="progress-text">{{ $session['percentage'] }}%</div>
                    </div>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="footer">
        <p>Generated on {{ date('Y-m-d H:i:s') }}</p>
        <p>This is an official document of the Scheduling System.</p>
    </div>
</body>
</html>
