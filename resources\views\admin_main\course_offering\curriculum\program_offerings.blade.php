<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>
@extends($layout)

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-book"></i>
        {{ $program->program_name }} Offerings
        <small></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{url('/superadmin/course_offerings/curriculum')}}">Course Offering Curriculum</a></li>
        <li class="active">{{ $program->program_name }}</li>
      </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            @if(Session::has('success'))
                <div class="alert alert-success">
                    {{Session::get('success')}}
                </div>
            @endif
            @if(Session::has('error'))
                <div class="alert alert-danger">
                    {{Session::get('error')}}
                </div>
            @endif
            @if(Session::has('warning'))
                <div class="alert alert-warning">
                    {{Session::get('warning')}}
                </div>
            @endif
            @if(Session::has('info'))
                <div class="alert alert-info">
                    {{Session::get('info')}}
                </div>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            @if(empty($curriculum_years) && $sections->isEmpty())
                <div class="alert alert-info">
                    No curriculum years or sections found for this program.
                </div>
            @else
                @if(!empty($curriculum_years))
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">Curriculum Years</h3>
                        </div>
                        <div class="box-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Year</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($curriculum_years as $cy)
                                            <tr>
                                                <td>{{ $cy }}</td>
                                                <td>
                                                    <a href="{{ url('/superadmin/course_offerings/curriculum/program/curriculum', [$program->program_code, $cy]) }}" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="fa fa-eye"></i> View Details
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif

                @if(!$sections->isEmpty())
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">Active Sections</h3>
                        </div>
                        <div class="box-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Section Name</th>
                                            <th>Level</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($sections as $section)
                                            <tr>
                                                <td>{{ $section->section_name }}</td>
                                                <td>{{ $section->level }}</td>
                                                <td>
                                                    <a href="{{ url('/superadmin/course_offerings/curriculum/section', [$section->id]) }}" 
                                                       class="btn btn-sm btn-success">
                                                        <i class="fa fa-list"></i> View Offerings
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>
</section>

<script src="{{asset('plugins/select2/select2.js')}}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
@endsection
