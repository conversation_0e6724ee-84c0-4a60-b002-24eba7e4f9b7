<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Auth;
use Excel;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use App\offerings_infos_table;

class AddCurriculumController extends Controller
{
    //
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('admin');
    }

    function index()
    {
        $colleges = \App\College::where('is_active', 1)->get();

        // Get all program codes and names from program_names table
        $programNames = DB::table('program_names')->get();
        $programNameMap = [];
        foreach ($programNames as $program) {
            $programNameMap[$program->program_code] = $program->program_name;
        }

        // Get all program codes and names
        $allPrograms = [];

        // Get programs grouped by college for JavaScript
        $programsByCollege = [];
        foreach ($colleges as $college) {
            $programCodes = $college->courses_array;

            if (!empty($programCodes)) {
                $programsByCollege[$college->college_code] = [];

                foreach ($programCodes as $code) {
                    $name = $programNameMap[$code] ?? $code;

                    // Add to programs by college
                    $programsByCollege[$college->college_code][] = [
                        'program_code' => $code,
                        'program_name' => $name
                    ];

                    // Add to all programs list
                    $allPrograms[] = [
                        'program_code' => $code,
                        'program_name' => $name,
                        'college_code' => $college->college_code
                    ];
                }
            }
        }

        // Remove duplicates from all programs list
        $uniquePrograms = [];
        $seenCodes = [];

        foreach ($allPrograms as $program) {
            if (!in_array($program['program_code'], $seenCodes)) {
                $uniquePrograms[] = (object) $program; // Convert to object to match previous format
                $seenCodes[] = $program['program_code'];
            }
        }

        return view('/admin/curriculum_management/add_curriculum', compact('uniquePrograms', 'colleges', 'programsByCollege'));
    }
    function upload_backup2(Request $request)
    {
        if (Auth::user()->accesslevel == env('REG_COLLEGE')) {
            $row = 2;
            $path = Input::file('import_file')->getRealPath();
            Excel::selectSheets('Sheet1')->load($path, function ($reader) use ($row) {
                $uploaded = array();
                do {
                    $curriculum_year = $reader->getActiveSheet()->getCell('A' . $row)->getValue();
                    $period = $reader->getActiveSheet()->getCell('B' . $row)->getValue();
                    $level = $reader->getActiveSheet()->getCell('C' . $row)->getValue();
                    $program_code = $reader->getActiveSheet()->getCell('D' . $row)->getValue();
                    $course_code = $reader->getActiveSheet()->getCell('E' . $row)->getValue();
                    $course_name = $reader->getActiveSheet()->getCell('F' . $row)->getValue();
                    $lec = $reader->getActiveSheet()->getCell('G' . $row)->getValue();
                    $lab = $reader->getActiveSheet()->getCell('H' . $row)->getValue();
                    $units = $reader->getActiveSheet()->getCell('I' . $row)->getValue();

                    $uploaded[] = array(
                        'curriculum_year' => $curriculum_year,
                        'period' => $period,
                        'level' => $level,
                        'program_code' => $program_code,
                        'course_code' => $course_code,
                        'course_name' => $course_name,
                        'lec' => $lec,
                        'lab' => $lab,
                        'units' => $units
                    );
                    $row++;
                } while (strlen($reader->getActiveSheet()->getCell('B' . $row)->getValue()) > 6);

                session()->flash('upload', $uploaded);
            });

            $upload = session('upload');
            return view('reg_college.curriculum_management.upload_curriculum', compact('upload'));
        } else {
            return view('layouts.401');
        }
    }

    public function save_changes(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'curriculum_year' => 'required|array',
            'program_code' => 'required|array',
            'course_code' => 'required|array',
            'course_name' => 'required|array',
            'lec' => 'required|array',
            'lab' => 'required|array',
            'units' => 'required|array',
            'complab' => 'required|array',
            'period' => 'required|array',
            'level' => 'required|array',
            'add_to_offerings' => 'nullable|string'
        ]);

        // Process college codes
        $collegeCodeMap = [];
        foreach ($request->program_code as $index => $programCode) {
            // First try to get college from hidden_college_code
            if (isset($request->hidden_college_code[$index]) && !empty($request->hidden_college_code[$index])) {
                $collegeCodeMap[$index] = $request->hidden_college_code[$index];
            }
            // Then try to get from college_code
            elseif (isset($request->college_code[$index]) && !empty($request->college_code[$index])) {
                $collegeCodeMap[$index] = $request->college_code[$index];
            }
            // If still not found, try to find a college that has this program
            else {
                $found = false;
                $colleges = \App\College::all();
                foreach ($colleges as $college) {
                    if ($college->hasProgram($programCode)) {
                        $collegeCodeMap[$index] = $college->college_code;
                        $found = true;
                        break;
                    }
                }

                // If no college found, assign to unassigned college
                if (!$found) {
                    $unassignedCollege = \App\College::firstOrCreate(
                        ['college_code' => 'UNASSIGNED'],
                        [
                            'college_name' => 'Unassigned College',
                            'description' => $programCode,
                            'is_active' => 1
                        ]
                    );
                    $collegeCodeMap[$index] = 'UNASSIGNED';
                }
            }
        }

        // Track created curricula for course offerings
        $createdCurricula = [];

        // Now save all curriculum entries
        for ($x = 0; $x < count($request->curriculum_year); $x++) {
            $curricula = new \App\curriculum;
            $curricula->curriculum_year = $request->curriculum_year[$x];
            $curricula->program_code = $request->program_code[$x];

            // Set college code
            $curricula->college_code = $collegeCodeMap[$x];

            // Get program name from program_names table or use program code as fallback
            $programName = DB::table('program_names')
                ->where('program_code', $request->program_code[$x])
                ->first();
            $curricula->program_name = $programName ? $programName->program_name : $request->program_code[$x];

            // Save program name if it doesn't exist in program_names table
            if (!$programName) {
                DB::table('program_names')->insert([
                    'program_code' => $request->program_code[$x],
                    'program_name' => $request->program_code[$x], // Use code as name if not found
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            // Check if program needs to be added to this college
            $college = \App\College::where('college_code', $curricula->college_code)->first();
            if ($college && !$college->hasProgram($request->program_code[$x])) {
                // Add program to college description
                $college->addProgram($request->program_code[$x], $curricula->program_name);
            }

            $curricula->control_code = $request->course_code[$x];
            $curricula->course_code = $request->course_code[$x];
            $curricula->course_name = $request->course_name[$x];
            $curricula->lec = $request->lec[$x];
            $curricula->lab = $request->lab[$x];
            $curricula->units = $request->units[$x];
            $curricula->level = $request->level[$x];
            $curricula->period = $request->period[$x];
            $curricula->percent_tuition = 100;
            $curricula->is_complab = $request->complab[$x];
            $curricula->is_active = 1; // Set as active by default
            $curricula->save();

            // Add to created curricula array for course offerings
            $createdCurricula[] = [
                'id' => $curricula->id,
                'program_code' => $curricula->program_code,
                'level' => $curricula->level
            ];
        }

        // If add_to_offerings is set to 'yes', add the curricula to course offerings
        if ($request->add_to_offerings === 'yes' && !empty($createdCurricula)) {
            $this->addCurriculaToOfferings($createdCurricula);
            Session::flash('info', 'Curriculum subjects have been added to course offerings where applicable.');
        }

        Session::flash('success', 'Successfully saved curriculum data!');
        Log::info('User ' . Auth::user()->name . " added a new curriculum");

        return redirect(url('/admin/curriculum_management/curriculum'));
    }

    /**
     * Add curricula to course offerings for available sections
     *
     * @param array $curricula Array of curriculum data with id, program_code, and level
     * @return void
     */
    private function addCurriculaToOfferings(array $curricula)
    {
        $addedCount = 0;
        $totalCount = count($curricula);

        foreach ($curricula as $curriculum) {
            // Get sections for this program and level
            $sections = \App\CtrSection::where('program_code', $curriculum['program_code'])
                ->where('level', $curriculum['level'])
                ->where('is_active', 1)
                ->get();

            if ($sections->isEmpty()) {
                continue; // No sections found for this program and level
            }

            // Add curriculum to each section's offerings
            foreach ($sections as $section) {
                $added = offerings_infos_table::addToOfferings(
                    $curriculum['id'],
                    $section->section_name,
                    $curriculum['level']
                );

                if ($added) {
                    $addedCount++;
                }
            }
        }

        // Log the results
        Log::info("Added {$addedCount} curriculum subjects to course offerings out of {$totalCount} total subjects.");
    }

    function upload_backup(Request $request)
    {
        if (Auth::user()->accesslevel == env('REG_COLLEGE')) {
            $row = 9;
            $path = Input::file('import_file')->getRealPath();
            //            Excel::selectSheets('curriculum')->load($path, function($reader) use ($row) {
//                $uploaded = array();
//                do {
//                    $course_code = $reader->getActiveSheet()->getCell('A' . $row)->getValue();
//                    $course_name = $reader->getActiveSheet()->getCell('B' . $row)->getValue();
//                    $lec = $reader->getActiveSheet()->getCell('G' . $row)->getValue();
//                    $lab = $reader->getActiveSheet()->getCell('H' . $row)->getValue();
//                    $hours = $reader->getActiveSheet()->getCell('I' . $row)->getValue();
//
//                    $uploaded[] = array('course_code' => $course_code, 'course_name' => $course_name, 'lec' => $lec, 'lab' => $lab, 'hours' => $hours);
//                    $row++;
//                } while (strlen($reader->getActiveSheet()->getCell('A' . $row)->getValue()) > 1);
//
//                session()->flash('courses', $uploaded);
//            });

            Excel::selectSheets('curriculum')->load($path, function ($reader) {

                $program_code = $reader->getActiveSheet()->getCell('B1')->getValue();

                session()->flash('program_codes', $program_code);
            });

            Excel::selectSheets('curriculum')->load($path, function ($reader) {

                $program_name = $reader->getActiveSheet()->getCell('B2')->getValue();

                session()->flash('program_name', $program_name);
            });

            Excel::selectSheets('curriculum')->load($path, function ($reader) {

                $curriculum_year = $reader->getActiveSheet()->getCell('B3')->getValue();

                session()->flash('curriculum_year', $curriculum_year);
            });

            //$courses = session('courses');
            $program_codes = session('program_codes');
            $program_names = session('program_name');
            $curriculum_years = session('curriculum_year');

            return view('registrar_college.curriculum_management.upload', compact('program_codes', 'program_names', 'curriculum_years'));
            //            return view('registrar.grades.upload_grade', compact('grades', 'course', 'prof', 'request'));
        } else {
            return view('layouts.401');
        }
    }
}
