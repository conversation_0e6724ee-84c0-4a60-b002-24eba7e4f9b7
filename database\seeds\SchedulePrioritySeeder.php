<?php

use Illuminate\Database\Seeder;
use App\SchedulePriority;
use App\curriculum;

class SchedulePrioritySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Set priorities for capstone/thesis courses (highest priority)
        $this->setPriorityForCoursesContaining(['capstone', 'thesis'], 3, 'Capstone/Thesis courses');
        
        // Set priorities for laboratory courses (medium-high priority)
        $this->setPriorityForCoursesWithComplab(2, 'Laboratory courses');
        
        // Set priorities for major courses (medium priority)
        $this->setPriorityForMajorCourses(1, 'Major courses');
    }
    
    /**
     * Set priority for courses containing specific keywords in their names
     *
     * @param array $keywords
     * @param int $priorityLevel
     * @param string $description
     */
    private function setPriorityForCoursesContaining($keywords, $priorityLevel, $description)
    {
        foreach ($keywords as $keyword) {
            $courses = curriculum::where('course_name', 'like', "%{$keyword}%")
                ->where('is_active', 1)
                ->get();
                
            foreach ($courses as $course) {
                SchedulePriority::setPriority($course->id, $priorityLevel, $description);
            }
        }
    }
    
    /**
     * Set priority for courses with computer laboratory
     *
     * @param int $priorityLevel
     * @param string $description
     */
    private function setPriorityForCoursesWithComplab($priorityLevel, $description)
    {
        $courses = curriculum::where('is_complab', 1)
            ->where('is_active', 1)
            ->get();
            
        foreach ($courses as $course) {
            SchedulePriority::setPriority($course->id, $priorityLevel, $description);
        }
    }
    
    /**
     * Set priority for major courses (based on course code pattern)
     *
     * @param int $priorityLevel
     * @param string $description
     */
    private function setPriorityForMajorCourses($priorityLevel, $description)
    {
        // Get all curricula
        $courses = curriculum::where('is_active', 1)->get();
        
        foreach ($courses as $course) {
            // Check if course code contains program code (indicating it's a major course)
            if (strpos($course->course_code, $course->program_code) !== false) {
                SchedulePriority::setPriority($course->id, $priorityLevel, $description);
            }
        }
    }
}
