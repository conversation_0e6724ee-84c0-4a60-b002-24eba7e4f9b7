<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class instructors_infos extends Model
{
    protected $table = 'instructors_infos';

    protected $fillable = [
        'instructor_id',
        'college',
        'department',
        'gender',
        'street',
        'barangay',
        'municipality',
        'tel_no',
        'cell_no',
        'emerg_cont_#',
        'degree_status',
        'program_graduated',
        'employee_type'
    ];

    /**
     * Get the user that owns the instructor info.
     */
    public function user()
    {
        return $this->belongsTo('App\User', 'instructor_id');
    }
}
