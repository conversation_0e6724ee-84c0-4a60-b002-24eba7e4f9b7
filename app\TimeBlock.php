<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class TimeBlock extends Model
{
    protected $table = 'time_blocks';

    protected $fillable = [
        'day_type',
        'day',
        'start_time',
        'end_time',
        'display_text',
        'is_active'
    ];

    /**
     * Get time blocks by day type
     *
     * @param string $dayType
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByDayType($dayType)
    {
        return self::where('day_type', $dayType)
            ->where('is_active', 1)
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Get time blocks by individual day
     *
     * @param string $day
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByDay($day)
    {
        return self::where('day', $day)
            ->where('is_active', 1)
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Check if this time block overlaps with another time range
     *
     * @param string $startTime
     * @param string $endTime
     * @return bool
     */
    public function overlaps($startTime, $endTime)
    {
        $blockStart = strtotime($this->start_time);
        $blockEnd = strtotime($this->end_time);
        $checkStart = strtotime($startTime);
        $checkEnd = strtotime($endTime);

        // Check if either the start or end time falls within the block
        return ($checkStart >= $blockStart && $checkStart < $blockEnd) ||
               ($checkEnd > $blockStart && $checkEnd <= $blockEnd) ||
               ($checkStart <= $blockStart && $checkEnd >= $blockEnd);
    }
}
