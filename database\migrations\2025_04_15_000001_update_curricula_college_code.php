<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateCurriculaCollegeCode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, ensure all curricula have a college_code
        $this->populateMissingCollegeCodes();

        // Drop the foreign key constraint first
        Schema::table('curricula', function (Blueprint $table) {
            // Check if the foreign key exists before trying to drop it
            if (Schema::hasColumn('curricula', 'college_code')) {
                // Get all foreign keys
                $foreignKeys = DB::select(
                    "SELECT CONSTRAINT_NAME
                     FROM information_schema.TABLE_CONSTRAINTS
                     WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
                     AND TABLE_NAME = 'curricula'
                     AND CONSTRAINT_NAME = 'curricula_college_code_foreign'"
                );

                if (!empty($foreignKeys)) {
                    $table->dropForeign('curricula_college_code_foreign');
                }
            }
        });

        // Then, make the college_code field required
        DB::statement('ALTER TABLE curricula MODIFY college_code VARCHAR(20) NOT NULL');

        // Add the foreign key
        Schema::table('curricula', function (Blueprint $table) {
            $table->foreign('college_code')->references('college_code')->on('colleges')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('curricula', function (Blueprint $table) {
            // Drop the foreign key first
            $table->dropForeign(['college_code']);
        });

        // Make the column nullable using raw SQL
        DB::statement('ALTER TABLE curricula MODIFY college_code VARCHAR(20) NULL');

        // Re-add the foreign key
        Schema::table('curricula', function (Blueprint $table) {
            $table->foreign('college_code')->references('college_code')->on('colleges')->onDelete('set null');
        });
    }

    /**
     * Populate missing college_code values in curricula table
     */
    private function populateMissingCollegeCodes()
    {
        // Get all curricula without a college_code
        $curricula = DB::table('curricula')->whereNull('college_code')->get();

        foreach ($curricula as $curriculum) {
            // Try to find the program's college_code
            $program = DB::table('academic_programs')
                ->where('program_code', $curriculum->program_code)
                ->first();

            if ($program && $program->college_code) {
                // Update the curriculum with the program's college_code
                DB::table('curricula')
                    ->where('id', $curriculum->id)
                    ->update(['college_code' => $program->college_code]);
            } else {
                // Try to find a college that has this program in its description
                $colleges = DB::table('colleges')->get();

                foreach ($colleges as $college) {
                    $courses = explode(',', $college->description);
                    $courses = array_map('trim', $courses);

                    if (in_array($curriculum->program_code, $courses)) {
                        // Update the curriculum with the college_code
                        DB::table('curricula')
                            ->where('id', $curriculum->id)
                            ->update(['college_code' => $college->college_code]);

                        break;
                    }
                }

                // If no college found, assign to the default college
                if (DB::table('curricula')->where('id', $curriculum->id)->whereNull('college_code')->exists()) {
                    // Check if default college exists
                    $defaultCollege = DB::table('colleges')->where('college_code', 'UNASSIGNED')->first();

                    if (!$defaultCollege) {
                        // Create default college
                        $defaultCollegeId = DB::table('colleges')->insertGetId([
                            'college_code' => 'UNASSIGNED',
                            'college_name' => 'Unassigned College',
                            'description' => $curriculum->program_code,
                            'is_active' => 1,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    } else {
                        // Update description to include this program if not already included
                        $description = $defaultCollege->description;
                        $courses = explode(',', $description);
                        $courses = array_map('trim', $courses);

                        if (!in_array($curriculum->program_code, $courses)) {
                            if ($description) {
                                $description .= ', ' . $curriculum->program_code;
                            } else {
                                $description = $curriculum->program_code;
                            }

                            DB::table('colleges')
                                ->where('id', $defaultCollege->id)
                                ->update(['description' => $description]);
                        }
                    }

                    // Assign the curriculum to the default college
                    DB::table('curricula')
                        ->where('id', $curriculum->id)
                        ->update(['college_code' => 'UNASSIGNED']);
                }
            }
        }
    }
}
