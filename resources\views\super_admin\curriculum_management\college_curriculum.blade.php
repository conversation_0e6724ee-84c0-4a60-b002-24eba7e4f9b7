@extends('adminlte::page')
@section('title', 'Curriculum - ' . $college->college_name)
@section('content_header')
    <h1><i class="fa fa-book"></i> Curriculum - {{ $college->college_name }} ({{ $curriculum_year }}-{{ is_numeric($curriculum_year) ? (int)$curriculum_year + 1 : $curriculum_year }})</h1>
    <ol class="breadcrumb">
        <li><a href="/"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="#"> Curriculum Management</a></li>
        <li><a href="{{ url('/superadmin/curriculum_management/colleges') }}"> Colleges</a></li>
        <li><a href="{{ url('/superadmin/curriculum_management/college', $college->college_code) }}"> {{ $college->college_name }}</a></li>
        <li><a href="{{ url('/superadmin/curriculum_management/college/'.$college->college_code.'/curricula') }}"> Curricula</a></li>
        <li class="active"><a href="{{ url('/superadmin/curriculum_management/college/'.$college->college_code.'/curriculum/'.$curriculum_year) }}"> {{ $curriculum_year }}-{{ is_numeric($curriculum_year) ? (int)$curriculum_year + 1 : $curriculum_year }}</a></li>
    </ol>
@stop
@section('main-content')
<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-default">
                <div class="box-header">
                    <h3 class="box-title">Curriculum for {{ $college->college_name }} ({{ $curriculum_year }}-{{ is_numeric($curriculum_year) ? (int)$curriculum_year + 1 : $curriculum_year }})</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ url('/superadmin/curriculum_management/college/'.$college->college_code.'/curricula') }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back to Curricula</a>
                        <a href="{{ url('/admin/curriculum_management/add_curriculum') }}" class="btn btn-flat btn-success"><i class="fa fa-plus"></i> New Curriculum</a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Filter by Program:</label>
                                <select id="program-filter" class="form-control select2" style="width: 100%">
                                    <option value="">All Programs</option>
                                    @foreach($programs as $program)
                                        <option value="{{ $program->program_code }}">{{ $program->program_code }} - {{ $program->program_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id="curriculum-content">
                        @php
                            $levels = $curricula->pluck('level', 'level')->unique();
                            $periods = $curricula->pluck('period', 'period')->unique();
                        @endphp

                        @foreach($periods as $period)
                            @foreach($levels as $level)
                                @php
                                    $levelCurricula = $curricula->where('level', $level)->where('period', $period);
                                    if($levelCurricula->isEmpty()) continue;
                                @endphp

                                <div class="curriculum-section">
                                    <h4>{{ $period }} - {{ $level }}</h4>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th width="15%">Program</th>
                                                    <th width="15%">Subject Code</th>
                                                    <th width="40%">Description Title</th>
                                                    <th width="8%">LEC</th>
                                                    <th width="8%">LAB</th>
                                                    <th width="8%">UNITS</th>
                                                    <th width="6%">COMPLAB</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($levelCurricula as $curriculum)
                                                    <tr class="program-row" data-program="{{ $curriculum->program_code }}">
                                                        <td>{{ $curriculum->program_code }}</td>
                                                        <td>
                                                            @if(Auth::user()->accesslevel == 100)
                                                                <a onclick="editmodal('{{ $curriculum->id }}')" href="#" title="Click to Edit">{{ $curriculum->course_code }}</a>
                                                            @else
                                                                {{ $curriculum->course_code }}
                                                            @endif
                                                        </td>
                                                        <td>{{ $curriculum->course_name }}</td>
                                                        <td>{{ $curriculum->lec }}</td>
                                                        <td>{{ $curriculum->lab }}</td>
                                                        <td>{{ $curriculum->units }}</td>
                                                        <td class="text-center">
                                                            @if($curriculum->is_complab == 1)
                                                                <span class="label label-success">Yes</span>
                                                            @else
                                                                <span class="label label-default">No</span>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div id="displayeditmodal"></div>
@endsection

@section('footer-script')
<script src="{{ asset('plugins/select2/select2.js') }}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();

        // Program filter
        $('#program-filter').on('change', function() {
            var program = $(this).val();

            if (program) {
                $('.program-row').hide();
                $('.program-row[data-program="' + program + '"]').show();
            } else {
                $('.program-row').show();
            }
        });
    });

    function editmodal(curriculum_id) {
        var array = {};
        array['curriculum_id'] = curriculum_id;
        $.ajax({
            type: "GET",
            url: "/ajax/curriculum_management/edit_modal",
            data: array,
            success: function(data) {
                $('#displayeditmodal').html(data).fadeIn();
                $('#editModal').modal('toggle');
            },
            error: function() {
                toastr.error('Something Went Wrong!', 'Message!');
            }
        });
    }
</script>
@endsection
