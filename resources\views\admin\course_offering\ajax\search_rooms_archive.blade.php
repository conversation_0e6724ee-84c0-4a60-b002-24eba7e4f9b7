@if(!$rooms->isEmpty())
    @foreach($rooms as $room)
    <tr>
        <td>{{$room->room}}</td>
        <td>{{$room->building}}</td>
        <td>
            @if($room->college_code)
                @php
                    $college = \App\College::where('college_code', $room->college_code)->first();
                @endphp
                @if($college)
                    <span class='label label-primary'>{{$college->college_code}} - {{$college->college_name}}</span>
                @else
                    <span class='label label-default'>{{$room->college_code}}</span>
                @endif
            @else
                <span class='text-muted'>Not assigned</span>
            @endif
        </td>
        <td>{{$room->description}}</td>
        <td>
            @if($room->is_active == 1)
            <label class='label label-success'>Active</label>
            @else
            <label class='label label-danger'>Inactive</label>
            @endif
        </td>
        <td>
            <a href="{{url('/admin/room_management/archive',[$room->id])}}" class="btn btn-flat btn-success" title="Change to Active Status?" onclick="return confirm('Do you wish to restore the Record?')"><i class="fa fa-recycle"></i></a>
        </td>
    </tr>
    @endforeach
@else
    <tr>
        <td colspan="6" class="text-center">No rooms found matching your search criteria.</td>
    </tr>
@endif
